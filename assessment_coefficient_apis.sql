-- 考核系数分配相关API接口
-- 插入到 sys_apis 表中

-- 1. 替换考核系数接口
INSERT INTO `sys_apis` (`created_at`, `updated_at`, `deleted_at`, `path`, `description`, `api_group`, `method`) 
VALUES (NOW(), NOW(), NULL, '/assessment/coefficients/replace', '替换考核系数', 'assessment', 'POST');

-- 2. 获取考核系数接口
INSERT INTO `sys_apis` (`created_at`, `updated_at`, `deleted_at`, `path`, `description`, `api_group`, `method`) 
VALUES (NOW(), NOW(), NULL, '/assessment/coefficients/:configId', '获取考核系数', 'assessment', 'GET');

-- 3. 校验考核系数接口
INSERT INTO `sys_apis` (`created_at`, `updated_at`, `deleted_at`, `path`, `description`, `api_group`, `method`) 
VALUES (NOW(), NOW(), NULL, '/assessment/coefficients/:configId/validate', '校验考核系数总和', 'assessment', 'GET');

-- 4. 导出考核系数Excel接口
INSERT INTO `sys_apis` (`created_at`, `updated_at`, `deleted_at`, `path`, `description`, `api_group`, `method`) 
VALUES (NOW(), NOW(), NULL, '/assessment/coefficients/:configId/export', '导出考核系数Excel', 'assessment', 'GET');

-- 查询刚插入的API记录
SELECT id, path, description, api_group, method, created_at 
FROM `sys_apis` 
WHERE `api_group` = 'assessment' 
  AND `path` LIKE '/assessment/coefficients%'
ORDER BY `created_at` DESC;

-- 如果需要为特定角色分配这些API权限，可以使用以下语句
-- 假设角色ID为888（普通用户）和777（管理员）

-- 获取刚插入的API ID
SET @replace_api_id = (SELECT id FROM sys_apis WHERE path = '/assessment/coefficients/replace' AND method = 'POST');
SET @get_api_id = (SELECT id FROM sys_apis WHERE path = '/assessment/coefficients/:configId' AND method = 'GET');
SET @validate_api_id = (SELECT id FROM sys_apis WHERE path = '/assessment/coefficients/:configId/validate' AND method = 'GET');
SET @export_api_id = (SELECT id FROM sys_apis WHERE path = '/assessment/coefficients/:configId/export' AND method = 'GET');

-- 为管理员角色(777)分配所有权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES 
('p', '777', '/assessment/coefficients/replace', 'POST'),
('p', '777', '/assessment/coefficients/:configId', 'GET'),
('p', '777', '/assessment/coefficients/:configId/validate', 'GET'),
('p', '777', '/assessment/coefficients/:configId/export', 'GET');

-- 为普通用户角色(888)分配查看和导出权限（不包括替换权限）
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES 
('p', '888', '/assessment/coefficients/:configId', 'GET'),
('p', '888', '/assessment/coefficients/:configId/validate', 'GET'),
('p', '888', '/assessment/coefficients/:configId/export', 'GET');

-- 验证权限分配
SELECT cr.v0 as role_id, cr.v1 as api_path, cr.v2 as method, sa.description
FROM casbin_rule cr
LEFT JOIN sys_apis sa ON cr.v1 = sa.path AND cr.v2 = sa.method
WHERE cr.v1 LIKE '/assessment/coefficients%'
ORDER BY cr.v0, cr.v1;
