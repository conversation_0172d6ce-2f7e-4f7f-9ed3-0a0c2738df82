-- ===========================
-- 考核系统数据库表创建 - 第一步：基础表结构
-- ===========================

-- 1. 创建算法配置表
DROP TABLE IF EXISTS `algorithm_config`;
CREATE TABLE `algorithm_config` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '算法配置ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '算法名称',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '算法描述',
  `formula` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公式字符串',
  `formula_ast` json NULL COMMENT '公式AST结构',
  `variables` json NULL COMMENT '使用的变量',
  `metadata` json NULL COMMENT '元数据',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_created_by`(`created_by` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '算法配置表' ROW_FORMAT = DYNAMIC;

-- 2. 创建考核配置表
DROP TABLE IF EXISTS `assessment_config`;
CREATE TABLE `assessment_config` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '考核配置ID',
  `assessment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '考核配置名称',
  `assessment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '考核类型：月、季度、年',
  `assessment_period` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '考核周期',
  `is_archived` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否归档：0-未归档，1-已归档',
  `bonus_relation_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '奖金关联ID',
  `score_quota_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '高分配额关联ID',
  `algorithm_relation_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '算法关联ID',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_assessment_name`(`assessment_name` ASC) USING BTREE,
  INDEX `idx_assessment_type`(`assessment_type` ASC) USING BTREE,
  INDEX `idx_is_archived`(`is_archived` ASC) USING BTREE,
  INDEX `idx_algorithm_relation_id`(`algorithm_relation_id` ASC) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '考核配置表' ROW_FORMAT = DYNAMIC;

-- 3. 创建变量定义表
DROP TABLE IF EXISTS `variable_definition`;
CREATE TABLE `variable_definition` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '变量ID',
  `key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变量键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变量名称',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变量描述',
  `data_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据类型',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变量分类',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位',
  `min_value` decimal(10,2) NULL DEFAULT NULL COMMENT '最小值',
  `max_value` decimal(10,2) NULL DEFAULT NULL COMMENT '最大值',
  `default_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认值',
  `is_builtin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否内置变量',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_key`(`key` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_data_type`(`data_type` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_is_builtin`(`is_builtin` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '变量定义表' ROW_FORMAT = DYNAMIC;
