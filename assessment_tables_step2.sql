-- ===========================
-- 考核系统数据库表创建 - 第二步：扩展表结构
-- ===========================

-- 4. 创建公式模板表
DROP TABLE IF EXISTS `formula_template`;
CREATE TABLE `formula_template` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板分类',
  `formula` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公式模板',
  `parameters` json NULL COMMENT '参数定义',
  `features` json NULL COMMENT '特性标签',
  `is_builtin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否内置模板',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `usage_count` int NOT NULL DEFAULT 0 COMMENT '使用次数',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_is_builtin`(`is_builtin` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_usage_count`(`usage_count` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公式模板表' ROW_FORMAT = DYNAMIC;

-- 5. 创建考核结果表
DROP TABLE IF EXISTS `assessment_result`;
CREATE TABLE `assessment_result` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `assessment_id` bigint UNSIGNED NOT NULL COMMENT '考核配置ID',
  `project_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '项目ID',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被考核用户ID',
  `algorithm_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '使用的算法ID',
  `input_data` json NULL COMMENT '输入数据',
  `calculation_steps` json NULL COMMENT '计算步骤',
  `final_score` decimal(10,2) NULL DEFAULT NULL COMMENT '最终得分',
  `grade` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评级',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `calculated_at` datetime(3) NULL DEFAULT NULL COMMENT '计算时间',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_assessment_id`(`assessment_id` ASC) USING BTREE,
  INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_algorithm_id`(`algorithm_id` ASC) USING BTREE,
  INDEX `idx_final_score`(`final_score` ASC) USING BTREE,
  INDEX `idx_calculated_at`(`calculated_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '考核结果表' ROW_FORMAT = DYNAMIC;
