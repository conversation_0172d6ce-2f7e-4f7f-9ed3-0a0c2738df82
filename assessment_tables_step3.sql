-- ===========================
-- 考核系统数据库表创建 - 第三步：初始化数据
-- ===========================

-- 插入内置变量定义
INSERT INTO `variable_definition` (`key`, `name`, `description`, `data_type`, `category`, `unit`, `is_builtin`, `is_active`, `created_at`, `updated_at`) VALUES
('member_effort', '项目成员精力分配', '项目成员的精力分配比例数组', 'array', '项目评估', '%', 1, 1, NOW(3), NOW(3)),
('member_evaluation', '项目成员评价', '对应项目成员的评价分数数组', 'array', '项目评估', '分', 1, 1, NOW(3), NOW(3)),
('manager_effort', '项目负责人精力分配', '项目负责人的精力分配比例', 'number', '项目评估', '%', 1, 1, NOW(3), NOW(3)),
('manager_evaluation', '机构负责人对项目负责人评价', '机构负责人对项目负责人的评价分数', 'number', '项目评估', '分', 1, 1, NOW(3), NOW(3)),
('org_evaluation', '机构负责人评价', '机构负责人的整体评价分数', 'number', '项目评估', '分', 1, 1, NOW(3), NOW(3)),
('project_completion', '项目完成度', '项目完成百分比', 'number', '项目进度', '%', 1, 1, NOW(3), NOW(3)),
('quality_score', '质量评分', '项目质量评分', 'number', '质量评估', '分', 1, 1, NOW(3), NOW(3)),
('time_progress', '时间进度', '项目时间进度', 'number', '项目进度', '%', 1, 1, NOW(3), NOW(3));

-- 插入内置公式模板
INSERT INTO `formula_template` (`name`, `description`, `category`, `formula`, `parameters`, `features`, `is_builtin`, `is_active`, `usage_count`, `created_at`, `updated_at`) VALUES
('综合评价算法', '基于成员精力分配和评价的综合算法', '综合评估', '(MULTIPLY({member_effort}, {member_evaluation}) + {manager_effort} * {manager_evaluation}) * {weight1} + {org_evaluation} * {weight2}', 
'[{"key": "weight1", "label": "第一部分权重", "type": "number", "min": 0, "max": 1, "step": 0.1, "default": 0.4}, {"key": "weight2", "label": "第二部分权重", "type": "number", "min": 0, "max": 1, "step": 0.1, "default": 0.6}]', 
'["权重可调", "综合评估", "多维度"]', 1, 1, 0, NOW(3), NOW(3)),

('简单加权算法', '基于权重的简单加权计算', '基础算法', '{completion_weight} * {project_completion} + {quality_weight} * {quality_score} + {time_weight} * {time_progress}', 
'[{"key": "completion_weight", "label": "完成度权重", "type": "number", "min": 0, "max": 1, "step": 0.1, "default": 0.4}, {"key": "quality_weight", "label": "质量权重", "type": "number", "min": 0, "max": 1, "step": 0.1, "default": 0.3}, {"key": "time_weight", "label": "时间权重", "type": "number", "min": 0, "max": 1, "step": 0.1, "default": 0.3}]', 
'["简单易用", "权重可调", "基础算法"]', 1, 1, 0, NOW(3), NOW(3));

-- 插入示例算法配置
INSERT INTO `algorithm_config` (`name`, `description`, `formula`, `formula_ast`, `variables`, `metadata`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
('项目综合评价算法', '基于项目成员精力分配和评价的综合算法', '(MULTIPLY({member_effort}, {member_evaluation}) + {manager_effort} * {manager_evaluation}) * 0.4 + {org_evaluation} * 0.6', 
'{"type": "FORMULA", "expression": "综合评价公式"}', 
'["member_effort", "member_evaluation", "manager_effort", "manager_evaluation", "org_evaluation"]', 
'{"author": "系统", "version": "1.0", "description": "用于项目综合评价的算法"}', 1, 'system', NOW(3), NOW(3));

-- 为考核类型创建字典数据
INSERT INTO `sys_dictionaries` (`name`, `type`, `status`, `desc`, `created_at`, `updated_at`) 
VALUES ('考核类型', 'assessment_type', 1, '项目考核类型字典', NOW(3), NOW(3));

-- 获取刚插入的字典ID
SET @dict_id = LAST_INSERT_ID();

-- 插入考核类型字典详情
INSERT INTO `sys_dictionary_details` (`label`, `value`, `status`, `sort`, `sys_dictionary_id`, `created_at`, `updated_at`) VALUES
('月度考核', '月', 1, 1, @dict_id, NOW(3), NOW(3)),
('季度考核', '季度', 1, 2, @dict_id, NOW(3), NOW(3)),
('年度考核', '年', 1, 3, @dict_id, NOW(3), NOW(3));
