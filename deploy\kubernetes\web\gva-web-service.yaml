apiVersion: v1
kind: Service
metadata:
  name: gva-web
  annotations:
    flipped-aurora/gin-vue-admin: ui
    github: "https://github.com/flipped-aurora/gin-vue-admin.git"
    app.kubernetes.io/version: 0.0.1
  labels:
    app: gva-web
    version: gva-vue3
spec:
#  type: NodePort
  type: ClusterIP
  ports:
    - name: http
      port: 8080
      targetPort: 8080
  selector:
    app: gva-web
    version: gva-vue3
