/*
 Navicat Premium Dump SQL

 Source Server         : nasMySQL9
 Source Server Type    : MySQL
 Source Server Version : 90300 (9.3.0)
 Source Host           : ************:3306
 Source Schema         : examine

 Target Server Type    : MySQL
 Target Server Version : 90300 (9.3.0)
 File Encoding         : 65001

 Date: 12/07/2025 17:43:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS `casbin_rule`;
CREATE TABLE `casbin_rule`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `ptype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v0` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_casbin_rule`(`ptype` ASC, `v0` ASC, `v1` ASC, `v2` ASC, `v3` ASC, `v4` ASC, `v5` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 782 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of casbin_rule
-- ----------------------------
INSERT INTO `casbin_rule` VALUES (769, 'p', '888', '/api/createApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (768, 'p', '888', '/api/deleteApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (763, 'p', '888', '/api/deleteApisByIds', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (760, 'p', '888', '/api/enterSyncApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (765, 'p', '888', '/api/getAllApis', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (764, 'p', '888', '/api/getApiById', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (761, 'p', '888', '/api/getApiGroups', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (766, 'p', '888', '/api/getApiList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (759, 'p', '888', '/api/ignoreApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (762, 'p', '888', '/api/syncApi', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (767, 'p', '888', '/api/updateApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (659, 'p', '888', '/attachmentCategory/addCategory', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (658, 'p', '888', '/attachmentCategory/deleteCategory', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (660, 'p', '888', '/attachmentCategory/getCategoryList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (758, 'p', '888', '/authority/copyAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (757, 'p', '888', '/authority/createAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (756, 'p', '888', '/authority/deleteAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (754, 'p', '888', '/authority/getAuthorityList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (753, 'p', '888', '/authority/setDataAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (755, 'p', '888', '/authority/updateAuthority', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (683, 'p', '888', '/authorityBtn/canRemoveAuthorityBtn', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (684, 'p', '888', '/authorityBtn/getAuthorityBtn', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (685, 'p', '888', '/authorityBtn/setAuthorityBtn', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (706, 'p', '888', '/autoCode/addFunc', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (714, 'p', '888', '/autoCode/createPackage', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (722, 'p', '888', '/autoCode/createTemp', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (711, 'p', '888', '/autoCode/delPackage', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (707, 'p', '888', '/autoCode/delSysHistory', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (720, 'p', '888', '/autoCode/getColumn', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (724, 'p', '888', '/autoCode/getDB', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (710, 'p', '888', '/autoCode/getMeta', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (712, 'p', '888', '/autoCode/getPackage', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (708, 'p', '888', '/autoCode/getSysHistory', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (723, 'p', '888', '/autoCode/getTables', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (713, 'p', '888', '/autoCode/getTemplates', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (719, 'p', '888', '/autoCode/installPlugin', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (717, 'p', '888', '/autoCode/mcp', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (715, 'p', '888', '/autoCode/mcpList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (716, 'p', '888', '/autoCode/mcpTest', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (721, 'p', '888', '/autoCode/preview', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (718, 'p', '888', '/autoCode/pubPlug', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (709, 'p', '888', '/autoCode/rollback', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (751, 'p', '888', '/casbin/getPolicyPathByAuthorityId', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (752, 'p', '888', '/casbin/updateCasbin', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (727, 'p', '888', '/customer/customer', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (726, 'p', '888', '/customer/customer', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (728, 'p', '888', '/customer/customer', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (729, 'p', '888', '/customer/customer', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (725, 'p', '888', '/customer/customerList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (687, 'p', '888', '/email/emailTest', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (686, 'p', '888', '/email/sendEmail', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (740, 'p', '888', '/fileUploadAndDownload/breakpointContinue', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (739, 'p', '888', '/fileUploadAndDownload/breakpointContinueFinish', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (736, 'p', '888', '/fileUploadAndDownload/deleteFile', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (735, 'p', '888', '/fileUploadAndDownload/editFileName', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (741, 'p', '888', '/fileUploadAndDownload/findFile', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (734, 'p', '888', '/fileUploadAndDownload/getFileList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (733, 'p', '888', '/fileUploadAndDownload/importURL', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (738, 'p', '888', '/fileUploadAndDownload/removeChunk', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (737, 'p', '888', '/fileUploadAndDownload/upload', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (673, 'p', '888', '/info/createInfo', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (672, 'p', '888', '/info/deleteInfo', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (671, 'p', '888', '/info/deleteInfoByIds', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (669, 'p', '888', '/info/findInfo', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (668, 'p', '888', '/info/getInfoList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (670, 'p', '888', '/info/updateInfo', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (781, 'p', '888', '/jwt/jsonInBlacklist', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (750, 'p', '888', '/menu/addBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (742, 'p', '888', '/menu/addMenuAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (748, 'p', '888', '/menu/deleteBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (746, 'p', '888', '/menu/getBaseMenuById', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (744, 'p', '888', '/menu/getBaseMenuTree', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (749, 'p', '888', '/menu/getMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (743, 'p', '888', '/menu/getMenuAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (745, 'p', '888', '/menu/getMenuList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (747, 'p', '888', '/menu/updateBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (643, 'p', '888', '/org/changeLoginOrg', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (655, 'p', '888', '/org/createOrganizational', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (653, 'p', '888', '/org/deleteOrganizational', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (646, 'p', '888', '/org/getNodeAdminAuthorityList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (651, 'p', '888', '/org/getOrganizationalMember', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (654, 'p', '888', '/org/getOrganizationalTree', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (647, 'p', '888', '/org/getSysAuthorityList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (650, 'p', '888', '/org/getUser', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (642, 'p', '888', '/org/getUserLoginList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (649, 'p', '888', '/org/joinOrganizationalMember', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (648, 'p', '888', '/org/removeOrganizationalMember', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (656, 'p', '888', '/org/setAuthorityLevel', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (641, 'p', '888', '/org/setNodeAdmin', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (644, 'p', '888', '/org/setOrganizationalMemberAuthority', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (657, 'p', '888', '/org/syncAuthority', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (645, 'p', '888', '/org/test', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (652, 'p', '888', '/org/updateOrganizational', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (640, 'p', '888', '/projectInfo/createProjectInfo', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (639, 'p', '888', '/projectInfo/deleteProjectInfo', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (638, 'p', '888', '/projectInfo/deleteProjectInfoByIds', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (636, 'p', '888', '/projectInfo/findProjectInfo', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (635, 'p', '888', '/projectInfo/getProjectInfoList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (637, 'p', '888', '/projectInfo/updateProjectInfo', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (689, 'p', '888', '/simpleUploader/checkFileMd5', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (688, 'p', '888', '/simpleUploader/mergeFileMd5', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (690, 'p', '888', '/simpleUploader/upload', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (700, 'p', '888', '/sysDictionary/createSysDictionary', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (699, 'p', '888', '/sysDictionary/deleteSysDictionary', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (697, 'p', '888', '/sysDictionary/findSysDictionary', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (696, 'p', '888', '/sysDictionary/getSysDictionaryList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (698, 'p', '888', '/sysDictionary/updateSysDictionary', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (704, 'p', '888', '/sysDictionaryDetail/createSysDictionaryDetail', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (703, 'p', '888', '/sysDictionaryDetail/deleteSysDictionaryDetail', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (702, 'p', '888', '/sysDictionaryDetail/findSysDictionaryDetail', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (701, 'p', '888', '/sysDictionaryDetail/getSysDictionaryDetailList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (705, 'p', '888', '/sysDictionaryDetail/updateSysDictionaryDetail', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (682, 'p', '888', '/sysExportTemplate/createSysExportTemplate', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (681, 'p', '888', '/sysExportTemplate/deleteSysExportTemplate', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (680, 'p', '888', '/sysExportTemplate/deleteSysExportTemplateByIds', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (676, 'p', '888', '/sysExportTemplate/exportExcel', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (675, 'p', '888', '/sysExportTemplate/exportTemplate', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (678, 'p', '888', '/sysExportTemplate/findSysExportTemplate', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (677, 'p', '888', '/sysExportTemplate/getSysExportTemplateList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (674, 'p', '888', '/sysExportTemplate/importExcel', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (679, 'p', '888', '/sysExportTemplate/updateSysExportTemplate', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (695, 'p', '888', '/sysOperationRecord/createSysOperationRecord', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (692, 'p', '888', '/sysOperationRecord/deleteSysOperationRecord', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (691, 'p', '888', '/sysOperationRecord/deleteSysOperationRecordByIds', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (694, 'p', '888', '/sysOperationRecord/findSysOperationRecord', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (693, 'p', '888', '/sysOperationRecord/getSysOperationRecordList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (667, 'p', '888', '/sysParams/createSysParams', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (666, 'p', '888', '/sysParams/deleteSysParams', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (665, 'p', '888', '/sysParams/deleteSysParamsByIds', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (663, 'p', '888', '/sysParams/findSysParams', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (661, 'p', '888', '/sysParams/getSysParam', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (662, 'p', '888', '/sysParams/getSysParamsList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (664, 'p', '888', '/sysParams/updateSysParams', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (732, 'p', '888', '/system/getServerInfo', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (731, 'p', '888', '/system/getSystemConfig', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (730, 'p', '888', '/system/setSystemConfig', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (779, 'p', '888', '/user/admin_register', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (773, 'p', '888', '/user/changePassword', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (780, 'p', '888', '/user/deleteUser', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (775, 'p', '888', '/user/getUserInfo', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (778, 'p', '888', '/user/getUserList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (771, 'p', '888', '/user/resetPassword', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (776, 'p', '888', '/user/setSelfInfo', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (770, 'p', '888', '/user/setSelfSetting', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (774, 'p', '888', '/user/setUserAuthorities', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (772, 'p', '888', '/user/setUserAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (777, 'p', '888', '/user/setUserInfo', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (128, 'p', '8881', '/api/createApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (131, 'p', '8881', '/api/deleteApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (133, 'p', '8881', '/api/getAllApis', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (130, 'p', '8881', '/api/getApiById', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (129, 'p', '8881', '/api/getApiList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (132, 'p', '8881', '/api/updateApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (134, 'p', '8881', '/authority/createAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (135, 'p', '8881', '/authority/deleteAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (136, 'p', '8881', '/authority/getAuthorityList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (137, 'p', '8881', '/authority/setDataAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (156, 'p', '8881', '/casbin/getPolicyPathByAuthorityId', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (155, 'p', '8881', '/casbin/updateCasbin', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (162, 'p', '8881', '/customer/customer', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (163, 'p', '8881', '/customer/customer', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (160, 'p', '8881', '/customer/customer', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (161, 'p', '8881', '/customer/customer', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (164, 'p', '8881', '/customer/customerList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (152, 'p', '8881', '/fileUploadAndDownload/deleteFile', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (153, 'p', '8881', '/fileUploadAndDownload/editFileName', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (151, 'p', '8881', '/fileUploadAndDownload/getFileList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (154, 'p', '8881', '/fileUploadAndDownload/importURL', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (150, 'p', '8881', '/fileUploadAndDownload/upload', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (157, 'p', '8881', '/jwt/jsonInBlacklist', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (140, 'p', '8881', '/menu/addBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (142, 'p', '8881', '/menu/addMenuAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (144, 'p', '8881', '/menu/deleteBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (146, 'p', '8881', '/menu/getBaseMenuById', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (141, 'p', '8881', '/menu/getBaseMenuTree', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (138, 'p', '8881', '/menu/getMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (143, 'p', '8881', '/menu/getMenuAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (139, 'p', '8881', '/menu/getMenuList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (145, 'p', '8881', '/menu/updateBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (158, 'p', '8881', '/system/getSystemConfig', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (159, 'p', '8881', '/system/setSystemConfig', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (127, 'p', '8881', '/user/admin_register', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (147, 'p', '8881', '/user/changePassword', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (165, 'p', '8881', '/user/getUserInfo', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (148, 'p', '8881', '/user/getUserList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (149, 'p', '8881', '/user/setUserAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (167, 'p', '9528', '/api/createApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (170, 'p', '9528', '/api/deleteApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (172, 'p', '9528', '/api/getAllApis', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (169, 'p', '9528', '/api/getApiById', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (168, 'p', '9528', '/api/getApiList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (171, 'p', '9528', '/api/updateApi', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (173, 'p', '9528', '/authority/createAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (174, 'p', '9528', '/authority/deleteAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (175, 'p', '9528', '/authority/getAuthorityList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (176, 'p', '9528', '/authority/setDataAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (204, 'p', '9528', '/autoCode/createTemp', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (195, 'p', '9528', '/casbin/getPolicyPathByAuthorityId', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (194, 'p', '9528', '/casbin/updateCasbin', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (202, 'p', '9528', '/customer/customer', 'DELETE', '', '', '');
INSERT INTO `casbin_rule` VALUES (200, 'p', '9528', '/customer/customer', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (201, 'p', '9528', '/customer/customer', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (199, 'p', '9528', '/customer/customer', 'PUT', '', '', '');
INSERT INTO `casbin_rule` VALUES (203, 'p', '9528', '/customer/customerList', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (191, 'p', '9528', '/fileUploadAndDownload/deleteFile', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (192, 'p', '9528', '/fileUploadAndDownload/editFileName', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (190, 'p', '9528', '/fileUploadAndDownload/getFileList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (193, 'p', '9528', '/fileUploadAndDownload/importURL', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (189, 'p', '9528', '/fileUploadAndDownload/upload', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (196, 'p', '9528', '/jwt/jsonInBlacklist', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (179, 'p', '9528', '/menu/addBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (181, 'p', '9528', '/menu/addMenuAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (183, 'p', '9528', '/menu/deleteBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (185, 'p', '9528', '/menu/getBaseMenuById', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (180, 'p', '9528', '/menu/getBaseMenuTree', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (177, 'p', '9528', '/menu/getMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (182, 'p', '9528', '/menu/getMenuAuthority', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (178, 'p', '9528', '/menu/getMenuList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (184, 'p', '9528', '/menu/updateBaseMenu', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (197, 'p', '9528', '/system/getSystemConfig', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (198, 'p', '9528', '/system/setSystemConfig', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (166, 'p', '9528', '/user/admin_register', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (186, 'p', '9528', '/user/changePassword', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (205, 'p', '9528', '/user/getUserInfo', 'GET', '', '', '');
INSERT INTO `casbin_rule` VALUES (187, 'p', '9528', '/user/getUserList', 'POST', '', '', '');
INSERT INTO `casbin_rule` VALUES (188, 'p', '9528', '/user/setUserAuthority', 'POST', '', '', '');

-- ----------------------------
-- Table structure for exa_attachment_category
-- ----------------------------
DROP TABLE IF EXISTS `exa_attachment_category`;
CREATE TABLE `exa_attachment_category`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `pid` bigint NULL DEFAULT 0 COMMENT '父节点ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exa_attachment_category_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of exa_attachment_category
-- ----------------------------

-- ----------------------------
-- Table structure for exa_customers
-- ----------------------------
DROP TABLE IF EXISTS `exa_customers`;
CREATE TABLE `exa_customers`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `customer_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名',
  `customer_phone_data` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户手机号',
  `sys_user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '管理ID',
  `sys_user_authority_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '管理角色ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exa_customers_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of exa_customers
-- ----------------------------

-- ----------------------------
-- Table structure for exa_file_chunks
-- ----------------------------
DROP TABLE IF EXISTS `exa_file_chunks`;
CREATE TABLE `exa_file_chunks`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `exa_file_id` bigint UNSIGNED NULL DEFAULT NULL,
  `file_chunk_number` bigint NULL DEFAULT NULL,
  `file_chunk_path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exa_file_chunks_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of exa_file_chunks
-- ----------------------------

-- ----------------------------
-- Table structure for exa_file_upload_and_downloads
-- ----------------------------
DROP TABLE IF EXISTS `exa_file_upload_and_downloads`;
CREATE TABLE `exa_file_upload_and_downloads`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `class_id` bigint NULL DEFAULT 0 COMMENT '分类id',
  `url` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
  `tag` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件标签',
  `key` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exa_file_upload_and_downloads_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of exa_file_upload_and_downloads
-- ----------------------------
INSERT INTO `exa_file_upload_and_downloads` VALUES (1, '2025-07-12 08:08:44.886', '2025-07-12 08:08:44.886', NULL, '10.png', 0, 'https://qmplusimg.henrongyi.top/gvalogo.png', 'png', '158787308910.png');
INSERT INTO `exa_file_upload_and_downloads` VALUES (2, '2025-07-12 08:08:44.886', '2025-07-12 08:08:44.886', NULL, 'logo.png', 0, 'https://qmplusimg.henrongyi.top/1576554439myAvatar.png', 'png', '1587973709logo.png');

-- ----------------------------
-- Table structure for exa_files
-- ----------------------------
DROP TABLE IF EXISTS `exa_files`;
CREATE TABLE `exa_files`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `file_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_md5` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `file_path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `chunk_total` bigint NULL DEFAULT NULL,
  `is_finish` tinyint(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exa_files_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of exa_files
-- ----------------------------

-- ----------------------------
-- Table structure for gva_announcements_info
-- ----------------------------
DROP TABLE IF EXISTS `gva_announcements_info`;
CREATE TABLE `gva_announcements_info`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `title` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公告标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公告内容',
  `user_id` bigint NULL DEFAULT NULL COMMENT '发布者',
  `attachments` json NULL COMMENT '相关附件',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_gva_announcements_info_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gva_announcements_info
-- ----------------------------

-- ----------------------------
-- Table structure for jwt_blacklists
-- ----------------------------
DROP TABLE IF EXISTS `jwt_blacklists`;
CREATE TABLE `jwt_blacklists`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `jwt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'jwt',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_jwt_blacklists_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of jwt_blacklists
-- ----------------------------
INSERT INTO `jwt_blacklists` VALUES (1, '2025-07-12 08:29:13.165', '2025-07-12 08:29:13.165', NULL, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZjg1MDNjYjItMzBjZS00NWRkLWI1MmMtYWE0MWQ5MTQzOWI0IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Ik1yLuWlh-a3vCIsIkF1dGhvcml0eUlkIjo4ODgsIk9yZ0lkIjoxLCJCdWZmZXJUaW1lIjo4NjQwMCwiaXNzIjoicW1QbHVzIiwiYXVkIjpbIkdWQSJdLCJleHAiOjE3NTI4ODQ2OTcsIm5iZiI6MTc1MjI3OTg5N30.BwsItCz06xZwkYEFoebW9m9l-6mdy4AOY__Mq8wrx-4');
INSERT INTO `jwt_blacklists` VALUES (2, '2025-07-12 09:17:41.963', '2025-07-12 09:17:41.963', NULL, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZjg1MDNjYjItMzBjZS00NWRkLWI1MmMtYWE0MWQ5MTQzOWI0IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6IiIsIkF1dGhvcml0eUlkIjo4ODgsIk9yZ0lkIjoxLCJCdWZmZXJUaW1lIjo4NjQwMCwiaXNzIjoicW1QbHVzIiwiYXVkIjpbIkdWQSJdLCJleHAiOjE3NTI4ODc2MDgsIm5iZiI6MTc1MjI4MjgwOH0.vbO-F_cjByQYm7PfQ6NsK_Q3mcRhncYpGX2ASe42fB0');

-- ----------------------------
-- Table structure for org_authority_permission
-- ----------------------------
DROP TABLE IF EXISTS `org_authority_permission`;
CREATE TABLE `org_authority_permission`  (
  `authority_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '角色ID',
  `parent_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '父节点',
  `level` bigint NULL DEFAULT 0 COMMENT '权限等级'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of org_authority_permission
-- ----------------------------
INSERT INTO `org_authority_permission` VALUES (888, 0, 1);
INSERT INTO `org_authority_permission` VALUES (8881, 888, 0);
INSERT INTO `org_authority_permission` VALUES (9528, 0, 0);

-- ----------------------------
-- Table structure for org_org
-- ----------------------------
DROP TABLE IF EXISTS `org_org`;
CREATE TABLE `org_org`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `type` bigint NULL DEFAULT NULL COMMENT '类型',
  `parent_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '父节点',
  `sort` bigint NULL DEFAULT NULL COMMENT '排序',
  `status` bigint NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_org_org_deleted_at`(`deleted_at` ASC) USING BTREE,
  INDEX `parent_id`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of org_org
-- ----------------------------
INSERT INTO `org_org` VALUES (1, '2025-07-12 08:26:52.746', '2025-07-12 08:26:52.746', NULL, '科创公司', 1, 0, 0, 1);
INSERT INTO `org_org` VALUES (2, '2025-07-12 08:27:06.961', '2025-07-12 08:27:06.961', NULL, '计划财务部', 2, 1, 0, 1);
INSERT INTO `org_org` VALUES (3, '2025-07-12 09:19:55.955', '2025-07-12 09:19:55.955', '2025-07-12 09:20:38.866', '科创公司', 1, 0, 0, 1);
INSERT INTO `org_org` VALUES (4, '2025-07-12 09:20:04.617', '2025-07-12 09:20:04.617', '2025-07-12 09:20:41.220', '科创公司', 1, 0, 0, 1);
INSERT INTO `org_org` VALUES (5, '2025-02-12 09:06:00.000', '2025-05-06 08:29:20.000', NULL, '数字能源智联技术研究所', 2, 1, 2, 1);
INSERT INTO `org_org` VALUES (6, '2025-02-12 09:06:00.000', '2025-06-09 09:10:06.000', NULL, '友好并网与新型配网技术研究所', 2, 1, 3, 1);
INSERT INTO `org_org` VALUES (7, '2025-02-14 15:26:39.000', '2025-05-28 17:22:19.000', NULL, '科技创新部', 2, 1, 1, 1);
INSERT INTO `org_org` VALUES (8, '2025-02-21 07:58:08.000', '2025-05-06 08:42:45.000', NULL, '氢能制备及耦合技术研究所', 2, 1, 4, 1);
INSERT INTO `org_org` VALUES (9, '2025-02-21 07:58:51.000', '2025-06-11 19:40:44.000', NULL, '风能利用先进技术研究所', 2, 1, 5, 1);
INSERT INTO `org_org` VALUES (10, '2025-02-21 07:59:08.000', '2025-06-02 10:54:50.000', NULL, '光能转化先进技术研究所', 2, 1, 6, 1);
INSERT INTO `org_org` VALUES (11, '2025-02-21 07:59:30.000', '2025-05-29 14:02:54.000', NULL, '新型储能技术研究所', 2, 1, 7, 1);
INSERT INTO `org_org` VALUES (12, '2025-02-24 01:10:46.000', '2025-05-29 14:03:18.000', NULL, '碳中和研究中心', 2, 1, 8, 1);
INSERT INTO `org_org` VALUES (13, '2025-05-13 04:01:32.000', '2025-06-11 19:43:01.000', NULL, '综合管理部（人力资源部）', 2, 1, 0, 1);
INSERT INTO `org_org` VALUES (14, '2025-05-29 13:55:23.000', '2025-05-29 14:04:36.000', NULL, '党建工作部', 2, 1, 10, 1);
INSERT INTO `org_org` VALUES (15, '2025-05-29 13:55:59.000', '2025-05-29 14:04:45.000', NULL, '纪委办公室 （审计部、法务风控部）', 2, 1, 11, 1);
INSERT INTO `org_org` VALUES (16, '2025-06-11 10:08:52.000', '2025-06-11 10:12:54.000', NULL, '临时部门', 2, 1, 15, 1);
INSERT INTO `org_org` VALUES (20, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '研发部门', 2, 5, 1, 1);
INSERT INTO `org_org` VALUES (21, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '市场部门', 2, 5, 2, 1);
INSERT INTO `org_org` VALUES (22, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '测试部门', 2, 5, 3, 1);
INSERT INTO `org_org` VALUES (23, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '财务部门', 2, 5, 4, 1);
INSERT INTO `org_org` VALUES (24, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '运维部门', 2, 5, 5, 1);
INSERT INTO `org_org` VALUES (25, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '市场部门', 2, 6, 1, 1);
INSERT INTO `org_org` VALUES (26, '2025-02-12 09:06:00.000', '2025-02-12 09:06:00.000', '2025-07-12 09:35:24.000', '财务部门', 2, 6, 2, 1);
INSERT INTO `org_org` VALUES (27, '2025-06-03 10:06:50.000', '2025-06-03 10:06:50.000', NULL, '共享商务中心', 2, 7, 1, 1);

-- ----------------------------
-- Table structure for org_organizational_user
-- ----------------------------
DROP TABLE IF EXISTS `org_organizational_user`;
CREATE TABLE `org_organizational_user`  (
  `org_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '组织ID',
  `user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '用户ID',
  `authority_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '角色ID',
  `is_admin` tinyint(1) NULL DEFAULT NULL COMMENT '是否管理员'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of org_organizational_user
-- ----------------------------
INSERT INTO `org_organizational_user` VALUES (1, 1, 888, 1);
INSERT INTO `org_organizational_user` VALUES (1, 2, NULL, 0);
INSERT INTO `org_organizational_user` VALUES (7, 6, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 22, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 23, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 24, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 25, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 26, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 27, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 28, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 29, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 30, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 31, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 32, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 33, 888, 0);
INSERT INTO `org_organizational_user` VALUES (8, 34, 888, 0);
INSERT INTO `org_organizational_user` VALUES (8, 35, 888, 0);
INSERT INTO `org_organizational_user` VALUES (6, 36, 888, 0);
INSERT INTO `org_organizational_user` VALUES (6, 37, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 38, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 39, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 40, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 41, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 42, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 43, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 44, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 45, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 46, 888, 0);
INSERT INTO `org_organizational_user` VALUES (9, 47, 888, 0);
INSERT INTO `org_organizational_user` VALUES (10, 48, 888, 0);
INSERT INTO `org_organizational_user` VALUES (12, 49, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 50, 888, 0);
INSERT INTO `org_organizational_user` VALUES (10, 51, 888, 0);
INSERT INTO `org_organizational_user` VALUES (11, 52, 888, 0);
INSERT INTO `org_organizational_user` VALUES (8, 53, 888, 0);
INSERT INTO `org_organizational_user` VALUES (10, 54, 888, 0);
INSERT INTO `org_organizational_user` VALUES (12, 55, 888, 0);
INSERT INTO `org_organizational_user` VALUES (9, 56, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 57, 888, 0);
INSERT INTO `org_organizational_user` VALUES (5, 58, 888, 0);
INSERT INTO `org_organizational_user` VALUES (9, 59, 888, 0);
INSERT INTO `org_organizational_user` VALUES (8, 60, 888, 0);
INSERT INTO `org_organizational_user` VALUES (6, 61, 888, 0);
INSERT INTO `org_organizational_user` VALUES (2, 63, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 64, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 65, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 66, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 67, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 68, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 69, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 70, 888, 0);
INSERT INTO `org_organizational_user` VALUES (13, 71, 888, 0);
INSERT INTO `org_organizational_user` VALUES (7, 72, 888, 0);
INSERT INTO `org_organizational_user` VALUES (2, 73, 888, 0);
INSERT INTO `org_organizational_user` VALUES (2, 74, 888, 0);
INSERT INTO `org_organizational_user` VALUES (14, 75, 888, 0);
INSERT INTO `org_organizational_user` VALUES (14, 76, 888, 0);
INSERT INTO `org_organizational_user` VALUES (15, 77, 888, 0);
INSERT INTO `org_organizational_user` VALUES (15, 78, 888, 0);
INSERT INTO `org_organizational_user` VALUES (2, 79, 888, 0);
INSERT INTO `org_organizational_user` VALUES (6, 80, 888, 0);
INSERT INTO `org_organizational_user` VALUES (16, 81, 888, 0);
INSERT INTO `org_organizational_user` VALUES (16, 82, 888, 0);
INSERT INTO `org_organizational_user` VALUES (16, 83, 888, 0);
INSERT INTO `org_organizational_user` VALUES (16, 84, 888, 0);
INSERT INTO `org_organizational_user` VALUES (16, 85, 888, 0);
INSERT INTO `org_organizational_user` VALUES (16, 86, 888, 0);

-- ----------------------------
-- Table structure for project_info
-- ----------------------------
DROP TABLE IF EXISTS `project_info`;
CREATE TABLE `project_info`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `department_id` mediumint NULL DEFAULT NULL COMMENT '所属部门ID',
  `manager_id` mediumint NULL DEFAULT NULL COMMENT '项目负责人ID',
  `type` tinyint(1) NOT NULL COMMENT '项目类型：1-自研项目，2-承揽项目',
  `members` json NULL COMMENT '项目成员',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_department_id`(`department_id` ASC) USING BTREE,
  INDEX `idx_manager_id`(`manager_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of project_info
-- ----------------------------

-- ----------------------------
-- Table structure for sys_apis
-- ----------------------------
DROP TABLE IF EXISTS `sys_apis`;
CREATE TABLE `sys_apis`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'api路径',
  `description` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'api中文描述',
  `api_group` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'api组',
  `method` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'POST' COMMENT '方法',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_apis_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 148 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_apis
-- ----------------------------
INSERT INTO `sys_apis` VALUES (1, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/jwt/jsonInBlacklist', 'jwt加入黑名单(退出，必选)', 'jwt', 'POST');
INSERT INTO `sys_apis` VALUES (2, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/deleteUser', '删除用户', '系统用户', 'DELETE');
INSERT INTO `sys_apis` VALUES (3, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/admin_register', '用户注册', '系统用户', 'POST');
INSERT INTO `sys_apis` VALUES (4, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/getUserList', '获取用户列表', '系统用户', 'POST');
INSERT INTO `sys_apis` VALUES (5, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/setUserInfo', '设置用户信息', '系统用户', 'PUT');
INSERT INTO `sys_apis` VALUES (6, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/setSelfInfo', '设置自身信息(必选)', '系统用户', 'PUT');
INSERT INTO `sys_apis` VALUES (7, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/getUserInfo', '获取自身信息(必选)', '系统用户', 'GET');
INSERT INTO `sys_apis` VALUES (8, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/setUserAuthorities', '设置权限组', '系统用户', 'POST');
INSERT INTO `sys_apis` VALUES (9, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/changePassword', '修改密码（建议选择)', '系统用户', 'POST');
INSERT INTO `sys_apis` VALUES (10, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/setUserAuthority', '修改用户角色(必选)', '系统用户', 'POST');
INSERT INTO `sys_apis` VALUES (11, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/resetPassword', '重置用户密码', '系统用户', 'POST');
INSERT INTO `sys_apis` VALUES (12, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/user/setSelfSetting', '用户界面配置', '系统用户', 'PUT');
INSERT INTO `sys_apis` VALUES (13, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/createApi', '创建api', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (14, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/deleteApi', '删除Api', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (15, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/updateApi', '更新Api', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (16, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/getApiList', '获取api列表', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (17, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/getAllApis', '获取所有api', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (18, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/getApiById', '获取api详细信息', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (19, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/deleteApisByIds', '批量删除api', 'api', 'DELETE');
INSERT INTO `sys_apis` VALUES (20, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/syncApi', '获取待同步API', 'api', 'GET');
INSERT INTO `sys_apis` VALUES (21, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/getApiGroups', '获取路由组', 'api', 'GET');
INSERT INTO `sys_apis` VALUES (22, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/enterSyncApi', '确认同步API', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (23, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/api/ignoreApi', '忽略API', 'api', 'POST');
INSERT INTO `sys_apis` VALUES (24, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authority/copyAuthority', '拷贝角色', '角色', 'POST');
INSERT INTO `sys_apis` VALUES (25, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authority/createAuthority', '创建角色', '角色', 'POST');
INSERT INTO `sys_apis` VALUES (26, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authority/deleteAuthority', '删除角色', '角色', 'POST');
INSERT INTO `sys_apis` VALUES (27, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authority/updateAuthority', '更新角色信息', '角色', 'PUT');
INSERT INTO `sys_apis` VALUES (28, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authority/getAuthorityList', '获取角色列表', '角色', 'POST');
INSERT INTO `sys_apis` VALUES (29, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authority/setDataAuthority', '设置角色资源权限', '角色', 'POST');
INSERT INTO `sys_apis` VALUES (30, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/casbin/updateCasbin', '更改角色api权限', 'casbin', 'POST');
INSERT INTO `sys_apis` VALUES (31, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/casbin/getPolicyPathByAuthorityId', '获取权限列表', 'casbin', 'POST');
INSERT INTO `sys_apis` VALUES (32, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/addBaseMenu', '新增菜单', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (33, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/getMenu', '获取菜单树(必选)', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (34, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/deleteBaseMenu', '删除菜单', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (35, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/updateBaseMenu', '更新菜单', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (36, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/getBaseMenuById', '根据id获取菜单', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (37, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/getMenuList', '分页获取基础menu列表', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (38, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/getBaseMenuTree', '获取用户动态路由', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (39, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/getMenuAuthority', '获取指定角色menu', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (40, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/menu/addMenuAuthority', '增加menu和角色关联关系', '菜单', 'POST');
INSERT INTO `sys_apis` VALUES (41, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/findFile', '寻找目标文件（秒传）', '分片上传', 'GET');
INSERT INTO `sys_apis` VALUES (42, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/breakpointContinue', '断点续传', '分片上传', 'POST');
INSERT INTO `sys_apis` VALUES (43, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/breakpointContinueFinish', '断点续传完成', '分片上传', 'POST');
INSERT INTO `sys_apis` VALUES (44, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/removeChunk', '上传完成移除文件', '分片上传', 'POST');
INSERT INTO `sys_apis` VALUES (45, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/upload', '文件上传（建议选择）', '文件上传与下载', 'POST');
INSERT INTO `sys_apis` VALUES (46, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/deleteFile', '删除文件', '文件上传与下载', 'POST');
INSERT INTO `sys_apis` VALUES (47, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/editFileName', '文件名或者备注编辑', '文件上传与下载', 'POST');
INSERT INTO `sys_apis` VALUES (48, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/getFileList', '获取上传文件列表', '文件上传与下载', 'POST');
INSERT INTO `sys_apis` VALUES (49, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/fileUploadAndDownload/importURL', '导入URL', '文件上传与下载', 'POST');
INSERT INTO `sys_apis` VALUES (50, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/system/getServerInfo', '获取服务器信息', '系统服务', 'POST');
INSERT INTO `sys_apis` VALUES (51, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/system/getSystemConfig', '获取配置文件内容', '系统服务', 'POST');
INSERT INTO `sys_apis` VALUES (52, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/system/setSystemConfig', '设置配置文件内容', '系统服务', 'POST');
INSERT INTO `sys_apis` VALUES (53, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/customer/customer', '更新客户', '客户', 'PUT');
INSERT INTO `sys_apis` VALUES (54, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/customer/customer', '创建客户', '客户', 'POST');
INSERT INTO `sys_apis` VALUES (55, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/customer/customer', '删除客户', '客户', 'DELETE');
INSERT INTO `sys_apis` VALUES (56, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/customer/customer', '获取单一客户', '客户', 'GET');
INSERT INTO `sys_apis` VALUES (57, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/customer/customerList', '获取客户列表', '客户', 'GET');
INSERT INTO `sys_apis` VALUES (58, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getDB', '获取所有数据库', '代码生成器', 'GET');
INSERT INTO `sys_apis` VALUES (59, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getTables', '获取数据库表', '代码生成器', 'GET');
INSERT INTO `sys_apis` VALUES (60, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/createTemp', '自动化代码', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (61, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/preview', '预览自动化代码', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (62, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getColumn', '获取所选table的所有字段', '代码生成器', 'GET');
INSERT INTO `sys_apis` VALUES (63, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/installPlugin', '安装插件', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (64, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/pubPlug', '打包插件', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (65, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/mcp', '自动生成 MCP Tool 模板', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (66, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/mcpTest', 'MCP Tool 测试', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (67, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/mcpList', '获取 MCP ToolList', '代码生成器', 'POST');
INSERT INTO `sys_apis` VALUES (68, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/createPackage', '配置模板', '模板配置', 'POST');
INSERT INTO `sys_apis` VALUES (69, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getTemplates', '获取模板文件', '模板配置', 'GET');
INSERT INTO `sys_apis` VALUES (70, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getPackage', '获取所有模板', '模板配置', 'POST');
INSERT INTO `sys_apis` VALUES (71, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/delPackage', '删除模板', '模板配置', 'POST');
INSERT INTO `sys_apis` VALUES (72, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getMeta', '获取meta信息', '代码生成器历史', 'POST');
INSERT INTO `sys_apis` VALUES (73, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/rollback', '回滚自动生成代码', '代码生成器历史', 'POST');
INSERT INTO `sys_apis` VALUES (74, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/getSysHistory', '查询回滚记录', '代码生成器历史', 'POST');
INSERT INTO `sys_apis` VALUES (75, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/delSysHistory', '删除回滚记录', '代码生成器历史', 'POST');
INSERT INTO `sys_apis` VALUES (76, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/autoCode/addFunc', '增加模板方法', '代码生成器历史', 'POST');
INSERT INTO `sys_apis` VALUES (77, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionaryDetail/updateSysDictionaryDetail', '更新字典内容', '系统字典详情', 'PUT');
INSERT INTO `sys_apis` VALUES (78, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionaryDetail/createSysDictionaryDetail', '新增字典内容', '系统字典详情', 'POST');
INSERT INTO `sys_apis` VALUES (79, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionaryDetail/deleteSysDictionaryDetail', '删除字典内容', '系统字典详情', 'DELETE');
INSERT INTO `sys_apis` VALUES (80, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionaryDetail/findSysDictionaryDetail', '根据ID获取字典内容', '系统字典详情', 'GET');
INSERT INTO `sys_apis` VALUES (81, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionaryDetail/getSysDictionaryDetailList', '获取字典内容列表', '系统字典详情', 'GET');
INSERT INTO `sys_apis` VALUES (82, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionary/createSysDictionary', '新增字典', '系统字典', 'POST');
INSERT INTO `sys_apis` VALUES (83, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionary/deleteSysDictionary', '删除字典', '系统字典', 'DELETE');
INSERT INTO `sys_apis` VALUES (84, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionary/updateSysDictionary', '更新字典', '系统字典', 'PUT');
INSERT INTO `sys_apis` VALUES (85, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionary/findSysDictionary', '根据ID获取字典（建议选择）', '系统字典', 'GET');
INSERT INTO `sys_apis` VALUES (86, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysDictionary/getSysDictionaryList', '获取字典列表', '系统字典', 'GET');
INSERT INTO `sys_apis` VALUES (87, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysOperationRecord/createSysOperationRecord', '新增操作记录', '操作记录', 'POST');
INSERT INTO `sys_apis` VALUES (88, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysOperationRecord/findSysOperationRecord', '根据ID获取操作记录', '操作记录', 'GET');
INSERT INTO `sys_apis` VALUES (89, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysOperationRecord/getSysOperationRecordList', '获取操作记录列表', '操作记录', 'GET');
INSERT INTO `sys_apis` VALUES (90, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysOperationRecord/deleteSysOperationRecord', '删除操作记录', '操作记录', 'DELETE');
INSERT INTO `sys_apis` VALUES (91, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysOperationRecord/deleteSysOperationRecordByIds', '批量删除操作历史', '操作记录', 'DELETE');
INSERT INTO `sys_apis` VALUES (92, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/simpleUploader/upload', '插件版分片上传', '断点续传(插件版)', 'POST');
INSERT INTO `sys_apis` VALUES (93, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/simpleUploader/checkFileMd5', '文件完整度验证', '断点续传(插件版)', 'GET');
INSERT INTO `sys_apis` VALUES (94, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/simpleUploader/mergeFileMd5', '上传完成合并文件', '断点续传(插件版)', 'GET');
INSERT INTO `sys_apis` VALUES (95, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/email/emailTest', '发送测试邮件', 'email', 'POST');
INSERT INTO `sys_apis` VALUES (96, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/email/sendEmail', '发送邮件', 'email', 'POST');
INSERT INTO `sys_apis` VALUES (97, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authorityBtn/setAuthorityBtn', '设置按钮权限', '按钮权限', 'POST');
INSERT INTO `sys_apis` VALUES (98, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authorityBtn/getAuthorityBtn', '获取已有按钮权限', '按钮权限', 'POST');
INSERT INTO `sys_apis` VALUES (99, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/authorityBtn/canRemoveAuthorityBtn', '删除按钮', '按钮权限', 'POST');
INSERT INTO `sys_apis` VALUES (100, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/createSysExportTemplate', '新增导出模板', '导出模板', 'POST');
INSERT INTO `sys_apis` VALUES (101, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/deleteSysExportTemplate', '删除导出模板', '导出模板', 'DELETE');
INSERT INTO `sys_apis` VALUES (102, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/deleteSysExportTemplateByIds', '批量删除导出模板', '导出模板', 'DELETE');
INSERT INTO `sys_apis` VALUES (103, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/updateSysExportTemplate', '更新导出模板', '导出模板', 'PUT');
INSERT INTO `sys_apis` VALUES (104, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/findSysExportTemplate', '根据ID获取导出模板', '导出模板', 'GET');
INSERT INTO `sys_apis` VALUES (105, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/getSysExportTemplateList', '获取导出模板列表', '导出模板', 'GET');
INSERT INTO `sys_apis` VALUES (106, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/exportExcel', '导出Excel', '导出模板', 'GET');
INSERT INTO `sys_apis` VALUES (107, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/exportTemplate', '下载模板', '导出模板', 'GET');
INSERT INTO `sys_apis` VALUES (108, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysExportTemplate/importExcel', '导入Excel', '导出模板', 'POST');
INSERT INTO `sys_apis` VALUES (109, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/info/createInfo', '新建公告', '公告', 'POST');
INSERT INTO `sys_apis` VALUES (110, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/info/deleteInfo', '删除公告', '公告', 'DELETE');
INSERT INTO `sys_apis` VALUES (111, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/info/deleteInfoByIds', '批量删除公告', '公告', 'DELETE');
INSERT INTO `sys_apis` VALUES (112, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/info/updateInfo', '更新公告', '公告', 'PUT');
INSERT INTO `sys_apis` VALUES (113, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/info/findInfo', '根据ID获取公告', '公告', 'GET');
INSERT INTO `sys_apis` VALUES (114, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/info/getInfoList', '获取公告列表', '公告', 'GET');
INSERT INTO `sys_apis` VALUES (115, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/createSysParams', '新建参数', '参数管理', 'POST');
INSERT INTO `sys_apis` VALUES (116, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/deleteSysParams', '删除参数', '参数管理', 'DELETE');
INSERT INTO `sys_apis` VALUES (117, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/deleteSysParamsByIds', '批量删除参数', '参数管理', 'DELETE');
INSERT INTO `sys_apis` VALUES (118, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/updateSysParams', '更新参数', '参数管理', 'PUT');
INSERT INTO `sys_apis` VALUES (119, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/findSysParams', '根据ID获取参数', '参数管理', 'GET');
INSERT INTO `sys_apis` VALUES (120, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/getSysParamsList', '获取参数列表', '参数管理', 'GET');
INSERT INTO `sys_apis` VALUES (121, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/sysParams/getSysParam', '获取参数列表', '参数管理', 'GET');
INSERT INTO `sys_apis` VALUES (122, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/attachmentCategory/getCategoryList', '分类列表', '媒体库分类', 'GET');
INSERT INTO `sys_apis` VALUES (123, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/attachmentCategory/addCategory', '添加/编辑分类', '媒体库分类', 'POST');
INSERT INTO `sys_apis` VALUES (124, '2025-07-12 08:08:43.173', '2025-07-12 08:08:43.173', NULL, '/attachmentCategory/deleteCategory', '删除分类', '媒体库分类', 'POST');
INSERT INTO `sys_apis` VALUES (125, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/syncAuthority', '同步权限', '组织', 'PUT');
INSERT INTO `sys_apis` VALUES (126, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/setAuthorityLevel', '设置权限等级', '组织', 'PUT');
INSERT INTO `sys_apis` VALUES (127, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/createOrganizational', '创建组织', '组织', 'POST');
INSERT INTO `sys_apis` VALUES (128, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/getOrganizationalTree', '获取组织树', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (129, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/deleteOrganizational', '删组织', '组织', 'DELETE');
INSERT INTO `sys_apis` VALUES (130, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/updateOrganizational', '改组织', '组织', 'PUT');
INSERT INTO `sys_apis` VALUES (131, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/getOrganizationalMember', '获取组织成员', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (132, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/getUser', '获取用户', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (133, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/joinOrganizationalMember', '加入组织', '组织', 'POST');
INSERT INTO `sys_apis` VALUES (134, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/removeOrganizationalMember', '移除成员', '组织', 'DELETE');
INSERT INTO `sys_apis` VALUES (135, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/getSysAuthorityList', '获取系统权限', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (136, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/getNodeAdminAuthorityList', '获取节点管理员权限', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (137, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/test', '测试API', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (138, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/setOrganizationalMemberAuthority', '设置组织成员权限', '组织', 'PUT');
INSERT INTO `sys_apis` VALUES (139, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/changeLoginOrg', '变更登录组织(必选)', '组织', 'PUT');
INSERT INTO `sys_apis` VALUES (140, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/getUserLoginList', '获取登录列表(必选)', '组织', 'GET');
INSERT INTO `sys_apis` VALUES (141, '2025-07-12 08:23:59.879', '2025-07-12 08:23:59.879', NULL, '/org/setNodeAdmin', '设置节点管理员', '组织', 'PUT');
INSERT INTO `sys_apis` VALUES (142, '2025-07-12 17:13:25.808', '2025-07-12 17:13:25.808', NULL, '/projectInfo/createProjectInfo', '新增projectInfo表', 'projectInfo表', 'POST');
INSERT INTO `sys_apis` VALUES (143, '2025-07-12 17:13:25.843', '2025-07-12 17:13:25.843', NULL, '/projectInfo/deleteProjectInfo', '删除projectInfo表', 'projectInfo表', 'DELETE');
INSERT INTO `sys_apis` VALUES (144, '2025-07-12 17:13:25.878', '2025-07-12 17:13:25.878', NULL, '/projectInfo/deleteProjectInfoByIds', '批量删除projectInfo表', 'projectInfo表', 'DELETE');
INSERT INTO `sys_apis` VALUES (145, '2025-07-12 17:13:25.918', '2025-07-12 17:13:25.918', NULL, '/projectInfo/updateProjectInfo', '更新projectInfo表', 'projectInfo表', 'PUT');
INSERT INTO `sys_apis` VALUES (146, '2025-07-12 17:13:25.959', '2025-07-12 17:13:25.959', NULL, '/projectInfo/findProjectInfo', '根据ID获取projectInfo表', 'projectInfo表', 'GET');
INSERT INTO `sys_apis` VALUES (147, '2025-07-12 17:13:26.001', '2025-07-12 17:13:26.001', NULL, '/projectInfo/getProjectInfoList', '获取projectInfo表列表', 'projectInfo表', 'GET');

-- ----------------------------
-- Table structure for sys_authorities
-- ----------------------------
DROP TABLE IF EXISTS `sys_authorities`;
CREATE TABLE `sys_authorities`  (
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `authority_id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `authority_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '角色名',
  `parent_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '父角色ID',
  `default_router` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'dashboard' COMMENT '默认菜单',
  PRIMARY KEY (`authority_id`) USING BTREE,
  UNIQUE INDEX `uni_sys_authorities_authority_id`(`authority_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9529 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_authorities
-- ----------------------------
INSERT INTO `sys_authorities` VALUES ('2025-07-12 08:08:43.342', '2025-07-12 17:13:46.830', NULL, 888, '普通用户', 0, 'dashboard');
INSERT INTO `sys_authorities` VALUES ('2025-07-12 08:08:43.342', '2025-07-12 08:08:44.770', NULL, 8881, '普通用户子角色', 888, 'dashboard');
INSERT INTO `sys_authorities` VALUES ('2025-07-12 08:08:43.342', '2025-07-12 08:08:44.669', NULL, 9528, '测试角色', 0, 'dashboard');

-- ----------------------------
-- Table structure for sys_authority_btns
-- ----------------------------
DROP TABLE IF EXISTS `sys_authority_btns`;
CREATE TABLE `sys_authority_btns`  (
  `authority_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '角色ID',
  `sys_menu_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '菜单ID',
  `sys_base_menu_btn_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '菜单按钮ID'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_authority_btns
-- ----------------------------

-- ----------------------------
-- Table structure for sys_authority_menus
-- ----------------------------
DROP TABLE IF EXISTS `sys_authority_menus`;
CREATE TABLE `sys_authority_menus`  (
  `sys_base_menu_id` bigint UNSIGNED NOT NULL,
  `sys_authority_authority_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`sys_base_menu_id`, `sys_authority_authority_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_authority_menus
-- ----------------------------
INSERT INTO `sys_authority_menus` VALUES (1, 888);
INSERT INTO `sys_authority_menus` VALUES (1, 8881);
INSERT INTO `sys_authority_menus` VALUES (1, 9528);
INSERT INTO `sys_authority_menus` VALUES (2, 888);
INSERT INTO `sys_authority_menus` VALUES (2, 8881);
INSERT INTO `sys_authority_menus` VALUES (2, 9528);
INSERT INTO `sys_authority_menus` VALUES (3, 888);
INSERT INTO `sys_authority_menus` VALUES (3, 8881);
INSERT INTO `sys_authority_menus` VALUES (4, 888);
INSERT INTO `sys_authority_menus` VALUES (4, 8881);
INSERT INTO `sys_authority_menus` VALUES (4, 9528);
INSERT INTO `sys_authority_menus` VALUES (5, 888);
INSERT INTO `sys_authority_menus` VALUES (5, 8881);
INSERT INTO `sys_authority_menus` VALUES (6, 888);
INSERT INTO `sys_authority_menus` VALUES (6, 8881);
INSERT INTO `sys_authority_menus` VALUES (7, 888);
INSERT INTO `sys_authority_menus` VALUES (7, 8881);
INSERT INTO `sys_authority_menus` VALUES (8, 888);
INSERT INTO `sys_authority_menus` VALUES (8, 8881);
INSERT INTO `sys_authority_menus` VALUES (8, 9528);
INSERT INTO `sys_authority_menus` VALUES (9, 888);
INSERT INTO `sys_authority_menus` VALUES (9, 8881);
INSERT INTO `sys_authority_menus` VALUES (10, 888);
INSERT INTO `sys_authority_menus` VALUES (11, 888);
INSERT INTO `sys_authority_menus` VALUES (12, 888);
INSERT INTO `sys_authority_menus` VALUES (13, 888);
INSERT INTO `sys_authority_menus` VALUES (14, 888);
INSERT INTO `sys_authority_menus` VALUES (15, 888);
INSERT INTO `sys_authority_menus` VALUES (16, 888);
INSERT INTO `sys_authority_menus` VALUES (17, 888);
INSERT INTO `sys_authority_menus` VALUES (17, 8881);
INSERT INTO `sys_authority_menus` VALUES (18, 888);
INSERT INTO `sys_authority_menus` VALUES (18, 8881);
INSERT INTO `sys_authority_menus` VALUES (19, 888);
INSERT INTO `sys_authority_menus` VALUES (19, 8881);
INSERT INTO `sys_authority_menus` VALUES (20, 888);
INSERT INTO `sys_authority_menus` VALUES (20, 8881);
INSERT INTO `sys_authority_menus` VALUES (21, 888);
INSERT INTO `sys_authority_menus` VALUES (21, 8881);
INSERT INTO `sys_authority_menus` VALUES (22, 888);
INSERT INTO `sys_authority_menus` VALUES (22, 8881);
INSERT INTO `sys_authority_menus` VALUES (23, 888);
INSERT INTO `sys_authority_menus` VALUES (23, 8881);
INSERT INTO `sys_authority_menus` VALUES (24, 888);
INSERT INTO `sys_authority_menus` VALUES (24, 8881);
INSERT INTO `sys_authority_menus` VALUES (25, 888);
INSERT INTO `sys_authority_menus` VALUES (25, 8881);
INSERT INTO `sys_authority_menus` VALUES (26, 888);
INSERT INTO `sys_authority_menus` VALUES (26, 8881);
INSERT INTO `sys_authority_menus` VALUES (27, 888);
INSERT INTO `sys_authority_menus` VALUES (27, 8881);
INSERT INTO `sys_authority_menus` VALUES (28, 888);
INSERT INTO `sys_authority_menus` VALUES (28, 8881);
INSERT INTO `sys_authority_menus` VALUES (29, 888);
INSERT INTO `sys_authority_menus` VALUES (29, 8881);
INSERT INTO `sys_authority_menus` VALUES (30, 888);
INSERT INTO `sys_authority_menus` VALUES (31, 888);
INSERT INTO `sys_authority_menus` VALUES (32, 888);
INSERT INTO `sys_authority_menus` VALUES (33, 888);
INSERT INTO `sys_authority_menus` VALUES (34, 888);
INSERT INTO `sys_authority_menus` VALUES (35, 888);
INSERT INTO `sys_authority_menus` VALUES (36, 888);
INSERT INTO `sys_authority_menus` VALUES (37, 888);
INSERT INTO `sys_authority_menus` VALUES (38, 888);

-- ----------------------------
-- Table structure for sys_auto_code_histories
-- ----------------------------
DROP TABLE IF EXISTS `sys_auto_code_histories`;
CREATE TABLE `sys_auto_code_histories`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `table_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `package` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名/插件名',
  `request` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '前端传入的结构化信息',
  `struct_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结构体名称',
  `abbreviation` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结构体名称缩写',
  `business_db` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务库',
  `description` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Struct中文名称',
  `templates` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板信息',
  `Injections` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '注入路径',
  `flag` bigint NULL DEFAULT NULL COMMENT '[0:创建,1:回滚]',
  `api_ids` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'api表注册内容',
  `menu_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '菜单ID',
  `export_template_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '导出模板ID',
  `package_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '包ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_auto_code_histories_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_auto_code_histories
-- ----------------------------
INSERT INTO `sys_auto_code_histories` VALUES (1, '2025-07-12 17:13:26.102', '2025-07-12 17:13:26.102', NULL, 'project_info', 'project', '{\"package\":\"project\",\"tableName\":\"project_info\",\"businessDB\":\"\",\"structName\":\"ProjectInfo\",\"packageName\":\"projectInfo\",\"description\":\"projectInfo表\",\"abbreviation\":\"projectInfo\",\"humpPackageName\":\"project_info\",\"gvaModel\":false,\"autoMigrate\":true,\"autoCreateResource\":false,\"autoCreateApiToSql\":true,\"autoCreateMenuToSql\":true,\"autoCreateBtnAuth\":false,\"onlyTemplate\":false,\"isTree\":false,\"treeJson\":\"\",\"isAdd\":false,\"fields\":[{\"fieldName\":\"Id\",\"fieldDesc\":\"项目ID\",\"fieldType\":\"int\",\"fieldJson\":\"id\",\"dataTypeLong\":\"20\",\"comment\":\"项目ID\",\"columnName\":\"id\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":true,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"Name\",\"fieldDesc\":\"项目名称\",\"fieldType\":\"string\",\"fieldJson\":\"name\",\"dataTypeLong\":\"100\",\"comment\":\"项目名称\",\"columnName\":\"name\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"DepartmentId\",\"fieldDesc\":\"所属部门ID\",\"fieldType\":\"int\",\"fieldJson\":\"departmentId\",\"dataTypeLong\":\"20\",\"comment\":\"所属部门ID\",\"columnName\":\"department_id\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"ManagerId\",\"fieldDesc\":\"项目负责人ID\",\"fieldType\":\"int\",\"fieldJson\":\"managerId\",\"dataTypeLong\":\"20\",\"comment\":\"项目负责人ID\",\"columnName\":\"manager_id\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"Type\",\"fieldDesc\":\"项目类型：1-自研项目，2-承揽项目\",\"fieldType\":\"bool\",\"fieldJson\":\"type\",\"dataTypeLong\":\"\",\"comment\":\"项目类型：1-自研项目，2-承揽项目\",\"columnName\":\"type\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"Members\",\"fieldDesc\":\"项目成员\",\"fieldType\":\"json\",\"fieldJson\":\"members\",\"dataTypeLong\":\"\",\"comment\":\"项目成员\",\"columnName\":\"members\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"CreatedAt\",\"fieldDesc\":\"创建时间\",\"fieldType\":\"time.Time\",\"fieldJson\":\"createdAt\",\"dataTypeLong\":\"\",\"comment\":\"创建时间\",\"columnName\":\"created_at\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"},{\"fieldName\":\"UpdatedAt\",\"fieldDesc\":\"更新时间\",\"fieldType\":\"time.Time\",\"fieldJson\":\"updatedAt\",\"dataTypeLong\":\"\",\"comment\":\"更新时间\",\"columnName\":\"updated_at\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":false,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"}],\"generateWeb\":true,\"generateServer\":true,\"primaryField\":{\"fieldName\":\"Id\",\"fieldDesc\":\"项目ID\",\"fieldType\":\"int\",\"fieldJson\":\"id\",\"dataTypeLong\":\"20\",\"comment\":\"项目ID\",\"columnName\":\"id\",\"fieldSearchType\":\"\",\"fieldSearchHide\":false,\"dictType\":\"\",\"form\":true,\"table\":true,\"desc\":true,\"excel\":false,\"require\":false,\"defaultValue\":\"\",\"errorText\":\"\",\"clearable\":true,\"sort\":false,\"primaryKey\":true,\"dataSource\":{\"dbName\":\"\",\"table\":\"\",\"label\":\"\",\"value\":\"\",\"association\":1,\"hasDeletedAt\":false},\"checkDataSource\":false,\"fieldIndexType\":\"\"}}', 'ProjectInfo', 'ProjectInfo', '', 'projectInfo表', '{\"resource/package/server/api/api.go.tpl\":\"api/v1/project/project_info.go\",\"resource/package/server/model/model.go.tpl\":\"model/project/project_info.go\",\"resource/package/server/model/request/request.go.tpl\":\"model/project/request/project_info.go\",\"resource/package/server/router/router.go.tpl\":\"router/project/project_info.go\",\"resource/package/server/service/service.go.tpl\":\"service/project/project_info.go\",\"resource/package/web/api/api.js.tpl\":\"api/project/projectInfo.js\",\"resource/package/web/view/form.vue.tpl\":\"view/project/projectInfo/projectInfoForm.vue\",\"resource/package/web/view/table.vue.tpl\":\"view/project/projectInfo/projectInfo.vue\"}', '{\"PackageApiEnter\":\"{\\\"Type\\\":\\\"PackageApiEnter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\api\\\\\\\\v1\\\\\\\\enter.go\\\",\\\"ImportPath\\\":\\\"\\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/api/v1/project\\\\\\\"\\\",\\\"StructName\\\":\\\"ProjectApiGroup\\\",\\\"PackageName\\\":\\\"project\\\",\\\"RelativePath\\\":\\\"api/v1/enter.go\\\",\\\"PackageStructName\\\":\\\"ApiGroup\\\"}\",\"PackageApiModuleEnter\":\"{\\\"Type\\\":\\\"PackageApiModuleEnter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\api\\\\\\\\v1\\\\\\\\project\\\\\\\\enter.go\\\",\\\"ImportPath\\\":\\\"\\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/service\\\\\\\"\\\",\\\"RelativePath\\\":\\\"api/v1/project/enter.go\\\",\\\"StructName\\\":\\\"ProjectInfoApi\\\",\\\"AppName\\\":\\\"ServiceGroupApp\\\",\\\"GroupName\\\":\\\"ProjectServiceGroup\\\",\\\"ModuleName\\\":\\\"projectInfoService\\\",\\\"PackageName\\\":\\\"service\\\",\\\"ServiceName\\\":\\\"ProjectInfoService\\\"}\",\"PackageInitializeGorm\":\"{\\\"Type\\\":\\\"PackageInitializeGorm\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\initialize\\\\\\\\gorm_biz.go\\\",\\\"ImportPath\\\":\\\"\\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/model/project\\\\\\\"\\\",\\\"Business\\\":\\\"\\\",\\\"StructName\\\":\\\"ProjectInfo\\\",\\\"PackageName\\\":\\\"project\\\",\\\"RelativePath\\\":\\\"initialize/gorm_biz.go\\\",\\\"IsNew\\\":true}\",\"PackageInitializeRouter\":\"{\\\"Type\\\":\\\"PackageInitializeRouter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\initialize\\\\\\\\router_biz.go\\\",\\\"ImportPath\\\":\\\"\\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/router\\\\\\\"\\\",\\\"RelativePath\\\":\\\"initialize/router_biz.go\\\",\\\"AppName\\\":\\\"RouterGroupApp\\\",\\\"GroupName\\\":\\\"Project\\\",\\\"ModuleName\\\":\\\"projectRouter\\\",\\\"PackageName\\\":\\\"router\\\",\\\"FunctionName\\\":\\\"InitProjectInfoRouter\\\",\\\"RouterGroupName\\\":\\\"\\\",\\\"LeftRouterGroupName\\\":\\\"privateGroup\\\",\\\"RightRouterGroupName\\\":\\\"publicGroup\\\"}\",\"PackageRouterEnter\":\"{\\\"Type\\\":\\\"PackageRouterEnter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\router\\\\\\\\enter.go\\\",\\\"ImportPath\\\":\\\"\\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/router/project\\\\\\\"\\\",\\\"StructName\\\":\\\"Project\\\",\\\"PackageName\\\":\\\"project\\\",\\\"RelativePath\\\":\\\"router/enter.go\\\",\\\"PackageStructName\\\":\\\"RouterGroup\\\"}\",\"PackageRouterModuleEnter\":\"{\\\"Type\\\":\\\"PackageRouterModuleEnter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\router\\\\\\\\project\\\\\\\\enter.go\\\",\\\"ImportPath\\\":\\\"api \\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/api/v1\\\\\\\"\\\",\\\"RelativePath\\\":\\\"router/project/enter.go\\\",\\\"StructName\\\":\\\"ProjectInfoRouter\\\",\\\"AppName\\\":\\\"ApiGroupApp\\\",\\\"GroupName\\\":\\\"ProjectApiGroup\\\",\\\"ModuleName\\\":\\\"projectInfoApi\\\",\\\"PackageName\\\":\\\"api\\\",\\\"ServiceName\\\":\\\"ProjectInfoApi\\\"}\",\"PackageServiceEnter\":\"{\\\"Type\\\":\\\"PackageServiceEnter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\service\\\\\\\\enter.go\\\",\\\"ImportPath\\\":\\\"\\\\\\\"github.com/flipped-aurora/gin-vue-admin/server/service/project\\\\\\\"\\\",\\\"StructName\\\":\\\"ProjectServiceGroup\\\",\\\"PackageName\\\":\\\"project\\\",\\\"RelativePath\\\":\\\"service/enter.go\\\",\\\"PackageStructName\\\":\\\"ServiceGroup\\\"}\",\"PackageServiceModuleEnter\":\"{\\\"Type\\\":\\\"PackageServiceModuleEnter\\\",\\\"Path\\\":\\\"D:\\\\\\\\WorkSpace\\\\\\\\Go\\\\\\\\examine\\\\\\\\server\\\\\\\\service\\\\\\\\project\\\\\\\\enter.go\\\",\\\"ImportPath\\\":\\\"\\\",\\\"RelativePath\\\":\\\"service/project/enter.go\\\",\\\"StructName\\\":\\\"ProjectInfoService\\\",\\\"AppName\\\":\\\"\\\",\\\"GroupName\\\":\\\"\\\",\\\"ModuleName\\\":\\\"\\\",\\\"PackageName\\\":\\\"\\\",\\\"ServiceName\\\":\\\"\\\"}\"}', 0, '[142,143,144,145,146,147]', 38, 0, 0);

-- ----------------------------
-- Table structure for sys_auto_code_packages
-- ----------------------------
DROP TABLE IF EXISTS `sys_auto_code_packages`;
CREATE TABLE `sys_auto_code_packages`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `desc` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `label` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展示名',
  `template` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模版',
  `package_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包名',
  `module` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_auto_code_packages_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_auto_code_packages
-- ----------------------------
INSERT INTO `sys_auto_code_packages` VALUES (1, '2025-07-12 17:11:28.725', '2025-07-12 17:11:28.725', NULL, '系统自动读取example包', 'example包', 'package', 'example', 'github.com/flipped-aurora/gin-vue-admin/server');
INSERT INTO `sys_auto_code_packages` VALUES (2, '2025-07-12 17:11:28.725', '2025-07-12 17:11:28.725', NULL, '系统自动读取system包', 'system包', 'package', 'system', 'github.com/flipped-aurora/gin-vue-admin/server');
INSERT INTO `sys_auto_code_packages` VALUES (3, '2025-07-12 17:11:28.725', '2025-07-12 17:11:28.725', NULL, '系统自动读取announcement插件，使用前请确认是否为v2版本插件', 'announcement插件', 'plugin', 'announcement', 'github.com/flipped-aurora/gin-vue-admin/server');
INSERT INTO `sys_auto_code_packages` VALUES (4, '2025-07-12 17:11:28.725', '2025-07-12 17:11:28.725', NULL, '系统自动读取organizational插件，使用前请确认是否为v2版本插件', 'organizational插件', 'plugin', 'organizational', 'github.com/flipped-aurora/gin-vue-admin/server');
INSERT INTO `sys_auto_code_packages` VALUES (5, '2025-07-12 17:13:13.896', '2025-07-12 17:13:13.896', NULL, '', 'project信息', 'package', 'project', 'github.com/flipped-aurora/gin-vue-admin/server');

-- ----------------------------
-- Table structure for sys_base_menu_btns
-- ----------------------------
DROP TABLE IF EXISTS `sys_base_menu_btns`;
CREATE TABLE `sys_base_menu_btns`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '按钮关键key',
  `desc` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sys_base_menu_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_base_menu_btns_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_base_menu_btns
-- ----------------------------

-- ----------------------------
-- Table structure for sys_base_menu_parameters
-- ----------------------------
DROP TABLE IF EXISTS `sys_base_menu_parameters`;
CREATE TABLE `sys_base_menu_parameters`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `sys_base_menu_id` bigint UNSIGNED NULL DEFAULT NULL,
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址栏携带参数为params还是query',
  `key` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址栏携带参数的key',
  `value` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址栏携带参数的值',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_base_menu_parameters_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_base_menu_parameters
-- ----------------------------

-- ----------------------------
-- Table structure for sys_base_menus
-- ----------------------------
DROP TABLE IF EXISTS `sys_base_menus`;
CREATE TABLE `sys_base_menus`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `menu_level` bigint UNSIGNED NULL DEFAULT NULL,
  `parent_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '父菜单ID',
  `path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由path',
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由name',
  `hidden` tinyint(1) NULL DEFAULT NULL COMMENT '是否在列表隐藏',
  `component` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对应前端文件路径',
  `sort` bigint NULL DEFAULT NULL COMMENT '排序标记',
  `active_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加属性',
  `keep_alive` tinyint(1) NULL DEFAULT NULL COMMENT '附加属性',
  `default_menu` tinyint(1) NULL DEFAULT NULL COMMENT '附加属性',
  `title` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加属性',
  `icon` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加属性',
  `close_tab` tinyint(1) NULL DEFAULT NULL COMMENT '附加属性',
  `transition_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加属性',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_base_menus_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_base_menus
-- ----------------------------
INSERT INTO `sys_base_menus` VALUES (1, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'dashboard', 'dashboard', 0, 'view/dashboard/index.vue', 1, '', 0, 0, '仪表盘', 'odometer', 0, '');
INSERT INTO `sys_base_menus` VALUES (2, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'about', 'about', 0, 'view/about/index.vue', 9, '', 0, 0, '关于我们', 'info-filled', 0, '');
INSERT INTO `sys_base_menus` VALUES (3, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'admin', 'superAdmin', 0, 'view/superAdmin/index.vue', 3, '', 0, 0, '超级管理员', 'user', 0, '');
INSERT INTO `sys_base_menus` VALUES (4, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'person', 'person', 1, 'view/person/person.vue', 4, '', 0, 0, '个人信息', 'message', 0, '');
INSERT INTO `sys_base_menus` VALUES (5, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'example', 'example', 0, 'view/example/index.vue', 7, '', 0, 0, '示例文件', 'management', 0, '');
INSERT INTO `sys_base_menus` VALUES (6, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'systemTools', 'systemTools', 0, 'view/systemTools/index.vue', 5, '', 0, 0, '系统工具', 'tools', 0, '');
INSERT INTO `sys_base_menus` VALUES (7, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'https://www.gin-vue-admin.com', 'https://www.gin-vue-admin.com', 0, '/', 0, '', 0, 0, '官方网站', 'customer-gva', 0, '');
INSERT INTO `sys_base_menus` VALUES (8, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'state', 'state', 0, 'view/system/state.vue', 8, '', 0, 0, '服务器状态', 'cloudy', 0, '');
INSERT INTO `sys_base_menus` VALUES (9, '2025-07-12 08:08:44.102', '2025-07-12 08:08:44.102', NULL, 0, 0, 'plugin', 'plugin', 0, 'view/routerHolder.vue', 6, '', 0, 0, '插件系统', 'cherry', 0, '');
INSERT INTO `sys_base_menus` VALUES (10, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'authority', 'authority', 0, 'view/superAdmin/authority/authority.vue', 1, '', 0, 0, '角色管理', 'avatar', 0, '');
INSERT INTO `sys_base_menus` VALUES (11, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'menu', 'menu', 0, 'view/superAdmin/menu/menu.vue', 2, '', 1, 0, '菜单管理', 'tickets', 0, '');
INSERT INTO `sys_base_menus` VALUES (12, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'api', 'api', 0, 'view/superAdmin/api/api.vue', 3, '', 1, 0, 'api管理', 'platform', 0, '');
INSERT INTO `sys_base_menus` VALUES (13, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'user', 'user', 0, 'view/superAdmin/user/user.vue', 4, '', 0, 0, '用户管理', 'coordinate', 0, '');
INSERT INTO `sys_base_menus` VALUES (14, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'dictionary', 'dictionary', 0, 'view/superAdmin/dictionary/sysDictionary.vue', 5, '', 0, 0, '字典管理', 'notebook', 0, '');
INSERT INTO `sys_base_menus` VALUES (15, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'operation', 'operation', 0, 'view/superAdmin/operation/sysOperationRecord.vue', 6, '', 0, 0, '操作历史', 'pie-chart', 0, '');
INSERT INTO `sys_base_menus` VALUES (16, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 3, 'sysParams', 'sysParams', 0, 'view/superAdmin/params/sysParams.vue', 7, '', 0, 0, '参数管理', 'compass', 0, '');
INSERT INTO `sys_base_menus` VALUES (17, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 5, 'upload', 'upload', 0, 'view/example/upload/upload.vue', 5, '', 0, 0, '媒体库（上传下载）', 'upload', 0, '');
INSERT INTO `sys_base_menus` VALUES (18, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 5, 'breakpoint', 'breakpoint', 0, 'view/example/breakpoint/breakpoint.vue', 6, '', 0, 0, '断点续传', 'upload-filled', 0, '');
INSERT INTO `sys_base_menus` VALUES (19, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 5, 'customer', 'customer', 0, 'view/example/customer/customer.vue', 7, '', 0, 0, '客户列表（资源示例）', 'avatar', 0, '');
INSERT INTO `sys_base_menus` VALUES (20, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'autoCode', 'autoCode', 0, 'view/systemTools/autoCode/index.vue', 1, '', 1, 0, '代码生成器', 'cpu', 0, '');
INSERT INTO `sys_base_menus` VALUES (21, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'formCreate', 'formCreate', 0, 'view/systemTools/formCreate/index.vue', 3, '', 1, 0, '表单生成器', 'magic-stick', 0, '');
INSERT INTO `sys_base_menus` VALUES (22, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'system', 'system', 0, 'view/systemTools/system/system.vue', 4, '', 0, 0, '系统配置', 'operation', 0, '');
INSERT INTO `sys_base_menus` VALUES (23, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'autoCodeAdmin', 'autoCodeAdmin', 0, 'view/systemTools/autoCodeAdmin/index.vue', 2, '', 0, 0, '自动化代码管理', 'magic-stick', 0, '');
INSERT INTO `sys_base_menus` VALUES (24, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'autoCodeEdit/:id', 'autoCodeEdit', 1, 'view/systemTools/autoCode/index.vue', 0, '', 0, 0, '自动化代码-${id}', 'magic-stick', 0, '');
INSERT INTO `sys_base_menus` VALUES (25, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'autoPkg', 'autoPkg', 0, 'view/systemTools/autoPkg/autoPkg.vue', 0, '', 0, 0, '模板配置', 'folder', 0, '');
INSERT INTO `sys_base_menus` VALUES (26, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'exportTemplate', 'exportTemplate', 0, 'view/systemTools/exportTemplate/exportTemplate.vue', 5, '', 0, 0, '导出模板', 'reading', 0, '');
INSERT INTO `sys_base_menus` VALUES (27, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'picture', 'picture', 0, 'view/systemTools/autoCode/picture.vue', 6, '', 0, 0, 'AI页面绘制', 'picture-filled', 0, '');
INSERT INTO `sys_base_menus` VALUES (28, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'mcpTool', 'mcpTool', 0, 'view/systemTools/autoCode/mcp.vue', 7, '', 0, 0, 'Mcp Tools模板', 'magnet', 0, '');
INSERT INTO `sys_base_menus` VALUES (29, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 6, 'mcpTest', 'mcpTest', 0, 'view/systemTools/autoCode/mcpTest.vue', 7, '', 0, 0, 'Mcp Tools测试', 'partly-cloudy', 0, '');
INSERT INTO `sys_base_menus` VALUES (30, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 9, 'https://plugin.gin-vue-admin.com/', 'https://plugin.gin-vue-admin.com/', 0, 'https://plugin.gin-vue-admin.com/', 0, '', 0, 0, '插件市场', 'shop', 0, '');
INSERT INTO `sys_base_menus` VALUES (31, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 9, 'installPlugin', 'installPlugin', 0, 'view/systemTools/installPlugin/index.vue', 1, '', 0, 0, '插件安装', 'box', 0, '');
INSERT INTO `sys_base_menus` VALUES (32, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 9, 'pubPlug', 'pubPlug', 0, 'view/systemTools/pubPlug/pubPlug.vue', 3, '', 0, 0, '打包插件', 'files', 0, '');
INSERT INTO `sys_base_menus` VALUES (33, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 9, 'plugin-email', 'plugin-email', 0, 'plugin/email/view/index.vue', 4, '', 0, 0, '邮件插件', 'message', 0, '');
INSERT INTO `sys_base_menus` VALUES (34, '2025-07-12 08:08:44.137', '2025-07-12 08:08:44.137', NULL, 1, 9, 'anInfo', 'anInfo', 0, 'plugin/announcement/view/info.vue', 5, '', 0, 0, '公告管理[示例]', 'scaleToOriginal', 0, '');
INSERT INTO `sys_base_menus` VALUES (35, '2025-07-12 08:23:59.946', '2025-07-12 08:23:59.946', NULL, 0, 0, 'organizationalMenu', 'organizationalMenu', 0, 'view/routerHolder.vue', 0, '', 0, 0, '组织架构管理', 'school', 0, '');
INSERT INTO `sys_base_menus` VALUES (36, '2025-07-12 08:23:59.981', '2025-07-12 08:23:59.981', NULL, 0, 35, 'org', 'org', 0, 'plugin/organizational/view/organizational.vue', 0, '', 0, 0, '组织架构', 'user-filled', 0, '');
INSERT INTO `sys_base_menus` VALUES (37, '2025-07-12 08:23:59.981', '2025-07-12 08:23:59.981', NULL, 0, 35, 'syncAuthority', 'syncAuthority', 0, 'plugin/organizational/view/SyncAuthority.vue', 0, '', 0, 0, '角色权限设置', 'operation', 0, '');
INSERT INTO `sys_base_menus` VALUES (38, '2025-07-12 17:13:26.055', '2025-07-12 17:13:26.055', NULL, 0, 0, 'projectInfo', 'projectInfo', 0, 'view/project/projectInfo/projectInfo.vue', 0, '', 0, 0, 'projectInfo表', '', 0, '');

-- ----------------------------
-- Table structure for sys_data_authority_id
-- ----------------------------
DROP TABLE IF EXISTS `sys_data_authority_id`;
CREATE TABLE `sys_data_authority_id`  (
  `sys_authority_authority_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
  `data_authority_id_authority_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`sys_authority_authority_id`, `data_authority_id_authority_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_data_authority_id
-- ----------------------------
INSERT INTO `sys_data_authority_id` VALUES (888, 888);
INSERT INTO `sys_data_authority_id` VALUES (888, 8881);
INSERT INTO `sys_data_authority_id` VALUES (888, 9528);
INSERT INTO `sys_data_authority_id` VALUES (9528, 8881);
INSERT INTO `sys_data_authority_id` VALUES (9528, 9528);

-- ----------------------------
-- Table structure for sys_dictionaries
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionaries`;
CREATE TABLE `sys_dictionaries`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典名（中）',
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典名（英）',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '状态',
  `desc` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_dictionaries_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dictionaries
-- ----------------------------
INSERT INTO `sys_dictionaries` VALUES (1, '2025-07-12 08:08:43.569', '2025-07-12 08:08:43.632', NULL, '性别', 'gender', 1, '性别字典');
INSERT INTO `sys_dictionaries` VALUES (2, '2025-07-12 08:08:43.569', '2025-07-12 08:08:43.706', NULL, '数据库int类型', 'int', 1, 'int类型对应的数据库类型');
INSERT INTO `sys_dictionaries` VALUES (3, '2025-07-12 08:08:43.569', '2025-07-12 08:08:43.780', NULL, '数据库时间日期类型', 'time.Time', 1, '数据库时间日期类型');
INSERT INTO `sys_dictionaries` VALUES (4, '2025-07-12 08:08:43.569', '2025-07-12 08:08:43.856', NULL, '数据库浮点型', 'float64', 1, '数据库浮点型');
INSERT INTO `sys_dictionaries` VALUES (5, '2025-07-12 08:08:43.569', '2025-07-12 08:08:43.931', NULL, '数据库字符串', 'string', 1, '数据库字符串');
INSERT INTO `sys_dictionaries` VALUES (6, '2025-07-12 08:08:43.569', '2025-07-12 08:08:44.008', NULL, '数据库bool类型', 'bool', 1, '数据库bool类型');

-- ----------------------------
-- Table structure for sys_dictionary_details
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionary_details`;
CREATE TABLE `sys_dictionary_details`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `label` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展示值',
  `value` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典值',
  `extend` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展值',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '启用状态',
  `sort` bigint NULL DEFAULT NULL COMMENT '排序标记',
  `sys_dictionary_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '关联标记',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_dictionary_details_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dictionary_details
-- ----------------------------
INSERT INTO `sys_dictionary_details` VALUES (1, '2025-07-12 08:08:43.646', '2025-07-12 08:08:43.646', NULL, '男', '1', '', 1, 1, 1);
INSERT INTO `sys_dictionary_details` VALUES (2, '2025-07-12 08:08:43.646', '2025-07-12 08:08:43.646', NULL, '女', '2', '', 1, 2, 1);
INSERT INTO `sys_dictionary_details` VALUES (3, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'smallint', '1', 'mysql', 1, 1, 2);
INSERT INTO `sys_dictionary_details` VALUES (4, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'mediumint', '2', 'mysql', 1, 2, 2);
INSERT INTO `sys_dictionary_details` VALUES (5, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'int', '3', 'mysql', 1, 3, 2);
INSERT INTO `sys_dictionary_details` VALUES (6, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'bigint', '4', 'mysql', 1, 4, 2);
INSERT INTO `sys_dictionary_details` VALUES (7, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'int2', '5', 'pgsql', 1, 5, 2);
INSERT INTO `sys_dictionary_details` VALUES (8, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'int4', '6', 'pgsql', 1, 6, 2);
INSERT INTO `sys_dictionary_details` VALUES (9, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'int6', '7', 'pgsql', 1, 7, 2);
INSERT INTO `sys_dictionary_details` VALUES (10, '2025-07-12 08:08:43.720', '2025-07-12 08:08:43.720', NULL, 'int8', '8', 'pgsql', 1, 8, 2);
INSERT INTO `sys_dictionary_details` VALUES (11, '2025-07-12 08:08:43.795', '2025-07-12 08:08:43.795', NULL, 'date', '', '', 1, 0, 3);
INSERT INTO `sys_dictionary_details` VALUES (12, '2025-07-12 08:08:43.795', '2025-07-12 08:08:43.795', NULL, 'time', '1', 'mysql', 1, 1, 3);
INSERT INTO `sys_dictionary_details` VALUES (13, '2025-07-12 08:08:43.795', '2025-07-12 08:08:43.795', NULL, 'year', '2', 'mysql', 1, 2, 3);
INSERT INTO `sys_dictionary_details` VALUES (14, '2025-07-12 08:08:43.795', '2025-07-12 08:08:43.795', NULL, 'datetime', '3', 'mysql', 1, 3, 3);
INSERT INTO `sys_dictionary_details` VALUES (15, '2025-07-12 08:08:43.795', '2025-07-12 08:08:43.795', NULL, 'timestamp', '5', 'mysql', 1, 5, 3);
INSERT INTO `sys_dictionary_details` VALUES (16, '2025-07-12 08:08:43.795', '2025-07-12 08:08:43.795', NULL, 'timestamptz', '6', 'pgsql', 1, 5, 3);
INSERT INTO `sys_dictionary_details` VALUES (17, '2025-07-12 08:08:43.870', '2025-07-12 08:08:43.870', NULL, 'float', '', '', 1, 0, 4);
INSERT INTO `sys_dictionary_details` VALUES (18, '2025-07-12 08:08:43.870', '2025-07-12 08:08:43.870', NULL, 'double', '1', 'mysql', 1, 1, 4);
INSERT INTO `sys_dictionary_details` VALUES (19, '2025-07-12 08:08:43.870', '2025-07-12 08:08:43.870', NULL, 'decimal', '2', 'mysql', 1, 2, 4);
INSERT INTO `sys_dictionary_details` VALUES (20, '2025-07-12 08:08:43.870', '2025-07-12 08:08:43.870', NULL, 'numeric', '3', 'pgsql', 1, 3, 4);
INSERT INTO `sys_dictionary_details` VALUES (21, '2025-07-12 08:08:43.870', '2025-07-12 08:08:43.870', NULL, 'smallserial', '4', 'pgsql', 1, 4, 4);
INSERT INTO `sys_dictionary_details` VALUES (22, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'char', '', '', 1, 0, 5);
INSERT INTO `sys_dictionary_details` VALUES (23, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'varchar', '1', 'mysql', 1, 1, 5);
INSERT INTO `sys_dictionary_details` VALUES (24, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'tinyblob', '2', 'mysql', 1, 2, 5);
INSERT INTO `sys_dictionary_details` VALUES (25, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'tinytext', '3', 'mysql', 1, 3, 5);
INSERT INTO `sys_dictionary_details` VALUES (26, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'text', '4', 'mysql', 1, 4, 5);
INSERT INTO `sys_dictionary_details` VALUES (27, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'blob', '5', 'mysql', 1, 5, 5);
INSERT INTO `sys_dictionary_details` VALUES (28, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'mediumblob', '6', 'mysql', 1, 6, 5);
INSERT INTO `sys_dictionary_details` VALUES (29, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'mediumtext', '7', 'mysql', 1, 7, 5);
INSERT INTO `sys_dictionary_details` VALUES (30, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'longblob', '8', 'mysql', 1, 8, 5);
INSERT INTO `sys_dictionary_details` VALUES (31, '2025-07-12 08:08:43.946', '2025-07-12 08:08:43.946', NULL, 'longtext', '9', 'mysql', 1, 9, 5);
INSERT INTO `sys_dictionary_details` VALUES (32, '2025-07-12 08:08:44.025', '2025-07-12 08:08:44.025', NULL, 'tinyint', '1', 'mysql', 1, 0, 6);
INSERT INTO `sys_dictionary_details` VALUES (33, '2025-07-12 08:08:44.025', '2025-07-12 08:08:44.025', NULL, 'bool', '2', 'pgsql', 1, 0, 6);

-- ----------------------------
-- Table structure for sys_export_template_condition
-- ----------------------------
DROP TABLE IF EXISTS `sys_export_template_condition`;
CREATE TABLE `sys_export_template_condition`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `template_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板标识',
  `from` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '条件取的key',
  `column` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作为查询条件的字段',
  `operator` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作符',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_export_template_condition_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_export_template_condition
-- ----------------------------

-- ----------------------------
-- Table structure for sys_export_template_join
-- ----------------------------
DROP TABLE IF EXISTS `sys_export_template_join`;
CREATE TABLE `sys_export_template_join`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `template_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板标识',
  `joins` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联',
  `table` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表',
  `on` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联条件',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_export_template_join_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_export_template_join
-- ----------------------------

-- ----------------------------
-- Table structure for sys_export_templates
-- ----------------------------
DROP TABLE IF EXISTS `sys_export_templates`;
CREATE TABLE `sys_export_templates`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `db_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
  `table_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称',
  `template_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板标识',
  `template_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `limit` bigint NULL DEFAULT NULL COMMENT '导出限制',
  `order` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_export_templates_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_export_templates
-- ----------------------------
INSERT INTO `sys_export_templates` VALUES (1, '2025-07-12 08:08:44.510', '2025-07-12 08:08:44.510', NULL, '', 'api', 'sys_apis', 'api', '{\n\"path\":\"路径\",\n\"method\":\"方法（大写）\",\n\"description\":\"方法介绍\",\n\"api_group\":\"方法分组\"\n}', NULL, '');

-- ----------------------------
-- Table structure for sys_ignore_apis
-- ----------------------------
DROP TABLE IF EXISTS `sys_ignore_apis`;
CREATE TABLE `sys_ignore_apis`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'api路径',
  `method` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'POST' COMMENT '方法',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_ignore_apis_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_ignore_apis
-- ----------------------------
INSERT INTO `sys_ignore_apis` VALUES (1, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/swagger/*any', 'GET');
INSERT INTO `sys_ignore_apis` VALUES (2, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/api/freshCasbin', 'GET');
INSERT INTO `sys_ignore_apis` VALUES (3, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/uploads/file/*filepath', 'GET');
INSERT INTO `sys_ignore_apis` VALUES (4, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/health', 'GET');
INSERT INTO `sys_ignore_apis` VALUES (5, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/uploads/file/*filepath', 'HEAD');
INSERT INTO `sys_ignore_apis` VALUES (6, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/autoCode/llmAuto', 'POST');
INSERT INTO `sys_ignore_apis` VALUES (7, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/system/reloadSystem', 'POST');
INSERT INTO `sys_ignore_apis` VALUES (8, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/base/login', 'POST');
INSERT INTO `sys_ignore_apis` VALUES (9, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/base/captcha', 'POST');
INSERT INTO `sys_ignore_apis` VALUES (10, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/init/initdb', 'POST');
INSERT INTO `sys_ignore_apis` VALUES (11, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/init/checkdb', 'POST');
INSERT INTO `sys_ignore_apis` VALUES (12, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/info/getInfoDataSource', 'GET');
INSERT INTO `sys_ignore_apis` VALUES (13, '2025-07-12 08:08:43.234', '2025-07-12 08:08:43.234', NULL, '/info/getInfoPublic', 'GET');

-- ----------------------------
-- Table structure for sys_operation_records
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_records`;
CREATE TABLE `sys_operation_records`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `ip` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求ip',
  `method` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方法',
  `path` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求路径',
  `status` bigint NULL DEFAULT NULL COMMENT '请求状态',
  `latency` bigint NULL DEFAULT NULL COMMENT '延迟',
  `agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '代理',
  `error_message` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误信息',
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求Body',
  `resp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '响应Body',
  `user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_operation_records_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 93 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_operation_records
-- ----------------------------
INSERT INTO `sys_operation_records` VALUES (1, '2025-07-12 08:25:30.349', '2025-07-12 08:25:30.349', NULL, '127.0.0.1', 'GET', '/api/getApiGroups', 200, 22287300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{}', '{\"code\":0,\"data\":{\"apiGroupMap\":{\"api\":\"api\",\"attachmentCategory\":\"媒体库分类\",\"authority\":\"角色\",\"authorityBtn\":\"按钮权限\",\"autoCode\":\"代码生成器历史\",\"casbin\":\"casbin\",\"customer\":\"客户\",\"email\":\"email\",\"fileUploadAndDownload\":\"文件上传与下载\",\"info\":\"公告\",\"jwt\":\"jwt\",\"menu\":\"菜单\",\"org\":\"组织\",\"simpleUploader\":\"断点续传(插件版)\",\"sysDictionary\":\"系统字典\",\"sysDictionaryDetail\":\"系统字典详情\",\"sysExportTemplate\":\"导出模板\",\"sysOperationRecord\":\"操作记录\",\"sysParams\":\"参数管理\",\"system\":\"系统服务\",\"user\":\"系统用户\"},\"groups\":[\"jwt\",\"系统用户\",\"api\",\"角色\",\"casbin\",\"菜单\",\"分片上传\",\"文件上传与下载\",\"系统服务\",\"客户\",\"代码生成器\",\"模板配置\",\"代码生成器历史\",\"系统字典详情\",\"系统字典\",\"操作记录\",\"断点续传(插件版)\",\"email\",\"按钮权限\",\"导出模板\",\"公告\",\"参数管理\",\"媒体库分类\",\"组织\"]},\"msg\":\"成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (2, '2025-07-12 08:25:40.282', '2025-07-12 08:25:40.282', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 58413400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"org\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"org\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (3, '2025-07-12 08:25:40.516', '2025-07-12 08:25:40.516', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 185140700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (4, '2025-07-12 08:25:42.072', '2025-07-12 08:25:42.072', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 51151600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"org\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"org\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (5, '2025-07-12 08:25:42.299', '2025-07-12 08:25:42.299', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 187090400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (6, '2025-07-12 08:25:42.753', '2025-07-12 08:25:42.753', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 52269800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"dashboard\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"dashboard\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (7, '2025-07-12 08:25:42.992', '2025-07-12 08:25:42.992', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 194634000, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (8, '2025-07-12 08:25:44.847', '2025-07-12 08:25:44.847', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 218136200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (9, '2025-07-12 08:25:47.545', '2025-07-12 08:25:47.545', NULL, '127.0.0.1', 'POST', '/casbin/updateCasbin', 200, 92533200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (10, '2025-07-12 08:25:49.143', '2025-07-12 08:25:49.143', NULL, '127.0.0.1', 'POST', '/casbin/updateCasbin', 200, 90461400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (11, '2025-07-12 08:25:50.270', '2025-07-12 08:25:50.270', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 207921700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (12, '2025-07-12 08:26:26.490', '2025-07-12 08:26:26.490', NULL, '127.0.0.1', 'PUT', '/org/syncAuthority', 200, 119031000, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (13, '2025-07-12 08:26:33.130', '2025-07-12 08:26:33.130', NULL, '127.0.0.1', 'PUT', '/org/setAuthorityLevel', 200, 77194800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authority_id\":888,\"level\":1}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (14, '2025-07-12 08:26:35.600', '2025-07-12 08:26:35.600', NULL, '127.0.0.1', 'PUT', '/org/syncAuthority', 200, 119819000, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (15, '2025-07-12 08:26:52.816', '2025-07-12 08:26:52.816', NULL, '127.0.0.1', 'POST', '/org/createOrganizational', 200, 70453100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"name\":\"科创公司\",\"type\":1,\"parent_id\":0,\"sort\":0,\"status\":1}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (16, '2025-07-12 08:27:07.023', '2025-07-12 08:27:07.023', NULL, '127.0.0.1', 'POST', '/org/createOrganizational', 200, 61575200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"name\":\"数智所\",\"type\":2,\"parent_id\":1,\"sort\":0,\"status\":1}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (17, '2025-07-12 08:28:07.974', '2025-07-12 08:28:07.974', NULL, '127.0.0.1', 'GET', '/api/getApiGroups', 200, 19043400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{}', '{\"code\":0,\"data\":{\"apiGroupMap\":{\"api\":\"api\",\"attachmentCategory\":\"媒体库分类\",\"authority\":\"角色\",\"authorityBtn\":\"按钮权限\",\"autoCode\":\"代码生成器历史\",\"casbin\":\"casbin\",\"customer\":\"客户\",\"email\":\"email\",\"fileUploadAndDownload\":\"文件上传与下载\",\"info\":\"公告\",\"jwt\":\"jwt\",\"menu\":\"菜单\",\"org\":\"组织\",\"simpleUploader\":\"断点续传(插件版)\",\"sysDictionary\":\"系统字典\",\"sysDictionaryDetail\":\"系统字典详情\",\"sysExportTemplate\":\"导出模板\",\"sysOperationRecord\":\"操作记录\",\"sysParams\":\"参数管理\",\"system\":\"系统服务\",\"user\":\"系统用户\"},\"groups\":[\"jwt\",\"系统用户\",\"api\",\"角色\",\"casbin\",\"菜单\",\"分片上传\",\"文件上传与下载\",\"系统服务\",\"客户\",\"代码生成器\",\"模板配置\",\"代码生成器历史\",\"系统字典详情\",\"系统字典\",\"操作记录\",\"断点续传(插件版)\",\"email\",\"按钮权限\",\"导出模板\",\"公告\",\"参数管理\",\"媒体库分类\",\"组织\"]},\"msg\":\"成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (18, '2025-07-12 08:28:29.163', '2025-07-12 08:28:29.163', NULL, '127.0.0.1', 'POST', '/org/joinOrganizationalMember', 200, 96755400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":2,\"user_ids\":[1,2]}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (19, '2025-07-12 08:28:32.961', '2025-07-12 08:28:32.961', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35700900, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (20, '2025-07-12 08:28:35.797', '2025-07-12 08:28:35.797', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36720400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (21, '2025-07-12 08:28:36.584', '2025-07-12 08:28:36.584', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 69740100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (22, '2025-07-12 08:28:37.103', '2025-07-12 08:28:37.103', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 68582300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (23, '2025-07-12 08:28:37.619', '2025-07-12 08:28:37.619', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 86807200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (24, '2025-07-12 08:28:38.428', '2025-07-12 08:28:38.428', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 80941700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (25, '2025-07-12 08:28:39.663', '2025-07-12 08:28:39.663', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 80926400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (26, '2025-07-12 08:28:50.104', '2025-07-12 08:28:50.104', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36721500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (27, '2025-07-12 08:28:51.106', '2025-07-12 08:28:51.106', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36642600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (28, '2025-07-12 08:28:51.636', '2025-07-12 08:28:51.636', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 33200200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (29, '2025-07-12 08:28:54.048', '2025-07-12 08:28:54.048', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 33691800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (30, '2025-07-12 08:30:06.173', '2025-07-12 08:30:06.173', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36720700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (31, '2025-07-12 08:30:07.639', '2025-07-12 08:30:07.639', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 34139600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (32, '2025-07-12 08:30:08.609', '2025-07-12 08:30:08.609', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 34117400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (33, '2025-07-12 08:30:09.427', '2025-07-12 08:30:09.427', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35731600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (34, '2025-07-12 08:30:10.344', '2025-07-12 08:30:10.344', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36019600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (35, '2025-07-12 08:30:11.569', '2025-07-12 08:30:11.569', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35170700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (36, '2025-07-12 08:30:12.486', '2025-07-12 08:30:12.486', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35197300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (37, '2025-07-12 08:30:22.395', '2025-07-12 08:30:22.395', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36390000, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":1,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (38, '2025-07-12 08:30:23.597', '2025-07-12 08:30:23.597', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36284800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":1,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (39, '2025-07-12 08:30:24.453', '2025-07-12 08:30:24.453', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35152600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":1,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (40, '2025-07-12 08:30:27.163', '2025-07-12 08:30:27.163', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 33765200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (41, '2025-07-12 08:30:27.960', '2025-07-12 08:30:27.960', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 38797000, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (42, '2025-07-12 08:30:28.505', '2025-07-12 08:30:28.505', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35254100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (43, '2025-07-12 08:30:28.986', '2025-07-12 08:30:28.986', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 36218900, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":2,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (44, '2025-07-12 08:30:34.562', '2025-07-12 08:30:34.562', NULL, '127.0.0.1', 'DELETE', '/org/removeOrganizationalMember', 200, 37270800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":2,\"user_ids\":[2]}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (45, '2025-07-12 08:30:38.538', '2025-07-12 08:30:38.538', NULL, '127.0.0.1', 'DELETE', '/org/removeOrganizationalMember', 200, 34770500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":2,\"user_ids\":[1]}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (46, '2025-07-12 08:31:16.639', '2025-07-12 08:31:16.639', NULL, '127.0.0.1', 'POST', '/user/setUserAuthorities', 200, 80537300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"ID\":1,\"authorityIds\":[888,8881,9528]}', '{\"code\":0,\"data\":{},\"msg\":\"修改成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (47, '2025-07-12 08:31:18.700', '2025-07-12 08:31:18.700', NULL, '127.0.0.1', 'POST', '/user/setUserAuthorities', 200, 78127300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"ID\":2,\"authorityIds\":[888]}', '{\"code\":0,\"data\":{},\"msg\":\"修改成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (48, '2025-07-12 08:31:38.208', '2025-07-12 08:31:38.208', NULL, '127.0.0.1', 'POST', '/user/setUserAuthorities', 200, 49886100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"ID\":2,\"authorityIds\":[]}', '{\"code\":7,\"data\":{},\"msg\":\"修改失败\"}', 1);
INSERT INTO `sys_operation_records` VALUES (49, '2025-07-12 09:11:36.323', '2025-07-12 09:11:36.323', NULL, '127.0.0.1', 'PUT', '/org/updateOrganizational', 200, 101396600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"ID\":2,\"name\":\"计划财务部\",\"type\":2,\"parent_id\":1,\"sort\":0,\"status\":1,\"parentName\":\"科创公司\"}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (50, '2025-07-12 09:12:12.854', '2025-07-12 09:12:12.854', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 35018900, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":1,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (51, '2025-07-12 09:12:46.057', '2025-07-12 09:12:46.057', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 69387700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (52, '2025-07-12 09:12:48.452', '2025-07-12 09:12:48.452', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 66614100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (53, '2025-07-12 09:13:26.900', '2025-07-12 09:13:26.900', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 69527500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (54, '2025-07-12 09:13:28.823', '2025-07-12 09:13:28.823', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 66285600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (55, '2025-07-12 09:17:32.465', '2025-07-12 09:17:32.465', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 52977400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"about\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"about\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (56, '2025-07-12 09:17:32.710', '2025-07-12 09:17:32.710', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 195488200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (57, '2025-07-12 09:18:05.671', '2025-07-12 09:18:05.671', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 50071300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"about\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"about\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (58, '2025-07-12 09:18:05.893', '2025-07-12 09:18:05.893', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 182486900, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (59, '2025-07-12 09:18:09.506', '2025-07-12 09:18:09.506', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 49698500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"https://www.gin-vue-admin.com\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"https://www.gin-vue-admin.com\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (60, '2025-07-12 09:18:09.738', '2025-07-12 09:18:09.738', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 183254700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (61, '2025-07-12 09:18:25.748', '2025-07-12 09:18:25.748', NULL, '127.0.0.1', 'PUT', '/user/setSelfSetting', 200, 34856600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"weakness\":false,\"grey\":false,\"primaryColor\":\"#3b82f6\",\"showTabs\":true,\"darkMode\":\"auto\",\"layout_side_width\":256,\"layout_side_collapsed_width\":80,\"layout_side_item_height\":48,\"show_watermark\":false,\"side_mode\":\"normal\",\"transition_type\":\"slide\"}', '{\"code\":0,\"data\":{},\"msg\":\"设置成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (62, '2025-07-12 09:18:28.384', '2025-07-12 09:18:28.384', NULL, '127.0.0.1', 'PUT', '/user/setSelfSetting', 200, 33321400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"weakness\":false,\"grey\":false,\"primaryColor\":\"#3b82f6\",\"showTabs\":true,\"darkMode\":\"dark\",\"layout_side_width\":256,\"layout_side_collapsed_width\":80,\"layout_side_item_height\":48,\"show_watermark\":false,\"side_mode\":\"normal\",\"transition_type\":\"slide\"}', '{\"code\":0,\"data\":{},\"msg\":\"设置成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (63, '2025-07-12 09:18:29.170', '2025-07-12 09:18:29.170', NULL, '127.0.0.1', 'PUT', '/user/setSelfSetting', 200, 34002800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"weakness\":false,\"grey\":false,\"primaryColor\":\"#3b82f6\",\"showTabs\":true,\"darkMode\":\"light\",\"layout_side_width\":256,\"layout_side_collapsed_width\":80,\"layout_side_item_height\":48,\"show_watermark\":false,\"side_mode\":\"normal\",\"transition_type\":\"slide\"}', '{\"code\":0,\"data\":{},\"msg\":\"设置成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (64, '2025-07-12 09:18:30.132', '2025-07-12 09:18:30.132', NULL, '127.0.0.1', 'PUT', '/user/setSelfSetting', 200, 75063300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"weakness\":false,\"grey\":false,\"primaryColor\":\"#3b82f6\",\"showTabs\":true,\"darkMode\":\"auto\",\"layout_side_width\":256,\"layout_side_collapsed_width\":80,\"layout_side_item_height\":48,\"show_watermark\":false,\"side_mode\":\"normal\",\"transition_type\":\"slide\"}', '{\"code\":0,\"data\":{},\"msg\":\"设置成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (65, '2025-07-12 09:18:30.790', '2025-07-12 09:18:30.790', NULL, '127.0.0.1', 'PUT', '/user/setSelfSetting', 200, 80811900, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"weakness\":false,\"grey\":false,\"primaryColor\":\"#3b82f6\",\"showTabs\":true,\"darkMode\":\"light\",\"layout_side_width\":256,\"layout_side_collapsed_width\":80,\"layout_side_item_height\":48,\"show_watermark\":false,\"side_mode\":\"normal\",\"transition_type\":\"slide\"}', '{\"code\":0,\"data\":{},\"msg\":\"设置成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (66, '2025-07-12 09:19:09.765', '2025-07-12 09:19:09.765', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 66947100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (67, '2025-07-12 09:19:17.595', '2025-07-12 09:19:17.595', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 76699500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (68, '2025-07-12 09:19:39.265', '2025-07-12 09:19:39.265', NULL, '127.0.0.1', 'DELETE', '/org/removeOrganizationalMember', 200, 36717000, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1,\"user_ids\":[1]}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (69, '2025-07-12 09:19:56.016', '2025-07-12 09:19:56.016', NULL, '127.0.0.1', 'POST', '/org/createOrganizational', 200, 61108100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"name\":\"科创公司\",\"type\":1,\"parent_id\":0,\"sort\":0,\"status\":1}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (70, '2025-07-12 09:20:04.718', '2025-07-12 09:20:04.718', NULL, '127.0.0.1', 'POST', '/org/createOrganizational', 200, 114200800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"name\":\"科创公司\",\"type\":1,\"parent_id\":0,\"sort\":0,\"status\":1}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (71, '2025-07-12 09:20:27.795', '2025-07-12 09:20:27.795', NULL, '127.0.0.1', 'PUT', '/org/syncAuthority', 200, 122131300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (72, '2025-07-12 09:20:38.931', '2025-07-12 09:20:38.931', NULL, '127.0.0.1', 'DELETE', '/org/deleteOrganizational', 200, 98453300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (73, '2025-07-12 09:20:41.281', '2025-07-12 09:20:41.281', NULL, '127.0.0.1', 'DELETE', '/org/deleteOrganizational', 200, 97638600, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (74, '2025-07-12 09:20:47.885', '2025-07-12 09:20:47.885', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 210801300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (75, '2025-07-12 09:21:12.015', '2025-07-12 09:21:12.015', NULL, '127.0.0.1', 'PUT', '/authority/updateAuthority', 200, 51786700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"authorityId\":888,\"AuthorityName\":\"普通用户\",\"parentId\":0,\"defaultRouter\":\"dashboard\"}', '{\"code\":0,\"data\":{\"authority\":{\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"DeletedAt\":null,\"authorityId\":888,\"authorityName\":\"普通用户\",\"parentId\":0,\"dataAuthorityId\":null,\"children\":null,\"menus\":null,\"defaultRouter\":\"dashboard\"}},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (76, '2025-07-12 09:21:12.249', '2025-07-12 09:21:12.249', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 184340100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (77, '2025-07-12 09:21:13.680', '2025-07-12 09:21:13.680', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 183004800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (78, '2025-07-12 09:21:22.476', '2025-07-12 09:21:22.476', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 75096100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (79, '2025-07-12 09:22:01.148', '2025-07-12 09:22:01.148', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 37308300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":1,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (80, '2025-07-12 09:22:01.939', '2025-07-12 09:22:01.939', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 33596100, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":1,\"org_id\":1,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (81, '2025-07-12 09:22:30.266', '2025-07-12 09:22:30.266', NULL, '127.0.0.1', 'POST', '/org/joinOrganizationalMember', 200, 87452800, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1,\"user_ids\":[2]}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (82, '2025-07-12 09:22:35.840', '2025-07-12 09:22:35.840', NULL, '127.0.0.1', 'POST', '/org/joinOrganizationalMember', 200, 51654200, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1,\"user_ids\":[1]}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (83, '2025-07-12 09:22:40.780', '2025-07-12 09:22:40.780', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 33220900, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":1,\"is_admin\":true}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (84, '2025-07-12 09:22:41.622', '2025-07-12 09:22:41.622', NULL, '127.0.0.1', 'PUT', '/org/setNodeAdmin', 200, 34605300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"user_id\":2,\"org_id\":1,\"is_admin\":false}', '{\"code\":0,\"data\":{},\"msg\":\"操作成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (85, '2025-07-12 09:22:51.024', '2025-07-12 09:22:51.024', NULL, '127.0.0.1', 'PUT', '/org/changeLoginOrg', 200, 65632700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{\"org_id\":1}', '{\"code\":0,\"data\":{},\"msg\":\"切换成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (86, '2025-07-12 11:57:37.378', '2025-07-12 11:57:37.378', NULL, '127.0.0.1', 'GET', '/api/getApiGroups', 200, 21482700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{}', '{\"code\":0,\"data\":{\"apiGroupMap\":{\"api\":\"api\",\"attachmentCategory\":\"媒体库分类\",\"authority\":\"角色\",\"authorityBtn\":\"按钮权限\",\"autoCode\":\"代码生成器历史\",\"casbin\":\"casbin\",\"customer\":\"客户\",\"email\":\"email\",\"fileUploadAndDownload\":\"文件上传与下载\",\"info\":\"公告\",\"jwt\":\"jwt\",\"menu\":\"菜单\",\"org\":\"组织\",\"simpleUploader\":\"断点续传(插件版)\",\"sysDictionary\":\"系统字典\",\"sysDictionaryDetail\":\"系统字典详情\",\"sysExportTemplate\":\"导出模板\",\"sysOperationRecord\":\"操作记录\",\"sysParams\":\"参数管理\",\"system\":\"系统服务\",\"user\":\"系统用户\"},\"groups\":[\"jwt\",\"系统用户\",\"api\",\"角色\",\"casbin\",\"菜单\",\"分片上传\",\"文件上传与下载\",\"系统服务\",\"客户\",\"代码生成器\",\"模板配置\",\"代码生成器历史\",\"系统字典详情\",\"系统字典\",\"操作记录\",\"断点续传(插件版)\",\"email\",\"按钮权限\",\"导出模板\",\"公告\",\"参数管理\",\"媒体库分类\",\"组织\"]},\"msg\":\"成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (87, '2025-07-12 11:58:04.523', '2025-07-12 11:58:04.523', NULL, '127.0.0.1', 'GET', '/api/syncApi', 200, 30776700, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{}', '{\"code\":0,\"data\":{\"deleteApis\":[{\"ID\":87,\"CreatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"UpdatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"path\":\"/sysOperationRecord/createSysOperationRecord\",\"description\":\"新增操作记录\",\"apiGroup\":\"操作记录\",\"method\":\"POST\"},{\"ID\":92,\"CreatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"UpdatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"path\":\"/simpleUploader/upload\",\"description\":\"插件版分片上传\",\"apiGroup\":\"断点续传(插件版)\",\"method\":\"POST\"},{\"ID\":93,\"CreatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"UpdatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"path\":\"/simpleUploader/checkFileMd5\",\"description\":\"文件完整度验证\",\"apiGroup\":\"断点续传(插件版)\",\"method\":\"GET\"},{\"ID\":94,\"CreatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"UpdatedAt\":\"2025-07-12T08:08:43.173+08:00\",\"path\":\"/simpleUploader/mergeFileMd5\",\"description\":\"上传完成合并文件\",\"apiGroup\":\"断点续传(插件版)\",\"method\":\"GET\"}],\"ignoreApis\":[{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/swagger/*any\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/api/freshCasbin\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/uploads/file/*filepath\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/health\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/uploads/file/*filepath\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"HEAD\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/autoCode/llmAuto\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/system/reloadSystem\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/base/login\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/base/captcha\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/init/initdb\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/init/checkdb\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/info/getInfoDataSource\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/info/getInfoPublic\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"}],\"newApis\":[{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/sysExportTemplate/exportExcelByToken\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/sysExportTemplate/exportTemplateByToken\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/sse\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"GET\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/autoCode/initMenu\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/autoCode/initAPI\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"},{\"ID\":0,\"CreatedAt\":\"0001-01-01T00:00:00Z\",\"UpdatedAt\":\"0001-01-01T00:00:00Z\",\"path\":\"/message\",\"description\":\"\",\"apiGroup\":\"\",\"method\":\"POST\"}]},\"msg\":\"成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (88, '2025-07-12 16:48:17.139', '2025-07-12 16:48:17.139', NULL, '127.0.0.1', 'GET', '/api/getApiGroups', 200, 57113500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{}', '{\"code\":0,\"data\":{\"apiGroupMap\":{\"api\":\"api\",\"attachmentCategory\":\"媒体库分类\",\"authority\":\"角色\",\"authorityBtn\":\"按钮权限\",\"autoCode\":\"代码生成器历史\",\"casbin\":\"casbin\",\"customer\":\"客户\",\"email\":\"email\",\"fileUploadAndDownload\":\"文件上传与下载\",\"info\":\"公告\",\"jwt\":\"jwt\",\"menu\":\"菜单\",\"org\":\"组织\",\"simpleUploader\":\"断点续传(插件版)\",\"sysDictionary\":\"系统字典\",\"sysDictionaryDetail\":\"系统字典详情\",\"sysExportTemplate\":\"导出模板\",\"sysOperationRecord\":\"操作记录\",\"sysParams\":\"参数管理\",\"system\":\"系统服务\",\"user\":\"系统用户\"},\"groups\":[\"jwt\",\"系统用户\",\"api\",\"角色\",\"casbin\",\"菜单\",\"分片上传\",\"文件上传与下载\",\"系统服务\",\"客户\",\"代码生成器\",\"模板配置\",\"代码生成器历史\",\"系统字典详情\",\"系统字典\",\"操作记录\",\"断点续传(插件版)\",\"email\",\"按钮权限\",\"导出模板\",\"公告\",\"参数管理\",\"媒体库分类\",\"组织\"]},\"msg\":\"成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (89, '2025-07-12 17:11:23.050', '2025-07-12 17:11:23.050', NULL, '127.0.0.1', 'GET', '/api/getApiGroups', 200, 18059400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '{}', '{\"code\":0,\"data\":{\"apiGroupMap\":{\"api\":\"api\",\"attachmentCategory\":\"媒体库分类\",\"authority\":\"角色\",\"authorityBtn\":\"按钮权限\",\"autoCode\":\"代码生成器历史\",\"casbin\":\"casbin\",\"customer\":\"客户\",\"email\":\"email\",\"fileUploadAndDownload\":\"文件上传与下载\",\"info\":\"公告\",\"jwt\":\"jwt\",\"menu\":\"菜单\",\"org\":\"组织\",\"simpleUploader\":\"断点续传(插件版)\",\"sysDictionary\":\"系统字典\",\"sysDictionaryDetail\":\"系统字典详情\",\"sysExportTemplate\":\"导出模板\",\"sysOperationRecord\":\"操作记录\",\"sysParams\":\"参数管理\",\"system\":\"系统服务\",\"user\":\"系统用户\"},\"groups\":[\"jwt\",\"系统用户\",\"api\",\"角色\",\"casbin\",\"菜单\",\"分片上传\",\"文件上传与下载\",\"系统服务\",\"客户\",\"代码生成器\",\"模板配置\",\"代码生成器历史\",\"系统字典详情\",\"系统字典\",\"操作记录\",\"断点续传(插件版)\",\"email\",\"按钮权限\",\"导出模板\",\"公告\",\"参数管理\",\"媒体库分类\",\"组织\"]},\"msg\":\"成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (90, '2025-07-12 17:13:46.935', '2025-07-12 17:13:46.935', NULL, '127.0.0.1', 'POST', '/menu/addMenuAuthority', 200, 179816500, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"添加成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (91, '2025-07-12 17:13:49.503', '2025-07-12 17:13:49.503', NULL, '127.0.0.1', 'POST', '/casbin/updateCasbin', 200, 76956300, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"更新成功\"}', 1);
INSERT INTO `sys_operation_records` VALUES (92, '2025-07-12 17:13:50.456', '2025-07-12 17:13:50.456', NULL, '127.0.0.1', 'POST', '/casbin/updateCasbin', 200, 79520400, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '', '[超出记录长度]', '{\"code\":0,\"data\":{},\"msg\":\"更新成功\"}', 1);

-- ----------------------------
-- Table structure for sys_params
-- ----------------------------
DROP TABLE IF EXISTS `sys_params`;
CREATE TABLE `sys_params`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数名称',
  `key` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数键',
  `value` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数值',
  `desc` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数说明',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_params_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_params
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_authority
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_authority`;
CREATE TABLE `sys_user_authority`  (
  `sys_user_id` bigint UNSIGNED NOT NULL,
  `sys_authority_authority_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`sys_user_id`, `sys_authority_authority_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_authority
-- ----------------------------
INSERT INTO `sys_user_authority` VALUES (1, 888);
INSERT INTO `sys_user_authority` VALUES (1, 8881);
INSERT INTO `sys_user_authority` VALUES (1, 9528);
INSERT INTO `sys_user_authority` VALUES (2, 888);

-- ----------------------------
-- Table structure for sys_users
-- ----------------------------
DROP TABLE IF EXISTS `sys_users`;
CREATE TABLE `sys_users`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `deleted_at` datetime(3) NULL DEFAULT NULL,
  `uuid` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户UUID',
  `username` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户登录名',
  `password` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户登录密码',
  `nick_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '系统用户' COMMENT '用户昵称',
  `header_img` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'https://qmplusimg.henrongyi.top/gva_header.jpg' COMMENT '用户头像',
  `authority_id` bigint UNSIGNED NULL DEFAULT 888 COMMENT '用户角色ID',
  `phone` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户手机号',
  `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `enable` bigint NULL DEFAULT 1 COMMENT '用户是否被冻结 1正常 2冻结',
  `origin_setting` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置',
  `org_id` bigint UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_users_uuid`(`uuid` ASC) USING BTREE,
  INDEX `idx_sys_users_username`(`username` ASC) USING BTREE,
  INDEX `idx_sys_users_deleted_at`(`deleted_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 130 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_users
-- ----------------------------
INSERT INTO `sys_users` VALUES (1, '2025-07-12 08:08:44.279', '2025-07-12 09:22:50.975', NULL, 'f8503cb2-30ce-45dd-b52c-aa41d91439b4', 'admin', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', 'Mr.奇淼', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '17611111111', '<EMAIL>', 1, '{\"darkMode\":\"light\",\"grey\":false,\"layout_side_collapsed_width\":80,\"layout_side_item_height\":48,\"layout_side_width\":256,\"primaryColor\":\"#3b82f6\",\"showTabs\":true,\"show_watermark\":false,\"side_mode\":\"normal\",\"transition_type\":\"slide\",\"weakness\":false}', 1);
INSERT INTO `sys_users` VALUES (2, '2025-07-12 08:08:44.279', '2025-07-12 08:31:18.668', NULL, '13c2c816-34f7-4951-887f-76a6923570f7', 'a303176530', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '用户1', 'https:///qmplusimg.henrongyi.top/1572075907logo.png', 888, '17611111111', '<EMAIL>', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (3, '2025-02-12 09:06:00.000', '2025-02-21 12:03:40.000', '2025-07-12 09:30:48.000', 'e4e1dbf9-5f02-11f0-a537-0242ac110003', 'ry', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王五', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '15666666666', '<EMAIL>', 1, NULL, 9);
INSERT INTO `sys_users` VALUES (4, '2025-02-14 06:00:15.000', '2025-02-21 13:30:29.000', '2025-07-12 09:30:48.000', 'e4e1dea3-5f02-11f0-a537-0242ac110003', '10001', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张三', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (5, '2025-02-19 12:11:04.000', '2025-02-19 12:11:04.000', '2025-07-12 09:30:48.000', 'e4e1dfdf-5f02-11f0-a537-0242ac110003', '100002', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '李四', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (6, '2025-02-21 10:22:50.000', '2025-06-11 19:49:44.000', NULL, 'e4e1e0e5-5f02-11f0-a537-0242ac110003', '10079652', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '燕雨虹', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (7, '2025-02-21 15:09:15.000', '2025-02-21 15:09:15.000', '2025-07-12 09:30:48.000', 'e4e1e1da-5f02-11f0-a537-0242ac110003', '10112531', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '宋寅', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (8, '2025-02-21 15:11:13.000', '2025-02-22 10:59:40.000', '2025-07-12 09:30:48.000', 'e4e1e2d0-5f02-11f0-a537-0242ac110003', '10084188', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张海君', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 7);
INSERT INTO `sys_users` VALUES (9, '2025-02-21 15:13:09.000', '2025-02-21 15:15:53.000', '2025-07-12 09:30:48.000', 'e4e1e3b9-5f02-11f0-a537-0242ac110003', '10076045', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '程学文', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 7);
INSERT INTO `sys_users` VALUES (10, '2025-02-21 15:13:09.000', '2025-02-21 15:16:05.000', '2025-07-12 09:30:48.000', 'e4e1e49f-5f02-11f0-a537-0242ac110003', '10108835', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '胡宇非', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 7);
INSERT INTO `sys_users` VALUES (11, '2025-02-21 15:13:10.000', '2025-02-21 15:20:33.000', '2025-07-12 09:30:48.000', 'e4e1e587-5f02-11f0-a537-0242ac110003', '10154816', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '马康', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (12, '2025-02-21 15:13:10.000', '2025-02-21 15:20:29.000', '2025-07-12 09:30:48.000', 'e4e1e673-5f02-11f0-a537-0242ac110003', '10111931', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '曹蕃', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (13, '2025-02-21 15:13:10.000', '2025-02-21 15:20:24.000', '2025-07-12 09:30:48.000', 'e4e1e75f-5f02-11f0-a537-0242ac110003', '10152462', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '焦洋', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (14, '2025-02-21 15:13:10.000', '2025-02-21 15:20:18.000', '2025-07-12 09:30:48.000', 'e4e1e84e-5f02-11f0-a537-0242ac110003', '10112885', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (15, '2025-02-21 15:13:10.000', '2025-02-21 15:20:13.000', '2025-07-12 09:30:48.000', 'e4e1e935-5f02-11f0-a537-0242ac110003', '10139059', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '南雄', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (16, '2025-02-21 15:13:10.000', '2025-02-21 15:20:08.000', '2025-07-12 09:30:48.000', 'e4e1ea1f-5f02-11f0-a537-0242ac110003', '10130732', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张猛', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (17, '2025-02-21 15:13:10.000', '2025-02-21 15:20:02.000', '2025-07-12 09:30:48.000', 'e4e1eb05-5f02-11f0-a537-0242ac110003', '10135860', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '李程', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (18, '2025-02-21 15:13:10.000', '2025-02-21 15:18:58.000', '2025-07-12 09:30:48.000', 'e4e1ebe6-5f02-11f0-a537-0242ac110003', '10157394', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '郭瑛', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (19, '2025-02-21 15:13:10.000', '2025-02-21 15:19:55.000', '2025-07-12 09:30:48.000', 'e4e1ecc9-5f02-11f0-a537-0242ac110003', '10152386', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '成艳亭', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (20, '2025-02-21 15:13:11.000', '2025-02-21 15:19:46.000', '2025-07-12 09:30:48.000', 'e4e1eda9-5f02-11f0-a537-0242ac110003', '10159180', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '齐春凯', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (21, '2025-02-21 15:13:11.000', '2025-02-21 15:18:46.000', '2025-07-12 09:30:48.000', 'e4e1ee90-5f02-11f0-a537-0242ac110003', '10159179', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘博伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 5);
INSERT INTO `sys_users` VALUES (22, '2025-02-22 11:04:02.000', '2025-05-29 14:00:34.000', NULL, 'e4e1ef77-5f02-11f0-a537-0242ac110003', '10076045', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '程学文', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (23, '2025-02-22 11:04:02.000', '2025-05-29 14:00:34.000', NULL, 'e4e1f04c-5f02-11f0-a537-0242ac110003', '10108835', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '胡宇非', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (24, '2025-02-22 11:04:02.000', '2025-05-29 14:00:34.000', NULL, 'e4e1f125-5f02-11f0-a537-0242ac110003', '10084188', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张海君', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (25, '2025-02-22 11:04:03.000', '2025-06-16 09:40:12.000', NULL, 'e4e1f1fc-5f02-11f0-a537-0242ac110003', '10071942', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '唐宏芬', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (26, '2025-02-22 11:04:03.000', '2025-06-17 11:18:33.000', NULL, 'e4e1f2dc-5f02-11f0-a537-0242ac110003', '10152260', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '李同辉', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (27, '2025-02-22 11:04:03.000', '2025-06-12 19:27:16.000', NULL, 'e4e1f3c8-5f02-11f0-a537-0242ac110003', '10141370', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '朱鸿飞', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (28, '2025-02-22 11:04:03.000', '2025-06-12 19:11:00.000', NULL, 'e4e1f4ae-5f02-11f0-a537-0242ac110003', '10135524', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王丽杰', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (29, '2025-02-22 11:04:03.000', '2025-06-12 17:30:10.000', NULL, 'e4e1f584-5f02-11f0-a537-0242ac110003', '10129656', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '陈洪胜', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (30, '2025-02-22 11:04:03.000', '2025-06-12 18:53:30.000', NULL, 'e4e1f65f-5f02-11f0-a537-0242ac110003', '10134987', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '孙坤元', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (31, '2025-02-22 11:04:03.000', '2025-05-29 14:00:34.000', NULL, 'e4e1f737-5f02-11f0-a537-0242ac110003', '10130903', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘帅伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (32, '2025-02-22 11:04:03.000', '2025-06-12 17:40:02.000', NULL, 'e4e1f810-5f02-11f0-a537-0242ac110003', '10157389', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张宏博', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (33, '2025-02-22 11:04:03.000', '2025-06-12 17:16:02.000', NULL, 'e4e1f8e5-5f02-11f0-a537-0242ac110003', '10157390', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '高大伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (34, '2025-02-22 11:04:03.000', '2025-06-12 10:54:55.000', NULL, 'e4e1f9b9-5f02-11f0-a537-0242ac110003', '10129265', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '段琦玮', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (35, '2025-02-22 11:04:03.000', '2025-06-12 20:38:56.000', NULL, 'e4e1fa9b-5f02-11f0-a537-0242ac110003', '10157421', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '冯少广', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (36, '2025-02-22 11:04:03.000', '2025-06-12 16:33:29.000', NULL, 'e4e1fb5d-5f02-11f0-a537-0242ac110003', '10152433', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '殷波', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (37, '2025-02-22 11:04:03.000', '2025-06-12 16:52:29.000', NULL, 'e4e1fc19-5f02-11f0-a537-0242ac110003', '10134601', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '丛聪', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (38, '2025-02-22 11:04:03.000', '2025-06-13 15:59:04.000', NULL, 'e4e1fcd7-5f02-11f0-a537-0242ac110003', '10112531', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '宋寅', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (39, '2025-02-22 11:04:04.000', '2025-06-12 15:54:53.000', NULL, 'e4e1fd94-5f02-11f0-a537-0242ac110003', '10154816', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '马康', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (40, '2025-02-22 11:04:04.000', '2025-06-12 18:50:51.000', NULL, 'e4e1fe4f-5f02-11f0-a537-0242ac110003', '10111931', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '曹蕃', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (41, '2025-02-22 11:04:04.000', '2025-05-29 14:00:34.000', NULL, 'e4e1ff0d-5f02-11f0-a537-0242ac110003', '10152462', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '焦洋', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (42, '2025-02-22 11:04:04.000', '2025-06-12 19:18:49.000', NULL, 'e4e1ffc8-5f02-11f0-a537-0242ac110003', '10112885', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (43, '2025-02-22 11:04:04.000', '2025-06-12 17:07:34.000', NULL, 'e4e20083-5f02-11f0-a537-0242ac110003', '10139059', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '南雄', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (44, '2025-02-22 11:04:04.000', '2025-06-12 17:06:07.000', NULL, 'e4e2013e-5f02-11f0-a537-0242ac110003', '10130732', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张猛', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (45, '2025-02-22 11:04:04.000', '2025-05-29 14:00:34.000', NULL, 'e4e201f8-5f02-11f0-a537-0242ac110003', '10135860', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '李程', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (46, '2025-02-22 11:04:04.000', '2025-06-12 16:47:21.000', NULL, 'e4e202b2-5f02-11f0-a537-0242ac110003', '10157394', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '郭瑛', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (47, '2025-02-22 11:04:04.000', '2025-06-12 11:37:24.000', NULL, 'e4e2036c-5f02-11f0-a537-0242ac110003', '10124025', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘燕', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (48, '2025-02-22 11:04:04.000', '2025-06-12 14:56:09.000', NULL, 'e4e20426-5f02-11f0-a537-0242ac110003', '10152595', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '闫敬书', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (49, '2025-02-22 11:04:04.000', '2025-06-12 15:11:27.000', NULL, 'e4e204e3-5f02-11f0-a537-0242ac110003', '10109717', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '袁世通', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (50, '2025-02-22 11:04:04.000', '2025-05-29 14:00:34.000', NULL, 'e4e205a0-5f02-11f0-a537-0242ac110003', '10152386', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '成艳亭', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (51, '2025-02-22 11:04:04.000', '2025-05-29 14:00:34.000', NULL, 'e4e2065b-5f02-11f0-a537-0242ac110003', '10159213', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '杜文韬', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (52, '2025-02-22 11:04:04.000', '2025-06-12 18:50:00.000', NULL, 'e4e20713-5f02-11f0-a537-0242ac110003', '10159185', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王泽', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (53, '2025-02-22 11:04:05.000', '2025-05-29 14:00:34.000', NULL, 'e4e207cf-5f02-11f0-a537-0242ac110003', '10159184', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '杨文博', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (54, '2025-02-22 11:04:05.000', '2025-05-29 14:00:34.000', NULL, 'e4e2088f-5f02-11f0-a537-0242ac110003', '10159181', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '乔彦涵', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (55, '2025-02-22 11:04:05.000', '2025-06-11 16:04:05.000', NULL, 'e4e20949-5f02-11f0-a537-0242ac110003', '10159178', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '柴潇', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (56, '2025-02-22 11:04:05.000', '2025-05-29 14:00:34.000', NULL, 'e4e20a03-5f02-11f0-a537-0242ac110003', '10159182', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王翠', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (57, '2025-02-22 11:04:05.000', '2025-05-29 14:00:34.000', NULL, 'e4e20abd-5f02-11f0-a537-0242ac110003', '10159180', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '齐春凯', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (58, '2025-02-22 11:04:05.000', '2025-06-12 18:07:00.000', NULL, 'e4e20b77-5f02-11f0-a537-0242ac110003', '10159179', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘博伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (59, '2025-02-22 11:04:05.000', '2025-05-29 14:00:34.000', NULL, 'e4e20c31-5f02-11f0-a537-0242ac110003', '10159183', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王万堃', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (60, '2025-02-22 11:04:05.000', '2025-06-12 21:07:26.000', NULL, 'e4e20cea-5f02-11f0-a537-0242ac110003', '10107814', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘海洋', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (61, '2025-02-22 11:04:05.000', '2025-06-12 17:05:07.000', NULL, 'e4e20da4-5f02-11f0-a537-0242ac110003', '10159922', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘志杰', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (62, '2025-02-25 01:06:46.000', '2025-02-25 16:54:30.000', '2025-07-12 09:30:48.000', 'e4e20e75-5f02-11f0-a537-0242ac110003', 'salary001', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '薪酬填报人', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (63, '2025-03-03 11:21:14.000', '2025-05-29 14:00:34.000', NULL, 'e4e214ed-5f02-11f0-a537-0242ac110003', '10094841', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '晏芳芝', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (64, '2025-05-13 05:46:19.000', '2025-06-12 21:08:27.000', NULL, 'e4e21616-5f02-11f0-a537-0242ac110003', '10110555', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王超奇', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '16633335780', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (65, '2025-05-28 17:25:33.000', '2025-06-24 08:41:20.000', NULL, 'e4e216e0-5f02-11f0-a537-0242ac110003', '10077818', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王帅', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (66, '2025-05-28 17:26:06.000', '2025-05-29 14:00:34.000', NULL, 'e4e217a3-5f02-11f0-a537-0242ac110003', '10103540', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '鲁兵', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (67, '2025-05-28 17:26:40.000', '2025-05-29 14:00:34.000', NULL, 'e4e21862-5f02-11f0-a537-0242ac110003', '10085321', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '马泳吏', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (68, '2025-05-29 14:00:14.000', '2025-05-29 14:00:34.000', NULL, 'e4e2191f-5f02-11f0-a537-0242ac110003', '10081401', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '丰五强', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (69, '2025-05-29 14:00:14.000', '2025-05-29 14:00:34.000', NULL, 'e4e219e0-5f02-11f0-a537-0242ac110003', '10087838', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '李成伟', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (70, '2025-05-29 14:00:15.000', '2025-05-29 14:00:34.000', NULL, 'e4e21a9e-5f02-11f0-a537-0242ac110003', '10077991', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '李沫颖', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (71, '2025-05-29 14:00:15.000', '2025-05-29 14:00:34.000', NULL, 'e4e21b5c-5f02-11f0-a537-0242ac110003', '10123461', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王一跃', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (72, '2025-05-29 14:00:15.000', '2025-06-06 14:23:57.000', NULL, 'e4e21c19-5f02-11f0-a537-0242ac110003', '10055759', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '吕振涛', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (73, '2025-05-29 14:00:15.000', '2025-05-29 14:00:34.000', NULL, 'e4e21cd5-5f02-11f0-a537-0242ac110003', '10083843', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '袁森', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (74, '2025-05-29 14:00:15.000', '2025-05-29 14:00:34.000', NULL, 'e4e21d91-5f02-11f0-a537-0242ac110003', '10083024', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '付彦哲', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (75, '2025-05-29 14:00:15.000', '2025-06-12 08:40:44.000', NULL, 'e4e21e50-5f02-11f0-a537-0242ac110003', '10079361', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王舒', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (76, '2025-05-29 14:00:15.000', '2025-05-29 14:00:34.000', NULL, 'e4e21f0c-5f02-11f0-a537-0242ac110003', '10130581', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '白丽萍', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (77, '2025-05-29 14:00:15.000', '2025-06-11 16:32:39.000', NULL, 'e4e21fc9-5f02-11f0-a537-0242ac110003', '10102642', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘克然', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (78, '2025-05-29 14:00:15.000', '2025-05-29 14:00:34.000', NULL, 'e4e22084-5f02-11f0-a537-0242ac110003', '10113966', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '房濛濛', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (79, '2025-05-29 14:07:32.000', '2025-06-12 14:45:51.000', NULL, 'e4e22138-5f02-11f0-a537-0242ac110003', '10076201', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '姜明贺', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (80, '2025-06-09 08:28:03.000', '2025-06-24 08:52:51.000', NULL, 'e4e221e7-5f02-11f0-a537-0242ac110003', '10071943', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '杨琳', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (81, '2025-06-09 08:30:32.000', '2025-06-11 12:37:28.000', NULL, 'e4e22295-5f02-11f0-a537-0242ac110003', '10160103', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '蒋成', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (82, '2025-06-09 08:30:57.000', '2025-06-11 12:37:23.000', NULL, 'e4e22344-5f02-11f0-a537-0242ac110003', '10160096', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '张杰', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (83, '2025-06-09 08:31:41.000', '2025-06-11 12:37:16.000', NULL, 'e4e223ee-5f02-11f0-a537-0242ac110003', '10160100', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '师长立', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (84, '2025-06-09 08:32:05.000', '2025-06-11 12:37:11.000', NULL, 'e4e2249c-5f02-11f0-a537-0242ac110003', '10160101', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '杨璐', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (85, '2025-06-09 08:32:38.000', '2025-06-11 12:37:06.000', NULL, 'e4e22549-5f02-11f0-a537-0242ac110003', '10160102', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '刘伟东', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);
INSERT INTO `sys_users` VALUES (86, '2025-06-09 08:33:15.000', '2025-06-11 12:36:58.000', NULL, 'e4e225f5-5f02-11f0-a537-0242ac110003', '10152239', '$2a$10$aUhfZ36hh3EzzsfHdi3y5uIyTafe0QYQnSPxaEC1VZWtg6seKXV8.', '王乐', 'https://qmplusimg.henrongyi.top/gva_header.jpg', 888, '', '', 1, NULL, 1);

SET FOREIGN_KEY_CHECKS = 1;
