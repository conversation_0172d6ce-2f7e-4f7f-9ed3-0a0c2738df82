# 多考核配置数据查询修复

## 问题分析

### 原始问题
前端只能获取到考核配置ID=12的数据，无法获取考核配置ID=11的数据，导致历史标签页无法显示已存在的考核系数。

### 根本原因
后端的 `getCoefficientData` 方法逻辑有缺陷：
1. 只查询最新考核配置的数据
2. 只有当最新配置没有数据时，才查询上一个配置的数据
3. 无法同时返回多个考核配置的数据

## 修复方案

### 1. 修改查询逻辑

**修改前**：
```go
// 只查询最新考核配置的数据
err = global.GVA_DB.Table("assessment_coefficient_allocation aca").
    Where("aca.assessment_config_id = ? AND aca.username IN ?", latestAssessmentId, departmentUsernames).
    Scan(&currentCoeffs).Error

// 如果没有数据，才查询历史数据
if len(currentCoeffs) == 0 {
    prevCoeffs, prevId, err := s.getPreviousCoefficients(...)
}
```

**修改后**：
```go
// 获取所有未归档考核配置的ID
var allAssessmentIds []int
err = global.GVA_DB.Table("assessment_config").
    Select("id").
    Where("is_archived = ?", false).
    Pluck("id", &allAssessmentIds).Error

// 查询所有考核配置的数据
err = global.GVA_DB.Table("assessment_coefficient_allocation aca").
    Where("aca.assessment_config_id IN ? AND aca.username IN ?", allAssessmentIds, departmentUsernames).
    Scan(&allCoeffs).Error
```

### 2. 数据结构扩展

在 `CoefficientData` 结构体中添加 `AllCoefficients` 字段：

```go
type CoefficientData struct {
    HasCurrentData       bool              `json:"hasCurrentData"`
    CurrentAssessmentId  int               `json:"currentAssessmentId"`
    Coefficients         []CoefficientInfo `json:"coefficients"`
    PreviousAssessmentId *int              `json:"previousAssessmentId"`
    PreviousCoefficients []CoefficientInfo `json:"previousCoefficients"`
    AllCoefficients      []CoefficientInfo `json:"allCoefficients"`  // 新增
}
```

### 3. 数据分离逻辑

```go
// 分离当前考核配置和其他考核配置的数据
for _, coeff := range allCoeffs {
    if coeff.AssessmentConfigId == latestAssessmentId {
        currentCoeffs = append(currentCoeffs, coeff)
    } else {
        otherCoeffs = append(otherCoeffs, coeff)
    }
}
```

## 数据库查询结果

### 修复前
只返回考核配置12的数据：
```json
{
  "hasCurrentData": true,
  "currentAssessmentId": 12,
  "coefficients": [
    {"assessmentConfigId": 12, "username": "10159180", "projectId": 69, "assessmentCoefficient": 100},
    {"assessmentConfigId": 12, "username": "10159179", "projectId": 21, "assessmentCoefficient": 100}
  ],
  "previousAssessmentId": null,
  "previousCoefficients": null
}
```

### 修复后
返回所有考核配置的数据：
```json
{
  "hasCurrentData": true,
  "currentAssessmentId": 12,
  "coefficients": [
    {"assessmentConfigId": 12, "username": "10159180", "projectId": 69, "assessmentCoefficient": 100},
    {"assessmentConfigId": 12, "username": "10159179", "projectId": 21, "assessmentCoefficient": 100}
  ],
  "previousAssessmentId": 11,
  "previousCoefficients": [
    {"assessmentConfigId": 11, "username": "10159180", "projectId": 69, "assessmentCoefficient": 69},
    {"assessmentConfigId": 11, "username": "10159180", "projectId": 21, "assessmentCoefficient": 31},
    {"assessmentConfigId": 11, "username": "10159179", "projectId": 21, "assessmentCoefficient": 40},
    {"assessmentConfigId": 11, "username": "10159179", "projectId": 23, "assessmentCoefficient": 60}
  ],
  "allCoefficients": [
    // 包含所有考核配置的数据
  ]
}
```

## 前端影响

### 1. 数据加载改进

前端的 `loadExistingCoefficientData` 函数现在可以：
- 为"7月考核任务"标签页加载考核配置12的数据
- 为"2025月度考核"标签页加载考核配置11的数据

### 2. 标签页数据显示

**"7月考核任务"标签页**：
- 齐春凯在项目69：100%
- 刘博伟在项目21：100%

**"2025月度考核"标签页**：
- 齐春凯在项目69：69%，项目21：31%
- 刘博伟在项目21：40%，项目23：60%

## 测试验证

### 1. 数据库查询测试
```sql
SELECT aca.assessment_config_id, aca.username, aca.project_id, aca.assessment_coefficient
FROM assessment_coefficient_allocation aca
WHERE aca.assessment_config_id IN (11, 12)
  AND aca.username IN ('10159180', '10159179')
ORDER BY aca.assessment_config_id DESC, aca.created_at DESC;
```

### 2. API 测试
重启后端服务，调用 `/assessment/data` 接口，验证返回数据包含：
- `coefficients`: 考核配置12的数据
- `previousCoefficients`: 考核配置11的数据
- `allCoefficients`: 所有考核配置的数据

### 3. 前端显示测试
1. 刷新前端页面
2. 切换到"2025月度考核"标签页
3. 验证表格中显示考核配置11的系数数据
4. 切换到"7月考核任务"标签页
5. 验证表格中显示考核配置12的系数数据

## 预期结果

修复后，前端应该能够：
- ✅ 在不同标签页显示对应考核配置的数据
- ✅ 正确加载历史考核配置的系数数据
- ✅ 支持多个考核配置的独立管理
- ✅ 提供完整的数据上下文给前端使用

这个修复解决了多考核配置环境下的数据显示问题，确保每个标签页都能正确显示对应的考核系数数据。
