# 组织特定奖金和配额信息返回修复

## 需求说明

用户希望后端只返回当前传入的组织ID对应的奖金和配额信息，而不是返回所有部门的信息。

## 问题分析

### 修改前的逻辑
后端返回所有部门的奖金和配额信息：

**奖金信息**：
```json
{
  "id": 2,
  "bonusName": "第三季度奖金",
  "totalAmount": 250,
  "departmentAllocations": [
    {
      "departmentId": 5,
      "departmentName": "数字能源智联技术研究所",
      "allocatedAmount": 120,
      "usedAmount": 0,
      "remainingAmount": 120
    },
    {
      "departmentId": 6,
      "departmentName": "友好并网与新型配网技术研究所",
      "allocatedAmount": 130,
      "usedAmount": 0,
      "remainingAmount": 130
    }
  ]
}
```

**配额信息**：
```json
{
  "id": 6,
  "quotaName": "2025",
  "departmentQuotas": [
    {
      "departmentId": 5,
      "departmentName": "数字能源智联技术研究所",
      "quotaAmount": 12,
      "usedAmount": 0,
      "remainingAmount": 0
    },
    {
      "departmentId": 6,
      "departmentName": "友好并网与新型配网技术研究所",
      "quotaAmount": 12,
      "usedAmount": 0,
      "remainingAmount": 0
    }
  ]
}
```

### 修改后的逻辑
只返回当前组织ID对应的信息。

## 修改内容

### 1. 修改函数签名

**修改前**：
```go
func (s *AssessmentDataService) getBonusInfoByIds(ctx context.Context, bonusIds map[int]bool) (assessmentReq.BonusInfo, error)
func (s *AssessmentDataService) getQuotaInfoByIds(ctx context.Context, quotaIds map[int]bool) (assessmentReq.QuotaInfo, error)
```

**修改后**：
```go
func (s *AssessmentDataService) getBonusInfoByIds(ctx context.Context, bonusIds map[int]bool, orgId int) (assessmentReq.BonusInfo, error)
func (s *AssessmentDataService) getQuotaInfoByIds(ctx context.Context, quotaIds map[int]bool, orgId int) (assessmentReq.QuotaInfo, error)
```

### 2. 添加组织ID过滤逻辑

**奖金信息过滤**：
```go
// 解析部门分配JSON
var allAllocations []assessmentReq.DepartmentAllocation
if bonus.DepartmentAllocations != nil {
    err = json.Unmarshal(bonus.DepartmentAllocations, &allAllocations)
    if err != nil {
        return bonusInfo, err
    }
}

// 只返回当前组织ID对应的分配信息
var filteredAllocations []assessmentReq.DepartmentAllocation
var totalAmount float64
for _, alloc := range allAllocations {
    if alloc.DepartmentId == orgId {
        filteredAllocations = append(filteredAllocations, alloc)
        totalAmount += alloc.AllocatedAmount
    }
}

bonusInfo.DepartmentAllocations = filteredAllocations
bonusInfo.TotalAmount = totalAmount
```

**配额信息过滤**：
```go
// 解析部门配额JSON
var allQuotas []assessmentReq.DepartmentQuota
if quota.DepartmentQuotas != nil {
    err = json.Unmarshal(quota.DepartmentQuotas, &allQuotas)
    if err != nil {
        return quotaInfo, err
    }
}

// 只返回当前组织ID对应的配额信息
var filteredQuotas []assessmentReq.DepartmentQuota
for _, q := range allQuotas {
    if q.DepartmentId == orgId {
        filteredQuotas = append(filteredQuotas, q)
    }
}

quotaInfo.DepartmentQuotas = filteredQuotas
```

### 3. 更新调用点

```go
// 获取奖金信息（基于考核配置中的关联ID，只返回当前组织的信息）
bonusInfo, err := s.getBonusInfoByIds(ctx, bonusIds, orgId)

// 获取配额信息（基于考核配置中的关联ID，只返回当前组织的信息）
quotaInfo, err := s.getQuotaInfoByIds(ctx, quotaIds, orgId)
```

## 预期效果

### 当 orgId = 5 时

**奖金信息**：
```json
{
  "id": 2,
  "bonusName": "第三季度奖金",
  "totalAmount": 120,
  "departmentAllocations": [
    {
      "departmentId": 5,
      "departmentName": "数字能源智联技术研究所",
      "allocatedAmount": 120,
      "usedAmount": 0,
      "remainingAmount": 120
    }
  ]
}
```

**配额信息**：
```json
{
  "id": 6,
  "quotaName": "2025",
  "departmentQuotas": [
    {
      "departmentId": 5,
      "departmentName": "数字能源智联技术研究所",
      "quotaAmount": 12,
      "usedAmount": 0,
      "remainingAmount": 0
    }
  ]
}
```

### 当 orgId = 6 时

**奖金信息**：
```json
{
  "id": 2,
  "bonusName": "第三季度奖金",
  "totalAmount": 130,
  "departmentAllocations": [
    {
      "departmentId": 6,
      "departmentName": "友好并网与新型配网技术研究所",
      "allocatedAmount": 130,
      "usedAmount": 0,
      "remainingAmount": 130
    }
  ]
}
```

**配额信息**：
```json
{
  "id": 6,
  "quotaName": "2025",
  "departmentQuotas": [
    {
      "departmentId": 6,
      "departmentName": "友好并网与新型配网技术研究所",
      "quotaAmount": 12,
      "usedAmount": 0,
      "remainingAmount": 0
    }
  ]
}
```

## 业务优势

1. **数据安全性**：用户只能看到自己组织的奖金和配额信息
2. **性能优化**：减少不必要的数据传输
3. **权限控制**：符合组织权限管理的原则
4. **用户体验**：界面更简洁，只显示相关信息

## 测试验证

1. **重启后端服务**
2. **使用不同组织的用户登录**
3. **验证返回的奖金和配额信息**：
   - 只包含当前用户组织的信息
   - 不包含其他组织的信息
   - totalAmount 只计算当前组织的金额

这个修改确保了数据的安全性和相关性，用户只能看到与自己组织相关的奖金和配额信息。
