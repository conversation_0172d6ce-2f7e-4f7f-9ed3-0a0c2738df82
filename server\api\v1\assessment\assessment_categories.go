package assessment

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AssessmentCategoriesApi struct {}



// CreateAssessmentCategories 创建考核类型
// @Tags AssessmentCategories
// @Summary 创建考核类型
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentCategories true "创建考核类型"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /assessmentCategories/createAssessmentCategories [post]
func (assessmentCategoriesApi *AssessmentCategoriesApi) CreateAssessmentCategories(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var assessmentCategories assessment.AssessmentCategories
	err := c.ShouldBindJSON(&assessmentCategories)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = assessmentCategoriesService.CreateAssessmentCategories(ctx,&assessmentCategories)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAssessmentCategories 删除考核类型
// @Tags AssessmentCategories
// @Summary 删除考核类型
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentCategories true "删除考核类型"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /assessmentCategories/deleteAssessmentCategories [delete]
func (assessmentCategoriesApi *AssessmentCategoriesApi) DeleteAssessmentCategories(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := assessmentCategoriesService.DeleteAssessmentCategories(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAssessmentCategoriesByIds 批量删除考核类型
// @Tags AssessmentCategories
// @Summary 批量删除考核类型
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /assessmentCategories/deleteAssessmentCategoriesByIds [delete]
func (assessmentCategoriesApi *AssessmentCategoriesApi) DeleteAssessmentCategoriesByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := assessmentCategoriesService.DeleteAssessmentCategoriesByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAssessmentCategories 更新考核类型
// @Tags AssessmentCategories
// @Summary 更新考核类型
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentCategories true "更新考核类型"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /assessmentCategories/updateAssessmentCategories [put]
func (assessmentCategoriesApi *AssessmentCategoriesApi) UpdateAssessmentCategories(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var assessmentCategories assessment.AssessmentCategories
	err := c.ShouldBindJSON(&assessmentCategories)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = assessmentCategoriesService.UpdateAssessmentCategories(ctx,assessmentCategories)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAssessmentCategories 用id查询考核类型
// @Tags AssessmentCategories
// @Summary 用id查询考核类型
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询考核类型"
// @Success 200 {object} response.Response{data=assessment.AssessmentCategories,msg=string} "查询成功"
// @Router /assessmentCategories/findAssessmentCategories [get]
func (assessmentCategoriesApi *AssessmentCategoriesApi) FindAssessmentCategories(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	reassessmentCategories, err := assessmentCategoriesService.GetAssessmentCategories(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(reassessmentCategories, c)
}
// GetAssessmentCategoriesList 分页获取考核类型列表
// @Tags AssessmentCategories
// @Summary 分页获取考核类型列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.AssessmentCategoriesSearch true "分页获取考核类型列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /assessmentCategories/getAssessmentCategoriesList [get]
func (assessmentCategoriesApi *AssessmentCategoriesApi) GetAssessmentCategoriesList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo assessmentReq.AssessmentCategoriesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := assessmentCategoriesService.GetAssessmentCategoriesInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetAssessmentCategoriesPublic 不需要鉴权的考核类型接口
// @Tags AssessmentCategories
// @Summary 不需要鉴权的考核类型接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentCategories/getAssessmentCategoriesPublic [get]
func (assessmentCategoriesApi *AssessmentCategoriesApi) GetAssessmentCategoriesPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    assessmentCategoriesService.GetAssessmentCategoriesPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的考核类型接口信息",
    }, "获取成功", c)
}
