package assessment

import (
	"strconv"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AssessmentCoefficientApi struct{}

// ReplaceAssessmentCoefficients 替换考核系数
// @Tags AssessmentCoefficient
// @Summary 替换考核系数
// @Description 先删除指定考核配置的所有系数记录，然后插入新的系数记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body assessmentReq.ReplaceAssessmentCoefficientsRequest true "考核系数数据"
// @Success 200 {object} response.Response{msg=string} "替换成功"
// @Router /assessment/coefficients/replace [post]
func (a *AssessmentCoefficientApi) ReplaceAssessmentCoefficients(c *gin.Context) {
	var req assessmentReq.ReplaceAssessmentCoefficientsRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		global.GVA_LOG.Error("参数绑定失败", zap.Error(err))
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 基本参数验证
	if req.AssessmentConfigId == 0 {
		response.FailWithMessage("考核配置ID不能为空", c)
		return
	}

	// 记录操作日志
	global.GVA_LOG.Info("开始替换考核系数", 
		zap.Uint("assessmentConfigId", req.AssessmentConfigId),
		zap.Int("recordCount", len(req.Records)))

	// 调用服务层
	assessmentCoefficientService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentCoefficientService
	if err := assessmentCoefficientService.ReplaceAssessmentCoefficients(req); err != nil {
		global.GVA_LOG.Error("替换考核系数失败", 
			zap.Uint("assessmentConfigId", req.AssessmentConfigId),
			zap.Error(err))
		response.FailWithMessage("提交失败: "+err.Error(), c)
		return
	}

	global.GVA_LOG.Info("替换考核系数成功", 
		zap.Uint("assessmentConfigId", req.AssessmentConfigId),
		zap.Int("recordCount", len(req.Records)))

	response.OkWithMessage("考核系数提交成功", c)
}

// GetAssessmentCoefficients 获取考核系数
// @Tags AssessmentCoefficient
// @Summary 获取考核系数
// @Description 根据考核配置ID获取所有考核系数记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param configId path int true "考核配置ID"
// @Success 200 {object} response.Response{data=[]model.ProjectAssessmentScore} "获取成功"
// @Router /assessment/coefficients/{configId} [get]
func (a *AssessmentCoefficientApi) GetAssessmentCoefficients(c *gin.Context) {
	configIdStr := c.Param("configId")
	configId, err := strconv.ParseUint(configIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误: 考核配置ID必须是数字", c)
		return
	}

	// 调用服务层
	assessmentCoefficientService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentCoefficientService
	coefficients, err := assessmentCoefficientService.GetAssessmentCoefficients(uint(configId))
	if err != nil {
		global.GVA_LOG.Error("获取考核系数失败", 
			zap.Uint("assessmentConfigId", uint(configId)),
			zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"coefficients": coefficients,
		"total": len(coefficients),
	}, "获取成功", c)
}

// ValidateAssessmentCoefficients 校验考核系数总和
// @Tags AssessmentCoefficient
// @Summary 校验考核系数总和
// @Description 校验指定考核配置中每个用户的系数总和是否为100%
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param configId path int true "考核配置ID"
// @Success 200 {object} response.Response{data=object} "校验结果"
// @Router /assessment/coefficients/{configId}/validate [get]
func (a *AssessmentCoefficientApi) ValidateAssessmentCoefficients(c *gin.Context) {
	configIdStr := c.Param("configId")
	configId, err := strconv.ParseUint(configIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误: 考核配置ID必须是数字", c)
		return
	}

	// 调用服务层
	assessmentCoefficientService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentCoefficientService
	validationResult, err := assessmentCoefficientService.ValidateCoefficientsSum(uint(configId))
	if err != nil {
		global.GVA_LOG.Error("校验考核系数失败", 
			zap.Uint("assessmentConfigId", uint(configId)),
			zap.Error(err))
		response.FailWithMessage("校验失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(validationResult, "校验完成", c)
}

// ExportAssessmentCoefficients 导出考核系数分配Excel
// @Tags AssessmentCoefficient
// @Summary 导出考核系数分配Excel
// @Description 导出指定考核配置的系数分配表格为Excel文件
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/octet-stream
// @Param configId path int true "考核配置ID"
// @Success 200 {file} file "Excel文件"
// @Router /assessment/coefficients/{configId}/export [get]
func (a *AssessmentCoefficientApi) ExportAssessmentCoefficients(c *gin.Context) {
	configIdStr := c.Param("configId")
	configId, err := strconv.ParseUint(configIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误: 考核配置ID必须是数字", c)
		return
	}

	// 调用服务层
	assessmentCoefficientService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentCoefficientService
	fileBytes, fileName, err := assessmentCoefficientService.ExportAssessmentCoefficients(uint(configId))
	if err != nil {
		global.GVA_LOG.Error("导出考核系数失败", 
			zap.Uint("assessmentConfigId", uint(configId)),
			zap.Error(err))
		response.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.Header("Content-Length", strconv.Itoa(len(fileBytes)))

	// 返回文件内容
	c.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileBytes)
}
