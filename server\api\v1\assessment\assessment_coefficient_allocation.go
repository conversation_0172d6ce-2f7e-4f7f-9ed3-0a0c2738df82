package assessment

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AssessmentCoefficientAllocationApi struct {}



// CreateAssessmentCoefficientAllocation 创建考核系数分配
// @Tags AssessmentCoefficientAllocation
// @Summary 创建考核系数分配
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentCoefficientAllocation true "创建考核系数分配"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /assessmentCoefficientAllocation/createAssessmentCoefficientAllocation [post]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) CreateAssessmentCoefficientAllocation(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var assessmentCoefficientAllocation assessment.AssessmentCoefficientAllocation
	err := c.ShouldBindJSON(&assessmentCoefficientAllocation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = assessmentCoefficientAllocationService.CreateAssessmentCoefficientAllocation(ctx,&assessmentCoefficientAllocation)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAssessmentCoefficientAllocation 删除考核系数分配
// @Tags AssessmentCoefficientAllocation
// @Summary 删除考核系数分配
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentCoefficientAllocation true "删除考核系数分配"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /assessmentCoefficientAllocation/deleteAssessmentCoefficientAllocation [delete]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) DeleteAssessmentCoefficientAllocation(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := assessmentCoefficientAllocationService.DeleteAssessmentCoefficientAllocation(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAssessmentCoefficientAllocationByIds 批量删除考核系数分配
// @Tags AssessmentCoefficientAllocation
// @Summary 批量删除考核系数分配
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /assessmentCoefficientAllocation/deleteAssessmentCoefficientAllocationByIds [delete]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) DeleteAssessmentCoefficientAllocationByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := assessmentCoefficientAllocationService.DeleteAssessmentCoefficientAllocationByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAssessmentCoefficientAllocation 更新考核系数分配
// @Tags AssessmentCoefficientAllocation
// @Summary 更新考核系数分配
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentCoefficientAllocation true "更新考核系数分配"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /assessmentCoefficientAllocation/updateAssessmentCoefficientAllocation [put]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) UpdateAssessmentCoefficientAllocation(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var assessmentCoefficientAllocation assessment.AssessmentCoefficientAllocation
	err := c.ShouldBindJSON(&assessmentCoefficientAllocation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = assessmentCoefficientAllocationService.UpdateAssessmentCoefficientAllocation(ctx,assessmentCoefficientAllocation)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAssessmentCoefficientAllocation 用id查询考核系数分配
// @Tags AssessmentCoefficientAllocation
// @Summary 用id查询考核系数分配
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询考核系数分配"
// @Success 200 {object} response.Response{data=assessment.AssessmentCoefficientAllocation,msg=string} "查询成功"
// @Router /assessmentCoefficientAllocation/findAssessmentCoefficientAllocation [get]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) FindAssessmentCoefficientAllocation(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	reassessmentCoefficientAllocation, err := assessmentCoefficientAllocationService.GetAssessmentCoefficientAllocation(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(reassessmentCoefficientAllocation, c)
}
// GetAssessmentCoefficientAllocationList 分页获取考核系数分配列表
// @Tags AssessmentCoefficientAllocation
// @Summary 分页获取考核系数分配列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.AssessmentCoefficientAllocationSearch true "分页获取考核系数分配列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /assessmentCoefficientAllocation/getAssessmentCoefficientAllocationList [get]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) GetAssessmentCoefficientAllocationList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo assessmentReq.AssessmentCoefficientAllocationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := assessmentCoefficientAllocationService.GetAssessmentCoefficientAllocationInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetAssessmentCoefficientAllocationPublic 不需要鉴权的考核系数分配接口
// @Tags AssessmentCoefficientAllocation
// @Summary 不需要鉴权的考核系数分配接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentCoefficientAllocation/getAssessmentCoefficientAllocationPublic [get]
func (assessmentCoefficientAllocationApi *AssessmentCoefficientAllocationApi) GetAssessmentCoefficientAllocationPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    assessmentCoefficientAllocationService.GetAssessmentCoefficientAllocationPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的考核系数分配接口信息",
    }, "获取成功", c)
}
