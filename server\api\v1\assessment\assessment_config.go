package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AssessmentConfigApi struct{}

// CreateAssessmentConfig 创建assessmentConfig表
// @Tags AssessmentConfig
// @Summary 创建assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentConfig true "创建assessmentConfig表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /assessmentConfig/createAssessmentConfig [post]
func (assessmentConfigApi *AssessmentConfigApi) CreateAssessmentConfig(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var assessmentConfig assessment.AssessmentConfig
	err := c.ShouldBindJSON(&assessmentConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = assessmentConfigService.CreateAssessmentConfig(ctx, &assessmentConfig)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAssessmentConfig 删除assessmentConfig表
// @Tags AssessmentConfig
// @Summary 删除assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentConfig true "删除assessmentConfig表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /assessmentConfig/deleteAssessmentConfig [delete]
func (assessmentConfigApi *AssessmentConfigApi) DeleteAssessmentConfig(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := assessmentConfigService.DeleteAssessmentConfig(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAssessmentConfigByIds 批量删除assessmentConfig表
// @Tags AssessmentConfig
// @Summary 批量删除assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /assessmentConfig/deleteAssessmentConfigByIds [delete]
func (assessmentConfigApi *AssessmentConfigApi) DeleteAssessmentConfigByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := assessmentConfigService.DeleteAssessmentConfigByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAssessmentConfig 更新assessmentConfig表
// @Tags AssessmentConfig
// @Summary 更新assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.AssessmentConfig true "更新assessmentConfig表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /assessmentConfig/updateAssessmentConfig [put]
func (assessmentConfigApi *AssessmentConfigApi) UpdateAssessmentConfig(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var assessmentConfig assessment.AssessmentConfig
	err := c.ShouldBindJSON(&assessmentConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = assessmentConfigService.UpdateAssessmentConfig(ctx, assessmentConfig)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAssessmentConfig 用id查询assessmentConfig表
// @Tags AssessmentConfig
// @Summary 用id查询assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询assessmentConfig表"
// @Success 200 {object} response.Response{data=assessment.AssessmentConfig,msg=string} "查询成功"
// @Router /assessmentConfig/findAssessmentConfig [get]
func (assessmentConfigApi *AssessmentConfigApi) FindAssessmentConfig(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reassessmentConfig, err := assessmentConfigService.GetAssessmentConfig(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reassessmentConfig, c)
}

// FindAssessmentConfigWithNames 用id查询包含关联名称的考核配置
// @Tags AssessmentConfig
// @Summary 用id查询包含关联名称的考核配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询考核配置"
// @Success 200 {object} response.Response{data=assessmentReq.AssessmentConfigWithNames,msg=string} "查询成功"
// @Router /assessmentConfig/findAssessmentConfigWithNames [get]
func (assessmentConfigApi *AssessmentConfigApi) FindAssessmentConfigWithNames(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	result, err := assessmentConfigService.GetAssessmentConfigWithNames(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// GetAssessmentConfigList 分页获取assessmentConfig表列表
// @Tags AssessmentConfig
// @Summary 分页获取assessmentConfig表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.AssessmentConfigSearch true "分页获取assessmentConfig表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /assessmentConfig/getAssessmentConfigList [get]
func (assessmentConfigApi *AssessmentConfigApi) GetAssessmentConfigList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.AssessmentConfigSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := assessmentConfigService.GetAssessmentConfigInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAssessmentConfigWithNamesList 分页获取包含关联名称的考核配置列表
// @Tags AssessmentConfig
// @Summary 分页获取包含关联名称的考核配置列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.AssessmentConfigSearch true "分页获取考核配置列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /assessmentConfig/getAssessmentConfigWithNamesList [get]
func (assessmentConfigApi *AssessmentConfigApi) GetAssessmentConfigWithNamesList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.AssessmentConfigSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := assessmentConfigService.GetAssessmentConfigWithNamesList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAssessmentConfigPublic 不需要鉴权的assessmentConfig表接口
// @Tags AssessmentConfig
// @Summary 不需要鉴权的assessmentConfig表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentConfig/getAssessmentConfigPublic [get]
func (assessmentConfigApi *AssessmentConfigApi) GetAssessmentConfigPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	assessmentConfigService.GetAssessmentConfigPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的assessmentConfig表接口信息",
	}, "获取成功", c)
}
