package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AssessmentDataApi struct{}

// GetAssessmentData 获取考核数据
// @Tags AssessmentData
// @Summary 获取考核数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetAssessmentDataRequest true "获取考核数据请求"
// @Success 200 {object} response.Response{data=request.AssessmentDataResponse,msg=string} "获取成功"
// @Router /assessment/getAssessmentData [post]
func (a *AssessmentDataApi) GetAssessmentData(c *gin.Context) {
	var req request.GetAssessmentDataRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 参数验证
	if req.OrgId <= 0 {
		response.FailWithMessage("组织ID不能为空", c)
		return
	}

	if req.UserName == "" {
		response.FailWithMessage("用户名不能为空", c)
		return
	}

	// 调用服务层获取数据
	result, err := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService.GetAssessmentData(c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取考核数据失败!", zap.Error(err))
		response.FailWithMessage("获取考核数据失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetUserParameterScores 获取用户参数评分数据
// @Tags AssessmentData
// @Summary 获取用户参数评分数据（支持批量查询）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetUserParameterScoresRequest true "获取用户参数评分请求"
// @Success 200 {object} response.Response{data=request.GetUserParameterScoresResponse,msg=string} "获取成功"
// @Router /assessmentData/getUserParameterScores [post]
func (a *AssessmentDataApi) GetUserParameterScores(c *gin.Context) {
	var req request.GetUserParameterScoresRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 参数验证
	if len(req.UserNames) == 0 {
		response.FailWithMessage("用户名列表不能为空", c)
		return
	}

	// 限制批量查询的用户数量，防止性能问题
	if len(req.UserNames) > 100 {
		response.FailWithMessage("批量查询用户数量不能超过100个", c)
		return
	}

	// 调用服务层获取数据
	result, err := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService.GetUserParameterScores(c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取用户参数评分失败!", zap.Error(err))
		response.FailWithMessage("获取用户参数评分失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetEvaluationDetailsByConfig 根据考核配置获取评价详情
// @Tags AssessmentData
// @Summary 根据考核配置获取评价详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetEvaluationDetailsByConfigRequest true "获取评价详情请求"
// @Success 200 {object} response.Response{data=request.GetEvaluationDetailsByConfigResponse,msg=string} "获取成功"
// @Router /assessmentData/getEvaluationDetailsByConfig [post]
func (a *AssessmentDataApi) GetEvaluationDetailsByConfig(c *gin.Context) {
	var req request.GetEvaluationDetailsByConfigRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 参数验证
	if req.AssessmentConfigId <= 0 {
		response.FailWithMessage("考核配置ID不能为空", c)
		return
	}

	// 调用服务层获取数据
	result, err := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService.GetEvaluationDetailsByConfig(c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取评价详情失败!", zap.Error(err))
		response.FailWithMessage("获取评价详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetEvaluationDetailsByConfigId 根据考核配置ID获取评估详情
// @Tags AssessmentData
// @Summary 根据考核配置ID获取评估详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.EvaluationDetailRequest true "评估详情请求"
// @Success 200 {object} response.Response{data=request.EvaluationDetailResponse,msg=string} "获取成功"
// @Router /assessment/getEvaluationDetailsByConfigId [post]
func (a *AssessmentDataApi) GetEvaluationDetailsByConfigId(c *gin.Context) {
	var req request.EvaluationDetailRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 调用服务层获取数据
	result, err := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService.GetEvaluationDetailsByConfigId(c.Request.Context(), req.AssessmentConfigId)
	if err != nil {
		global.GVA_LOG.Error("获取评估详情失败!", zap.Error(err))
		response.FailWithMessage("获取评估详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取成功", c)
}
