package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BonusManagementApi struct{}

// CreateBonusManagement 创建奖金管理
// @Tags BonusManagement
// @Summary 创建奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.BonusManagement true "创建奖金管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /bonusManagement/createBonusManagement [post]
func (bonusManagementApinp *BonusManagementApi) CreateBonusManagement(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var bonusManagement assessment.BonusManagement
	err := c.ShouldBindJSON(&bonusManagement)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = bonusManagementService.CreateBonusManagement(ctx, &bonusManagement)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteBonusManagement 删除奖金管理
// @Tags BonusManagement
// @Summary 删除奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.BonusManagement true "删除奖金管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /bonusManagement/deleteBonusManagement [delete]
func (bonusManagementApi *BonusManagementApi) DeleteBonusManagement(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := bonusManagementService.DeleteBonusManagement(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteBonusManagementByIds 批量删除奖金管理
// @Tags BonusManagement
// @Summary 批量删除奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /bonusManagement/deleteBonusManagementByIds [delete]
func (bonusManagementApi *BonusManagementApi) DeleteBonusManagementByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := bonusManagementService.DeleteBonusManagementByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateBonusManagement 更新奖金管理
// @Tags BonusManagement
// @Summary 更新奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.BonusManagement true "更新奖金管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /bonusManagement/updateBonusManagement [put]
func (bonusManagementApi *BonusManagementApi) UpdateBonusManagement(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var bonusManagement assessment.BonusManagement
	err := c.ShouldBindJSON(&bonusManagement)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = bonusManagementService.UpdateBonusManagement(ctx, bonusManagement)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindBonusManagement 用id查询奖金管理
// @Tags BonusManagement
// @Summary 用id查询奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询奖金管理"
// @Success 200 {object} response.Response{data=assessment.BonusManagement,msg=string} "查询成功"
// @Router /bonusManagement/findBonusManagement [get]
func (bonusManagementApi *BonusManagementApi) FindBonusManagement(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	rebonusManagement, err := bonusManagementService.GetBonusManagement(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(rebonusManagement, c)
}

// GetBonusManagementList 分页获取奖金管理列表
// @Tags BonusManagement
// @Summary 分页获取奖金管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.BonusManagementSearch true "分页获取奖金管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /bonusManagement/getBonusManagementList [get]
func (bonusManagementApi *BonusManagementApi) GetBonusManagementList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.BonusManagementSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := bonusManagementService.GetBonusManagementInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetBonusManagementPublic 不需要鉴权的奖金管理接口
// @Tags BonusManagement
// @Summary 不需要鉴权的奖金管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /bonusManagement/getBonusManagementPublic [get]
func (bonusManagementApi *BonusManagementApi) GetBonusManagementPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	bonusManagementService.GetBonusManagementPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的奖金管理接口信息",
	}, "获取成功", c)
}
