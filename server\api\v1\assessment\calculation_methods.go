package assessment

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	assessmentService "github.com/flipped-aurora/gin-vue-admin/server/service/assessment"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CalculationMethodsApi struct{}

// CreateCalculationMethods 创建计算方法及其关联关系
// @Tags CalculationMethods
// @Summary 创建计算方法及其关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessmentReq.CreateCalculationMethodWithRelations true "创建计算方法及其关联关系"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /calculationMethods/createCalculationMethods [post]
func (calculationMethodsApi *CalculationMethodsApi) CreateCalculationMethods(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req assessmentReq.CreateCalculationMethodWithRelations
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = calculationMethodsService.CreateCalculationMethodWithRelations(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteCalculationMethods 删除calculationMethods表
// @Tags CalculationMethods
// @Summary 删除calculationMethods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.CalculationMethods true "删除calculationMethods表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /calculationMethods/deleteCalculationMethods [delete]
func (calculationMethodsApi *CalculationMethodsApi) DeleteCalculationMethods(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := calculationMethodsService.DeleteCalculationMethods(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCalculationMethodsByIds 批量删除calculationMethods表
// @Tags CalculationMethods
// @Summary 批量删除calculationMethods表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /calculationMethods/deleteCalculationMethodsByIds [delete]
func (calculationMethodsApi *CalculationMethodsApi) DeleteCalculationMethodsByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := calculationMethodsService.DeleteCalculationMethodsByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCalculationMethods 更新计算方法及其关联关系
// @Tags CalculationMethods
// @Summary 更新计算方法及其关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessmentReq.UpdateCalculationMethodWithRelations true "更新计算方法及其关联关系"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /calculationMethods/updateCalculationMethods [put]
func (calculationMethodsApi *CalculationMethodsApi) UpdateCalculationMethods(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var req assessmentReq.UpdateCalculationMethodWithRelations
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = calculationMethodsService.UpdateCalculationMethodWithRelations(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCalculationMethods 用id查询计算方法及其关联关系
// @Tags CalculationMethods
// @Summary 用id查询计算方法及其关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询计算方法及其关联关系"
// @Success 200 {object} response.Response{data=assessmentReq.CalculationMethodWithRelationsResponse,msg=string} "查询成功"
// @Router /calculationMethods/findCalculationMethods [get]
func (calculationMethodsApi *CalculationMethodsApi) FindCalculationMethods(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithMessage("无效的ID参数", c)
		return
	}

	result, err := calculationMethodsService.GetCalculationMethodWithRelations(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(result, "查询成功", c)
}

// GetCalculationMethodsList 分页获取calculationMethods表列表
// @Tags CalculationMethods
// @Summary 分页获取calculationMethods表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.CalculationMethodsSearch true "分页获取calculationMethods表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /calculationMethods/getCalculationMethodsList [get]
func (calculationMethodsApi *CalculationMethodsApi) GetCalculationMethodsList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.CalculationMethodsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := calculationMethodsService.GetCalculationMethodsInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetCalculationMethodsPublic 不需要鉴权的calculationMethods表接口
// @Tags CalculationMethods
// @Summary 不需要鉴权的calculationMethods表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /calculationMethods/getCalculationMethodsPublic [get]
func (calculationMethodsApi *CalculationMethodsApi) GetCalculationMethodsPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	calculationMethodsService.GetCalculationMethodsPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的calculationMethods表接口信息",
	}, "获取成功", c)
}

// CalculateByRule 根据规则计算结果
// @Tags CalculationMethods
// @Summary 根据规则计算结果
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessmentService.CalculateByRuleRequest true "规则计算请求"
// @Success 200 {object} response.Response{data=assessmentService.CalculateByRuleResponse,msg=string} "计算成功"
// @Router /calculationMethods/calculateByRule [post]
func (calculationMethodsApi *CalculationMethodsApi) CalculateByRule(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req assessmentService.CalculateByRuleRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	ruleEngineService := assessmentService.RuleEngineService{}
	result, err := ruleEngineService.CalculateByRule(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("规则计算失败!", zap.Error(err))
		response.FailWithMessage("规则计算失败:"+err.Error(), c)
		return
	}

	if !result.Success {
		response.FailWithDetailed(result, "规则计算失败", c)
		return
	}

	response.OkWithDetailed(result, "计算成功", c)
}

// TestRule 测试规则配置
// @Tags CalculationMethods
// @Summary 测试规则配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessmentService.TestRuleRequest true "规则测试请求"
// @Success 200 {object} response.Response{data=assessmentService.CalculateByRuleResponse,msg=string} "测试成功"
// @Router /calculationMethods/testRule [post]
func (calculationMethodsApi *CalculationMethodsApi) TestRule(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req assessmentService.TestRuleRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	ruleEngineService := assessmentService.RuleEngineService{}
	result, err := ruleEngineService.TestRule(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("规则测试失败!", zap.Error(err))
		response.FailWithMessage("规则测试失败:"+err.Error(), c)
		return
	}

	if !result.Success {
		response.FailWithDetailed(result, "规则测试失败", c)
		return
	}

	response.OkWithDetailed(result, "测试成功", c)
}
