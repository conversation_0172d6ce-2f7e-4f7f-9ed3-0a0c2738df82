package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CalculationParametersApi struct{}

// CreateCalculationParameters 创建计算参数表
// @Tags CalculationParameters
// @Summary 创建计算参数表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.CalculationParameters true "创建计算参数表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /calculationParameters/createCalculationParameters [post]
func (calculationParametersApi *CalculationParametersApi) CreateCalculationParameters(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var calculationParameters assessment.CalculationParameters
	err := c.ShouldBindJSON(&calculationParameters)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = calculationParametersService.CreateCalculationParameters(ctx, &calculationParameters)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteCalculationParameters 删除计算参数表
// @Tags CalculationParameters
// @Summary 删除计算参数表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.CalculationParameters true "删除计算参数表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /calculationParameters/deleteCalculationParameters [delete]
func (calculationParametersApi *CalculationParametersApi) DeleteCalculationParameters(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := calculationParametersService.DeleteCalculationParameters(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCalculationParametersByIds 批量删除计算参数表
// @Tags CalculationParameters
// @Summary 批量删除计算参数表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /calculationParameters/deleteCalculationParametersByIds [delete]
func (calculationParametersApi *CalculationParametersApi) DeleteCalculationParametersByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := calculationParametersService.DeleteCalculationParametersByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCalculationParameters 更新计算参数表
// @Tags CalculationParameters
// @Summary 更新计算参数表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.CalculationParameters true "更新计算参数表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /calculationParameters/updateCalculationParameters [put]
func (calculationParametersApi *CalculationParametersApi) UpdateCalculationParameters(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var calculationParameters assessment.CalculationParameters
	err := c.ShouldBindJSON(&calculationParameters)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = calculationParametersService.UpdateCalculationParameters(ctx, calculationParameters)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCalculationParameters 用id查询计算参数表
// @Tags CalculationParameters
// @Summary 用id查询计算参数表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询计算参数表"
// @Success 200 {object} response.Response{data=assessment.CalculationParameters,msg=string} "查询成功"
// @Router /calculationParameters/findCalculationParameters [get]
func (calculationParametersApi *CalculationParametersApi) FindCalculationParameters(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	recalculationParameters, err := calculationParametersService.GetCalculationParameters(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(recalculationParameters, c)
}

// GetCalculationParametersList 分页获取计算参数表列表
// @Tags CalculationParameters
// @Summary 分页获取计算参数表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.CalculationParametersSearch true "分页获取计算参数表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /calculationParameters/getCalculationParametersList [get]
func (calculationParametersApi *CalculationParametersApi) GetCalculationParametersList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.CalculationParametersSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := calculationParametersService.GetCalculationParametersInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetCalculationParametersWithRoleNameList 分页获取包含角色名称的计算参数表列表
// @Tags CalculationParameters
// @Summary 分页获取包含角色名称的计算参数表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.CalculationParametersSearch true "分页获取计算参数表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /calculationParameters/getCalculationParametersWithRoleNameList [get]
func (calculationParametersApi *CalculationParametersApi) GetCalculationParametersWithRoleNameList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.CalculationParametersSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := calculationParametersService.GetCalculationParametersWithRoleNameList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// CheckParameterUsage 检查参数是否被未归档的考核配置使用
// @Tags CalculationParameters
// @Summary 检查参数是否被未归档的考核配置使用
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "参数ID"
// @Success 200 {object} response.Response{data=object,msg=string} "检查成功"
// @Router /calculationParameters/checkParameterUsage [get]
func (calculationParametersApi *CalculationParametersApi) CheckParameterUsage(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	if id == "" {
		response.FailWithMessage("参数ID不能为空", c)
		return
	}

	usageInfo, err := calculationParametersService.CheckParameterUsage(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("检查参数使用情况失败!", zap.Error(err))
		response.FailWithMessage("检查失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(usageInfo, "检查成功", c)
}

// GetCalculationParametersPublic 不需要鉴权的计算参数表接口
// @Tags CalculationParameters
// @Summary 不需要鉴权的计算参数表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /calculationParameters/getCalculationParametersPublic [get]
func (calculationParametersApi *CalculationParametersApi) GetCalculationParametersPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	calculationParametersService.GetCalculationParametersPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的计算参数表接口信息",
	}, "获取成功", c)
}
