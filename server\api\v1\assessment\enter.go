package assessment

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	AssessmentConfigApi
	BonusManagementApi
	ScoreQuotaManagementApi
	AssessmentCategoriesApi
	CalculationParametersApi
	CalculationMethodsApi
	MethodParameterRelationsApi
	MethodUserAssignmentsApi
	AssessmentDataApi
	AssessmentCoefficientApi
	ProjectManagerScoreApi
	AssessmentCoefficientAllocationApi
}

var (
	assessmentConfigService                = service.ServiceGroupApp.AssessmentServiceGroup.AssessmentConfigService
	bonusManagementService                 = service.ServiceGroupApp.AssessmentServiceGroup.BonusManagementService
	scoreQuotaManagementService            = service.ServiceGroupApp.AssessmentServiceGroup.ScoreQuotaManagementService
	assessmentCategoriesService            = service.ServiceGroupApp.AssessmentServiceGroup.AssessmentCategoriesService
	calculationParametersService           = service.ServiceGroupApp.AssessmentServiceGroup.CalculationParametersService
	calculationMethodsService              = service.ServiceGroupApp.AssessmentServiceGroup.CalculationMethodsService
	methodParameterRelationsService        = service.ServiceGroupApp.AssessmentServiceGroup.MethodParameterRelationsService
	methodUserAssignmentsService           = service.ServiceGroupApp.AssessmentServiceGroup.MethodUserAssignmentsService
	assessmentDataService                  = service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService
	projectManagerScoreService             = service.ServiceGroupApp.AssessmentServiceGroup.ProjectManagerScoreService
	assessmentCoefficientAllocationService = service.ServiceGroupApp.AssessmentServiceGroup.AssessmentCoefficientAllocationService
)
