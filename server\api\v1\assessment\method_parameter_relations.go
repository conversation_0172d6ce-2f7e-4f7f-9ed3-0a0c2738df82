package assessment

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type MethodParameterRelationsApi struct {}



// CreateMethodParameterRelations 创建关联关系
// @Tags MethodParameterRelations
// @Summary 创建关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.MethodParameterRelations true "创建关联关系"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /methodParameterRelations/createMethodParameterRelations [post]
func (methodParameterRelationsApi *MethodParameterRelationsApi) CreateMethodParameterRelations(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var methodParameterRelations assessment.MethodParameterRelations
	err := c.ShouldBindJSON(&methodParameterRelations)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = methodParameterRelationsService.CreateMethodParameterRelations(ctx,&methodParameterRelations)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteMethodParameterRelations 删除关联关系
// @Tags MethodParameterRelations
// @Summary 删除关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.MethodParameterRelations true "删除关联关系"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /methodParameterRelations/deleteMethodParameterRelations [delete]
func (methodParameterRelationsApi *MethodParameterRelationsApi) DeleteMethodParameterRelations(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := methodParameterRelationsService.DeleteMethodParameterRelations(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteMethodParameterRelationsByIds 批量删除关联关系
// @Tags MethodParameterRelations
// @Summary 批量删除关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /methodParameterRelations/deleteMethodParameterRelationsByIds [delete]
func (methodParameterRelationsApi *MethodParameterRelationsApi) DeleteMethodParameterRelationsByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := methodParameterRelationsService.DeleteMethodParameterRelationsByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateMethodParameterRelations 更新关联关系
// @Tags MethodParameterRelations
// @Summary 更新关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.MethodParameterRelations true "更新关联关系"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /methodParameterRelations/updateMethodParameterRelations [put]
func (methodParameterRelationsApi *MethodParameterRelationsApi) UpdateMethodParameterRelations(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var methodParameterRelations assessment.MethodParameterRelations
	err := c.ShouldBindJSON(&methodParameterRelations)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = methodParameterRelationsService.UpdateMethodParameterRelations(ctx,methodParameterRelations)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindMethodParameterRelations 用id查询关联关系
// @Tags MethodParameterRelations
// @Summary 用id查询关联关系
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询关联关系"
// @Success 200 {object} response.Response{data=assessment.MethodParameterRelations,msg=string} "查询成功"
// @Router /methodParameterRelations/findMethodParameterRelations [get]
func (methodParameterRelationsApi *MethodParameterRelationsApi) FindMethodParameterRelations(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	remethodParameterRelations, err := methodParameterRelationsService.GetMethodParameterRelations(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(remethodParameterRelations, c)
}
// GetMethodParameterRelationsList 分页获取关联关系列表
// @Tags MethodParameterRelations
// @Summary 分页获取关联关系列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.MethodParameterRelationsSearch true "分页获取关联关系列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /methodParameterRelations/getMethodParameterRelationsList [get]
func (methodParameterRelationsApi *MethodParameterRelationsApi) GetMethodParameterRelationsList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo assessmentReq.MethodParameterRelationsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := methodParameterRelationsService.GetMethodParameterRelationsInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetMethodParameterRelationsPublic 不需要鉴权的关联关系接口
// @Tags MethodParameterRelations
// @Summary 不需要鉴权的关联关系接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /methodParameterRelations/getMethodParameterRelationsPublic [get]
func (methodParameterRelationsApi *MethodParameterRelationsApi) GetMethodParameterRelationsPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    methodParameterRelationsService.GetMethodParameterRelationsPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的关联关系接口信息",
    }, "获取成功", c)
}
