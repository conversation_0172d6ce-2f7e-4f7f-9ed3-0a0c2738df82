package assessment

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type MethodUserAssignmentsApi struct {}



// CreateMethodUserAssignments 创建方法用户关联
// @Tags MethodUserAssignments
// @Summary 创建方法用户关联
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.MethodUserAssignments true "创建方法用户关联"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /methodUserAssignments/createMethodUserAssignments [post]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) CreateMethodUserAssignments(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var methodUserAssignments assessment.MethodUserAssignments
	err := c.ShouldBindJSON(&methodUserAssignments)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = methodUserAssignmentsService.CreateMethodUserAssignments(ctx,&methodUserAssignments)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteMethodUserAssignments 删除方法用户关联
// @Tags MethodUserAssignments
// @Summary 删除方法用户关联
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.MethodUserAssignments true "删除方法用户关联"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /methodUserAssignments/deleteMethodUserAssignments [delete]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) DeleteMethodUserAssignments(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := methodUserAssignmentsService.DeleteMethodUserAssignments(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteMethodUserAssignmentsByIds 批量删除方法用户关联
// @Tags MethodUserAssignments
// @Summary 批量删除方法用户关联
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /methodUserAssignments/deleteMethodUserAssignmentsByIds [delete]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) DeleteMethodUserAssignmentsByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := methodUserAssignmentsService.DeleteMethodUserAssignmentsByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateMethodUserAssignments 更新方法用户关联
// @Tags MethodUserAssignments
// @Summary 更新方法用户关联
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.MethodUserAssignments true "更新方法用户关联"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /methodUserAssignments/updateMethodUserAssignments [put]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) UpdateMethodUserAssignments(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var methodUserAssignments assessment.MethodUserAssignments
	err := c.ShouldBindJSON(&methodUserAssignments)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = methodUserAssignmentsService.UpdateMethodUserAssignments(ctx,methodUserAssignments)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindMethodUserAssignments 用id查询方法用户关联
// @Tags MethodUserAssignments
// @Summary 用id查询方法用户关联
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询方法用户关联"
// @Success 200 {object} response.Response{data=assessment.MethodUserAssignments,msg=string} "查询成功"
// @Router /methodUserAssignments/findMethodUserAssignments [get]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) FindMethodUserAssignments(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	remethodUserAssignments, err := methodUserAssignmentsService.GetMethodUserAssignments(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(remethodUserAssignments, c)
}
// GetMethodUserAssignmentsList 分页获取方法用户关联列表
// @Tags MethodUserAssignments
// @Summary 分页获取方法用户关联列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.MethodUserAssignmentsSearch true "分页获取方法用户关联列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /methodUserAssignments/getMethodUserAssignmentsList [get]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) GetMethodUserAssignmentsList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo assessmentReq.MethodUserAssignmentsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := methodUserAssignmentsService.GetMethodUserAssignmentsInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetMethodUserAssignmentsPublic 不需要鉴权的方法用户关联接口
// @Tags MethodUserAssignments
// @Summary 不需要鉴权的方法用户关联接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /methodUserAssignments/getMethodUserAssignmentsPublic [get]
func (methodUserAssignmentsApi *MethodUserAssignmentsApi) GetMethodUserAssignmentsPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    methodUserAssignmentsService.GetMethodUserAssignmentsPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的方法用户关联接口信息",
    }, "获取成功", c)
}
