package assessment

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProjectManagerScoreApi struct{}

// CreateProjectManagerScore 创建项目负责人评分
// @Tags ProjectManagerScore
// @Summary 创建项目负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.ProjectManagerScore true "创建项目负责人评分"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projectManagerScore/createProjectManagerScore [post]
func (projectManagerScoreApi *ProjectManagerScoreApi) CreateProjectManagerScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var projectManagerScore assessment.ProjectManagerScore
	err := c.ShouldBindJSON(&projectManagerScore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectManagerScoreService.CreateProjectManagerScore(ctx, &projectManagerScore)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteProjectManagerScore 删除项目负责人评分
// @Tags ProjectManagerScore
// @Summary 删除项目负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.ProjectManagerScore true "删除项目负责人评分"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projectManagerScore/deleteProjectManagerScore [delete]
func (projectManagerScoreApi *ProjectManagerScoreApi) DeleteProjectManagerScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := projectManagerScoreService.DeleteProjectManagerScore(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteProjectManagerScoreByIds 批量删除项目负责人评分
// @Tags ProjectManagerScore
// @Summary 批量删除项目负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /projectManagerScore/deleteProjectManagerScoreByIds [delete]
func (projectManagerScoreApi *ProjectManagerScoreApi) DeleteProjectManagerScoreByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := projectManagerScoreService.DeleteProjectManagerScoreByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateProjectManagerScore 更新项目负责人评分
// @Tags ProjectManagerScore
// @Summary 更新项目负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.ProjectManagerScore true "更新项目负责人评分"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projectManagerScore/updateProjectManagerScore [put]
func (projectManagerScoreApi *ProjectManagerScoreApi) UpdateProjectManagerScore(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var projectManagerScore assessment.ProjectManagerScore
	err := c.ShouldBindJSON(&projectManagerScore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectManagerScoreService.UpdateProjectManagerScore(ctx, projectManagerScore)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindProjectManagerScore 用id查询项目负责人评分
// @Tags ProjectManagerScore
// @Summary 用id查询项目负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询项目负责人评分"
// @Success 200 {object} response.Response{data=assessment.ProjectManagerScore,msg=string} "查询成功"
// @Router /projectManagerScore/findProjectManagerScore [get]
func (projectManagerScoreApi *ProjectManagerScoreApi) FindProjectManagerScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reprojectManagerScore, err := projectManagerScoreService.GetProjectManagerScore(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reprojectManagerScore, c)
}

// GetProjectManagerScoreList 分页获取项目负责人评分列表
// @Tags ProjectManagerScore
// @Summary 分页获取项目负责人评分列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.ProjectManagerScoreSearch true "分页获取项目负责人评分列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projectManagerScore/getProjectManagerScoreList [get]
func (projectManagerScoreApi *ProjectManagerScoreApi) GetProjectManagerScoreList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo assessmentReq.ProjectManagerScoreSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := projectManagerScoreService.GetProjectManagerScoreInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetProjectManagerScorePublic 不需要鉴权的项目负责人评分接口
// @Tags ProjectManagerScore
// @Summary 不需要鉴权的项目负责人评分接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectManagerScore/getProjectManagerScorePublic [get]
func (projectManagerScoreApi *ProjectManagerScoreApi) GetProjectManagerScorePublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	projectManagerScoreService.GetProjectManagerScorePublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的项目负责人评分接口信息",
	}, "获取成功", c)
}

// BatchSubmitProjectMemberScores 批量提交项目成员评分
// @Tags ProjectManagerScore
// @Summary 批量提交项目成员评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body []assessmentReq.BatchSubmitProjectMemberScoresRequest true "批量提交项目成员评分数据"
// @Success 200 {object} response.Response{msg=string} "提交成功"
// @Router /projectManagerScore/batchSubmitProjectMemberScores [post]
func (projectManagerScoreApi *ProjectManagerScoreApi) BatchSubmitProjectMemberScores(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var scoreRequests []assessmentReq.BatchSubmitProjectMemberScoresRequest
	err := c.ShouldBindJSON(&scoreRequests)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(scoreRequests) == 0 {
		response.FailWithMessage("评分数据不能为空", c)
		return
	}

	err = projectManagerScoreService.BatchSubmitProjectMemberScores(ctx, scoreRequests)
	if err != nil {
		global.GVA_LOG.Error("批量提交项目成员评分失败!", zap.Error(err))
		response.FailWithMessage("提交失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage(fmt.Sprintf("成功提交 %d 条项目成员评分数据", len(scoreRequests)), c)
}
