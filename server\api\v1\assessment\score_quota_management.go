package assessment

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type ScoreQuotaManagementApi struct {}



// CreateScoreQuotaManagement 创建高分配额管理
// @Tags ScoreQuotaManagement
// @Summary 创建高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.ScoreQuotaManagement true "创建高分配额管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /scoreQuotaManagement/createScoreQuotaManagement [post]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) CreateScoreQuotaManagement(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var scoreQuotaManagement assessment.ScoreQuotaManagement
	err := c.ShouldBindJSON(&scoreQuotaManagement)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = scoreQuotaManagementService.CreateScoreQuotaManagement(ctx,&scoreQuotaManagement)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteScoreQuotaManagement 删除高分配额管理
// @Tags ScoreQuotaManagement
// @Summary 删除高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.ScoreQuotaManagement true "删除高分配额管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /scoreQuotaManagement/deleteScoreQuotaManagement [delete]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) DeleteScoreQuotaManagement(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := scoreQuotaManagementService.DeleteScoreQuotaManagement(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteScoreQuotaManagementByIds 批量删除高分配额管理
// @Tags ScoreQuotaManagement
// @Summary 批量删除高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /scoreQuotaManagement/deleteScoreQuotaManagementByIds [delete]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) DeleteScoreQuotaManagementByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := scoreQuotaManagementService.DeleteScoreQuotaManagementByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateScoreQuotaManagement 更新高分配额管理
// @Tags ScoreQuotaManagement
// @Summary 更新高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body assessment.ScoreQuotaManagement true "更新高分配额管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /scoreQuotaManagement/updateScoreQuotaManagement [put]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) UpdateScoreQuotaManagement(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var scoreQuotaManagement assessment.ScoreQuotaManagement
	err := c.ShouldBindJSON(&scoreQuotaManagement)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = scoreQuotaManagementService.UpdateScoreQuotaManagement(ctx,scoreQuotaManagement)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindScoreQuotaManagement 用id查询高分配额管理
// @Tags ScoreQuotaManagement
// @Summary 用id查询高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询高分配额管理"
// @Success 200 {object} response.Response{data=assessment.ScoreQuotaManagement,msg=string} "查询成功"
// @Router /scoreQuotaManagement/findScoreQuotaManagement [get]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) FindScoreQuotaManagement(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	rescoreQuotaManagement, err := scoreQuotaManagementService.GetScoreQuotaManagement(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(rescoreQuotaManagement, c)
}
// GetScoreQuotaManagementList 分页获取高分配额管理列表
// @Tags ScoreQuotaManagement
// @Summary 分页获取高分配额管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.ScoreQuotaManagementSearch true "分页获取高分配额管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /scoreQuotaManagement/getScoreQuotaManagementList [get]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) GetScoreQuotaManagementList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo assessmentReq.ScoreQuotaManagementSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := scoreQuotaManagementService.GetScoreQuotaManagementInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetScoreQuotaManagementPublic 不需要鉴权的高分配额管理接口
// @Tags ScoreQuotaManagement
// @Summary 不需要鉴权的高分配额管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /scoreQuotaManagement/getScoreQuotaManagementPublic [get]
func (scoreQuotaManagementApi *ScoreQuotaManagementApi) GetScoreQuotaManagementPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    scoreQuotaManagementService.GetScoreQuotaManagementPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的高分配额管理接口信息",
    }, "获取成功", c)
}
