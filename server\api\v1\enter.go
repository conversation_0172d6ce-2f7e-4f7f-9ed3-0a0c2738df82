package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/assessment"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/project"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/score"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
)

var ApiGroupApp = new(ApiGroup)

type ApiGroup struct {
	SystemApiGroup     system.ApiGroup
	ExampleApiGroup    example.ApiGroup
	ProjectApiGroup    project.ApiGroup
	AssessmentApiGroup assessment.ApiGroup
	ScoreApiGroup      score.ApiGroup
}
