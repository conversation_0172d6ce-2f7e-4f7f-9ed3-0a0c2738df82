package project

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/project"
	projectReq "github.com/flipped-aurora/gin-vue-admin/server/model/project/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProjectInfoApi struct{}

// CreateProjectInfo 创建项目信息表
// @Tags ProjectInfo
// @Summary 创建项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body project.ProjectInfo true "创建项目信息表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projectInfo/createProjectInfo [post]
func (projectInfoApi *ProjectInfoApi) CreateProjectInfo(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var projectInfo project.ProjectInfo
	err := c.ShouldBindJSON(&projectInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectInfoService.CreateProjectInfo(ctx, &projectInfo)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteProjectInfo 删除项目信息表
// @Tags ProjectInfo
// @Summary 删除项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body project.ProjectInfo true "删除项目信息表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projectInfo/deleteProjectInfo [delete]
func (projectInfoApi *ProjectInfoApi) DeleteProjectInfo(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := projectInfoService.DeleteProjectInfo(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteProjectInfoByIds 批量删除项目信息表
// @Tags ProjectInfo
// @Summary 批量删除项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /projectInfo/deleteProjectInfoByIds [delete]
func (projectInfoApi *ProjectInfoApi) DeleteProjectInfoByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := projectInfoService.DeleteProjectInfoByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateProjectInfo 更新项目信息表
// @Tags ProjectInfo
// @Summary 更新项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body project.ProjectInfo true "更新项目信息表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projectInfo/updateProjectInfo [put]
func (projectInfoApi *ProjectInfoApi) UpdateProjectInfo(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var projectInfo project.ProjectInfo
	err := c.ShouldBindJSON(&projectInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectInfoService.UpdateProjectInfo(ctx, projectInfo)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindProjectInfo 用id查询项目信息表
// @Tags ProjectInfo
// @Summary 用id查询项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询项目信息表"
// @Success 200 {object} response.Response{data=project.ProjectInfoResponse,msg=string} "查询成功"
// @Router /projectInfo/findProjectInfo [get]
func (projectInfoApi *ProjectInfoApi) FindProjectInfo(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reprojectInfo, err := projectInfoService.GetProjectInfo(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reprojectInfo, c)
}

// GetProjectInfoList 分页获取项目信息表列表
// @Tags ProjectInfo
// @Summary 分页获取项目信息表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query projectReq.ProjectInfoSearch true "分页获取项目信息表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projectInfo/getProjectInfoList [get]
func (projectInfoApi *ProjectInfoApi) GetProjectInfoList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo projectReq.ProjectInfoSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := projectInfoService.GetProjectInfoInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetProjectInfoPublic 不需要鉴权的项目信息表接口
// @Tags ProjectInfo
// @Summary 不需要鉴权的项目信息表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectInfo/getProjectInfoPublic [get]
func (projectInfoApi *ProjectInfoApi) GetProjectInfoPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	projectInfoService.GetProjectInfoPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的项目信息表接口信息",
	}, "获取成功", c)
}
