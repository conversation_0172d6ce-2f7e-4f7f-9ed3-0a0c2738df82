package score

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/score"
	scoreReq "github.com/flipped-aurora/gin-vue-admin/server/model/score/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DepartmentManagerScoreApi struct{}

// CreateDepartmentManagerScore 创建部门/机构负责人评分
// @Tags DepartmentManagerScore
// @Summary 创建部门/机构负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body score.DepartmentManagerScore true "创建部门/机构负责人评分"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /departmentManagerScore/createDepartmentManagerScore [post]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) CreateDepartmentManagerScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var departmentManagerScore score.DepartmentManagerScore
	err := c.ShouldBindJSON(&departmentManagerScore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = departmentManagerScoreService.CreateDepartmentManagerScore(ctx, &departmentManagerScore)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteDepartmentManagerScore 删除部门/机构负责人评分
// @Tags DepartmentManagerScore
// @Summary 删除部门/机构负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body score.DepartmentManagerScore true "删除部门/机构负责人评分"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /departmentManagerScore/deleteDepartmentManagerScore [delete]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) DeleteDepartmentManagerScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := departmentManagerScoreService.DeleteDepartmentManagerScore(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteDepartmentManagerScoreByIds 批量删除部门/机构负责人评分
// @Tags DepartmentManagerScore
// @Summary 批量删除部门/机构负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /departmentManagerScore/deleteDepartmentManagerScoreByIds [delete]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) DeleteDepartmentManagerScoreByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := departmentManagerScoreService.DeleteDepartmentManagerScoreByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateDepartmentManagerScore 更新部门/机构负责人评分
// @Tags DepartmentManagerScore
// @Summary 更新部门/机构负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body score.DepartmentManagerScore true "更新部门/机构负责人评分"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /departmentManagerScore/updateDepartmentManagerScore [put]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) UpdateDepartmentManagerScore(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var departmentManagerScore score.DepartmentManagerScore
	err := c.ShouldBindJSON(&departmentManagerScore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = departmentManagerScoreService.UpdateDepartmentManagerScore(ctx, departmentManagerScore)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindDepartmentManagerScore 用id查询部门/机构负责人评分
// @Tags DepartmentManagerScore
// @Summary 用id查询部门/机构负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询部门/机构负责人评分"
// @Success 200 {object} response.Response{data=score.DepartmentManagerScore,msg=string} "查询成功"
// @Router /departmentManagerScore/findDepartmentManagerScore [get]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) FindDepartmentManagerScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	redepartmentManagerScore, err := departmentManagerScoreService.GetDepartmentManagerScore(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(redepartmentManagerScore, c)
}

// GetDepartmentManagerScoreList 分页获取部门/机构负责人评分列表
// @Tags DepartmentManagerScore
// @Summary 分页获取部门/机构负责人评分列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scoreReq.DepartmentManagerScoreSearch true "分页获取部门/机构负责人评分列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /departmentManagerScore/getDepartmentManagerScoreList [get]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) GetDepartmentManagerScoreList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scoreReq.DepartmentManagerScoreSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := departmentManagerScoreService.GetDepartmentManagerScoreInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetDepartmentManagerScorePublic 不需要鉴权的部门/机构负责人评分接口
// @Tags DepartmentManagerScore
// @Summary 不需要鉴权的部门/机构负责人评分接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /departmentManagerScore/getDepartmentManagerScorePublic [get]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) GetDepartmentManagerScorePublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	departmentManagerScoreService.GetDepartmentManagerScorePublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的部门/机构负责人评分接口信息",
	}, "获取成功", c)
}

// BatchSubmitDepartmentManagerScores 批量提交部门负责人评分
// @Tags DepartmentManagerScore
// @Summary 批量提交部门负责人评分
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body []scoreReq.BatchSubmitDepartmentManagerScoresRequest true "批量提交部门负责人评分数据"
// @Success 200 {object} response.Response{msg=string} "提交成功"
// @Router /departmentManagerScore/batchSubmitDepartmentManagerScores [post]
func (departmentManagerScoreApi *DepartmentManagerScoreApi) BatchSubmitDepartmentManagerScores(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var scoreRequests []scoreReq.BatchSubmitDepartmentManagerScoresRequest
	err := c.ShouldBindJSON(&scoreRequests)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(scoreRequests) == 0 {
		response.FailWithMessage("评分数据不能为空", c)
		return
	}

	err = departmentManagerScoreService.BatchSubmitDepartmentManagerScores(ctx, scoreRequests)
	if err != nil {
		global.GVA_LOG.Error("批量提交部门负责人评分失败!", zap.Error(err))
		response.FailWithMessage("提交失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage(fmt.Sprintf("成功提交 %d 条部门负责人评分数据", len(scoreRequests)), c)
}
