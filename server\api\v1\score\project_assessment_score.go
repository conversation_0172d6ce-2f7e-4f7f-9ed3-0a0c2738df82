package score

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/score"
    scoreReq "github.com/flipped-aurora/gin-vue-admin/server/model/score/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type ProjectAssessmentScoreApi struct {}



// CreateProjectAssessmentScore 创建精力分配评分表
// @Tags ProjectAssessmentScore
// @Summary 创建精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body score.ProjectAssessmentScore true "创建精力分配评分表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projectAssessmentScore/createProjectAssessmentScore [post]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) CreateProjectAssessmentScore(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var projectAssessmentScore score.ProjectAssessmentScore
	err := c.ShouldBindJSON(&projectAssessmentScore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectAssessmentScoreService.CreateProjectAssessmentScore(ctx,&projectAssessmentScore)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteProjectAssessmentScore 删除精力分配评分表
// @Tags ProjectAssessmentScore
// @Summary 删除精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body score.ProjectAssessmentScore true "删除精力分配评分表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projectAssessmentScore/deleteProjectAssessmentScore [delete]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) DeleteProjectAssessmentScore(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := projectAssessmentScoreService.DeleteProjectAssessmentScore(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteProjectAssessmentScoreByIds 批量删除精力分配评分表
// @Tags ProjectAssessmentScore
// @Summary 批量删除精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /projectAssessmentScore/deleteProjectAssessmentScoreByIds [delete]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) DeleteProjectAssessmentScoreByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := projectAssessmentScoreService.DeleteProjectAssessmentScoreByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateProjectAssessmentScore 更新精力分配评分表
// @Tags ProjectAssessmentScore
// @Summary 更新精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body score.ProjectAssessmentScore true "更新精力分配评分表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projectAssessmentScore/updateProjectAssessmentScore [put]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) UpdateProjectAssessmentScore(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var projectAssessmentScore score.ProjectAssessmentScore
	err := c.ShouldBindJSON(&projectAssessmentScore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = projectAssessmentScoreService.UpdateProjectAssessmentScore(ctx,projectAssessmentScore)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindProjectAssessmentScore 用id查询精力分配评分表
// @Tags ProjectAssessmentScore
// @Summary 用id查询精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询精力分配评分表"
// @Success 200 {object} response.Response{data=score.ProjectAssessmentScore,msg=string} "查询成功"
// @Router /projectAssessmentScore/findProjectAssessmentScore [get]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) FindProjectAssessmentScore(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	reprojectAssessmentScore, err := projectAssessmentScoreService.GetProjectAssessmentScore(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(reprojectAssessmentScore, c)
}
// GetProjectAssessmentScoreList 分页获取精力分配评分表列表
// @Tags ProjectAssessmentScore
// @Summary 分页获取精力分配评分表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scoreReq.ProjectAssessmentScoreSearch true "分页获取精力分配评分表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projectAssessmentScore/getProjectAssessmentScoreList [get]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) GetProjectAssessmentScoreList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo scoreReq.ProjectAssessmentScoreSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := projectAssessmentScoreService.GetProjectAssessmentScoreInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetProjectAssessmentScorePublic 不需要鉴权的精力分配评分表接口
// @Tags ProjectAssessmentScore
// @Summary 不需要鉴权的精力分配评分表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectAssessmentScore/getProjectAssessmentScorePublic [get]
func (projectAssessmentScoreApi *ProjectAssessmentScoreApi) GetProjectAssessmentScorePublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    projectAssessmentScoreService.GetProjectAssessmentScorePublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的精力分配评分表接口信息",
    }, "获取成功", c)
}
