# 评估详情API实现文档

## 概述

本文档描述了根据考核配置ID获取评估详情的API实现。该API能够根据用户的计算方法参数，有条件地查询不同类型的数据，并返回详细的评估信息。

## API接口

### 接口信息
- **路径**: `/assessment/getEvaluationDetailsByConfigId`
- **方法**: POST
- **描述**: 根据考核配置ID获取评估详情

### 请求参数

```json
{
  "assessmentConfigId": 1
}
```

### 响应数据结构

```json
{
  "code": 0,
  "data": {
    "configId": 1,
    "configName": "2024年第一季度考核",
    "users": [
      {
        "userNickName": "张三",
        "username": "zhangsan",
        "employeeId": "zhangsan",
        "calculationMethod": {
          "methodId": 1,
          "methodName": "综合评估法",
          "description": "基于项目参与度和负责人评分的综合评估",
          "formula": "A*0.6 + B*0.4",
          "assessmentCategoryId": 1
        },
        "calculationParameters": [
          {
            "parameterId": 1,
            "parameterName": "project_participation",
            "parameterCode": "项目参与度",
            "value": 0.8
          },
          {
            "parameterId": 2,
            "parameterName": "project_manager_score",
            "parameterCode": "项目负责人评分",
            "value": 4.5
          }
        ],
        "projectDetails": [
          {
            "projectId": 1,
            "projectName": "智能客服系统开发",
            "coefficient": 0.8,
            "calculationParameter": "A",
            "projectManagerScore": 4.5,
            "projectManagerUsername": "lisi"
          }
        ],
        "departmentManagerScore": 4.2,
        "bonus": 5000.0,
        "status": "normal",
        "issues": []
      }
    ],
    "statistics": {
      "totalUsers": 10,
      "normalUsers": 8,
      "incompleteUsers": 1,
      "notParticipating": 1
    }
  },
  "msg": "获取成功"
}
```

## 核心功能

### 1. 条件数据查询

API根据用户的计算方法参数，有条件地查询不同类型的数据：

- **project_participation**: 查询考核系数分配表 (`assessment_coefficient_allocation`)
- **project_manager_score**: 查询项目负责人评分表 (`project_manager_score`)
- **department_manager_score**: 查询部门负责人评分表 (`department_manager_score`)
- **其他参数**: 查询用户参数评分表 (`user_parameter_scores`)

### 2. 数据整合

- 获取用户基本信息和计算方法
- 根据参数类型动态获取相应数据
- 整合项目详情（包括系数分配和项目负责人评分）
- 计算用户状态和识别问题

### 3. 状态判断

用户状态分为三种：
- **normal**: 数据完整，正常参与考核
- **incomplete**: 数据不完整，存在问题
- **not_participating**: 未参与考核（未分配计算方法）

## 实现文件

### 1. 数据结构定义
- `server/model/assessment/request/assessment_data.go`
  - `EvaluationDetailRequest`: 请求结构体
  - `EvaluationDetailResponse`: 响应结构体
  - `UserEvaluationDetail`: 用户评估详情
  - `ProjectDetailInfo`: 项目详情信息
  - `EvaluationStatistics`: 统计信息

### 2. API处理层
- `server/api/v1/assessment/assessment_data.go`
  - `GetEvaluationDetailsByConfigId`: API处理方法

### 3. 服务层
- `server/service/assessment/assessment_data.go`
  - `GetEvaluationDetailsByConfigId`: 主要业务逻辑
  - `getUsersWithCalculationMethods`: 获取分配了计算方法的用户
  - `buildUserEvaluationDetail`: 构建用户评估详情
  - `getParameterValue`: 获取参数值
  - `getProjectParticipationData`: 获取项目参与数据
  - `getDepartmentManagerScoreData`: 获取部门负责人评分

### 4. 路由配置
- `server/router/assessment/assessment_data.go`
  - 添加新的路由映射

## 数据库查询逻辑

### 用户和计算方法查询
```sql
SELECT DISTINCT
    mua.user_name as username,
    u.nick_name as user_nick_name,
    cm.id as method_id,
    cm.method_name,
    cm.description,
    cm.formula,
    cm.assessment_category_id as category_id
FROM method_user_assignments mua
JOIN sys_users u ON mua.user_name = u.username
JOIN calculation_methods cm ON mua.method_id = cm.id
JOIN assessment_categories ac ON cm.assessment_category_id = ac.id
WHERE ac.assessment_config_id = ?
```

### 项目参与数据查询
```sql
SELECT 
    aca.project_id,
    p.project_name,
    aca.assessment_coefficient as coefficient,
    aca.calculation_parameter,
    pms.manager_score as project_manager_score,
    pms.scorer_username as manager_username
FROM assessment_coefficient_allocation aca
LEFT JOIN projects p ON aca.project_id = p.id
LEFT JOIN project_manager_score pms ON aca.project_id = pms.project_id 
    AND aca.username = pms.username 
    AND aca.assessment_config_id = pms.assessment_config_id
WHERE aca.username = ? AND aca.assessment_config_id = ?
```

## 使用说明

1. 确保数据库中有相关的考核配置数据
2. 用户需要分配计算方法才能出现在结果中
3. 根据计算方法的参数类型，系统会自动查询相应的数据表
4. API返回的数据结构支持前端表格显示和数据导出功能

## 注意事项

- API会处理数据查询错误，但不会因为单个用户的数据问题而中断整个请求
- 对于缺失的数据，会在用户的issues字段中记录问题描述
- 统计信息会实时计算各种状态的用户数量
- 支持大量用户的并发查询，但建议在生产环境中添加适当的缓存机制
