# 用户参数评分查询API文档

## 接口概述

该API用于批量查询用户的参数评分数据，支持从三个评分表中动态获取评分信息：
- `assessment_coefficient_allocation` - 考核系数分配表
- `project_manager_score` - 项目经理评分表  
- `department_manager_score` - 部门经理评分表

## 接口信息

- **URL**: `/api/assessment/getUserParameterScores`
- **方法**: `POST`
- **认证**: 需要Bearer Token

## 请求参数

```json
{
  "userNames": ["10112531", "10159179"],
  "assessmentConfigIds": [1, 2],
  "parameterNames": ["project_participation", "project_manager_score", "department_manager_score"]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userNames | string[] | 是 | 用户名列表，支持批量查询（最多100个） |
| assessmentConfigIds | int[] | 否 | 考核配置ID列表，为空则查询所有未归档配置 |
| parameterNames | string[] | 否 | 参数名称列表，为空则查询用户所有分配的参数 |

## 响应结果

```json
{
  "code": 0,
  "data": {
    "users": [
      {
        "userName": "10112531",
        "userNickName": "宋寅",
        "calculationMethod": {
          "methodId": 1,
          "methodName": "综合评价法",
          "description": "基于多维度评价的综合计算方法",
          "formula": "A*0.3 + B*0.4 + C*0.3",
          "assessmentCategoryId": 1,
          "assignedParameters": [
            {
              "id": 1,
              "parameterName": "project_participation",
              "parameterNameCn": "项目参与度",
              "roleId": 888,
              "roleName": "项目成员",
              "assessmentCategoryId": 1
            },
            {
              "id": 2,
              "parameterName": "project_manager_score",
              "parameterNameCn": "项目经理评分",
              "roleId": 8881,
              "roleName": "项目经理",
              "assessmentCategoryId": 1
            },
            {
              "id": 3,
              "parameterName": "department_manager_score",
              "parameterNameCn": "部门经理评分",
              "roleId": 9528,
              "roleName": "部门经理",
              "assessmentCategoryId": 1
            }
          ]
        },
        "parameterScores": [
          {
            "parameterName": "project_participation",
            "parameterNameCn": "项目参与度",
            "dataSource": "assessment_coefficient_allocation",
            "scoreValue": 90.0,
            "assessmentConfigId": 1,
            "assessmentConfigName": "2024年第一季度考核",
            "projectId": 69,
            "projectName": "测试项目",
            "scorerUsername": null,
            "scorerNickName": null,
            "createdAt": "2024-01-15T10:30:00Z",
            "recordId": 123
          },
          {
            "parameterName": "project_manager_score",
            "parameterNameCn": "项目经理评分",
            "dataSource": "project_manager_score",
            "scoreValue": 89.0,
            "assessmentConfigId": 1,
            "assessmentConfigName": "2024年第一季度考核",
            "projectId": 69,
            "projectName": "测试项目",
            "scorerUsername": "10139059",
            "scorerNickName": "南雄",
            "createdAt": "2024-01-20T14:20:00Z",
            "recordId": 456
          },
          {
            "parameterName": "department_manager_score",
            "parameterNameCn": "部门经理评分",
            "dataSource": "department_manager_score",
            "scoreValue": 90.0,
            "assessmentConfigId": 1,
            "assessmentConfigName": "2024年第一季度考核",
            "projectId": null,
            "projectName": null,
            "scorerUsername": "10130732",
            "scorerNickName": "张猛",
            "createdAt": "2024-01-25T16:45:00Z",
            "recordId": 789
          }
        ],
        "userSummary": {
          "totalParameters": 3,
          "scoredParameters": 3,
          "unscoredParameters": 0,
          "totalRecords": 3
        },
        "hasError": false,
        "errorMessage": ""
      }
    ],
    "summary": {
      "totalUsers": 1,
      "successUsers": 1,
      "errorUsers": 0,
      "totalRecords": 3
    }
  },
  "msg": "获取成功"
}
```

## 数据字段说明

### ParameterScoreDetail 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| parameterName | string | 参数名称（英文） |
| parameterNameCn | string | 参数中文名称 |
| dataSource | string | 数据来源表名 |
| scoreValue | float64 | 评分值，null表示无评分 |
| assessmentConfigId | int | 考核配置ID |
| assessmentConfigName | string | 考核配置名称 |
| projectId | int | 项目ID（如果适用） |
| projectName | string | 项目名称（如果适用） |
| scorerUsername | string | 评分人用户名 |
| scorerNickName | string | 评分人昵称 |
| createdAt | string | 创建时间 |
| recordId | int | 记录ID |

## 使用场景

1. **部门评分页面**: 一次性获取部门所有成员的评分数据
2. **批量数据导出**: 导出多个用户的评分报告
3. **数据分析**: 对比分析多个用户的评分情况
4. **管理员视图**: 管理员查看组织内多个用户的评分状态

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 500 | 服务器内部错误 |

## 性能说明

- 支持批量查询最多100个用户
- 使用并发查询优化性能
- 建议添加适当的数据库索引以提高查询效率
