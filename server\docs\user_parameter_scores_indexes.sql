-- 用户参数评分查询API优化索引
-- 为批量查询用户参数评分功能创建必要的数据库索引

-- =====================================================
-- assessment_coefficient_allocation 表索引
-- =====================================================

-- 主要查询索引：用户名 + 考核配置ID + 计算参数
CREATE INDEX idx_aca_user_config_param ON assessment_coefficient_allocation(username, assessment_config_id, calculation_parameter);

-- 项目相关查询索引
CREATE INDEX idx_aca_project_user ON assessment_coefficient_allocation(project_id, username);

-- 考核配置查询索引
CREATE INDEX idx_aca_config_created ON assessment_coefficient_allocation(assessment_config_id, created_at DESC);

-- 复合查询索引（覆盖索引）
CREATE INDEX idx_aca_comprehensive ON assessment_coefficient_allocation(username, assessment_config_id, calculation_parameter, project_id, assessment_coefficient, created_at);

-- =====================================================
-- project_manager_score 表索引
-- =====================================================

-- 计算参数查询索引
CREATE INDEX idx_pms_param_created ON project_manager_score(calculation_parameter, created_at DESC);

-- 系数分配ID查询索引（已存在外键索引，但可能需要优化）
CREATE INDEX idx_pms_coefficient_allocation ON project_manager_score(coefficient_allocation_id, calculation_parameter);

-- 评分人查询索引
CREATE INDEX idx_pms_scorer_created ON project_manager_score(scorer_username, created_at DESC);

-- 复合查询索引
CREATE INDEX idx_pms_comprehensive ON project_manager_score(coefficient_allocation_id, calculation_parameter, manager_score, scorer_username, created_at);

-- =====================================================
-- department_manager_score 表索引
-- =====================================================

-- 主要查询索引：用户名 + 考核配置ID + 计算参数
CREATE INDEX idx_dms_user_config_param ON department_manager_score(username, assessment_config_id, calculation_parameter);

-- 部门查询索引
CREATE INDEX idx_dms_dept_config ON department_manager_score(department_id, assessment_config_id);

-- 评分人查询索引
CREATE INDEX idx_dms_scorer_created ON department_manager_score(scorer_username, created_at DESC);

-- 复合查询索引（覆盖索引）
CREATE INDEX idx_dms_comprehensive ON department_manager_score(username, assessment_config_id, calculation_parameter, manager_score, scorer_username, created_at);

-- =====================================================
-- 关联表索引优化
-- =====================================================

-- assessment_config 表
CREATE INDEX idx_ac_archived_name ON assessment_config(is_archived, assessment_name);

-- calculation_parameters 表
CREATE INDEX idx_cp_name_cn ON calculation_parameters(parameter_name, parameter_name_cn);

-- sys_users 表（用户昵称查询优化）
CREATE INDEX idx_users_username_nickname ON sys_users(username, nick_name);

-- project_info 表（项目名称查询优化）
CREATE INDEX idx_project_id_name ON project_info(id, name);

-- method_user_assignments 表（用户计算方法查询优化）
CREATE INDEX idx_mua_user_method ON method_user_assignments(user_name, method_id);

-- =====================================================
-- 索引使用说明
-- =====================================================

/*
索引设计原则：
1. 主要查询条件（username, assessment_config_id, calculation_parameter）作为复合索引的前缀
2. 经常用于排序的字段（created_at）放在索引末尾
3. 覆盖索引包含查询所需的所有字段，减少回表查询
4. 考虑查询频率和数据量，避免过多索引影响写入性能

查询优化效果：
- 批量用户查询：通过 username IN (...) 利用索引快速定位
- 考核配置过滤：通过 assessment_config_id IN (...) 进一步缩小范围
- 参数过滤：通过 calculation_parameter IN (...) 精确匹配
- 排序优化：通过 created_at DESC 索引避免文件排序

监控建议：
1. 定期检查索引使用情况：SHOW INDEX FROM table_name;
2. 分析慢查询日志，识别需要优化的查询
3. 使用 EXPLAIN 分析查询执行计划
4. 根据实际数据量和查询模式调整索引策略
*/

-- =====================================================
-- 索引维护脚本
-- =====================================================

-- 检查索引使用情况
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     COLUMN_NAME,
--     SEQ_IN_INDEX,
--     CARDINALITY
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = DATABASE() 
-- AND TABLE_NAME IN ('assessment_coefficient_allocation', 'project_manager_score', 'department_manager_score')
-- ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 分析表统计信息
-- ANALYZE TABLE assessment_coefficient_allocation, project_manager_score, department_manager_score;

-- 检查索引碎片
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     ROUND(STAT_VALUE * @@innodb_page_size / 1024 / 1024, 2) AS 'Index Size (MB)'
-- FROM mysql.innodb_index_stats 
-- WHERE DATABASE_NAME = DATABASE()
-- AND TABLE_NAME IN ('assessment_coefficient_allocation', 'project_manager_score', 'department_manager_score')
-- AND STAT_NAME = 'size';
