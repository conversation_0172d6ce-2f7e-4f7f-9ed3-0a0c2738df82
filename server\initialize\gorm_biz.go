package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	"github.com/flipped-aurora/gin-vue-admin/server/model/project"
	"github.com/flipped-aurora/gin-vue-admin/server/model/score"
)

func bizModel() error {
	db := global.GVA_DB
	err := db.AutoMigrate(project.ProjectInfo{}, assessment.AssessmentConfig{}, assessment.BonusManagement{}, assessment.ScoreQuotaManagement{}, assessment.AssessmentCategories{}, assessment.CalculationParameters{}, assessment.CalculationMethods{}, score.ProjectAssessmentScore{}, assessment.ProjectManagerScore{}, assessment.AssessmentCoefficientAllocation{}, score.DepartmentManagerScore{})
	if err != nil {
		return err
	}
	return nil
}
