package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	holder(publicGroup, privateGroup)
	{
		projectRouter := router.RouterGroupApp.Project
		projectRouter.InitProjectInfoRouter(privateGroup, publicGroup)
	}
	{
		assessmentRouter := router.RouterGroupApp.Assessment
		assessmentRouter.InitAssessmentConfigRouter(privateGroup, publicGroup)
		assessmentRouter.InitBonusManagementRouter(privateGroup, publicGroup)
		assessmentRouter.InitScoreQuotaManagementRouter(privateGroup, publicGroup)
		assessmentRouter.InitAssessmentCategoriesRouter(privateGroup, publicGroup)
		assessmentRouter.InitCalculationParametersRouter(privateGroup, publicGroup)
		assessmentRouter.InitCalculationMethodsRouter(privateGroup, publicGroup)
		assessmentRouter.InitMethodParameterRelationsRouter(privateGroup, publicGroup)
		assessmentRouter.InitMethodUserAssignmentsRouter(privateGroup, publicGroup)
		assessmentRouter.InitAssessmentDataRouter(privateGroup, publicGroup)
		assessmentRouter.InitAssessmentCoefficientRouter(privateGroup, publicGroup)
		assessmentRouter.InitProjectManagerScoreRouter(privateGroup, publicGroup)
		assessmentRouter.InitAssessmentCoefficientAllocationRouter(privateGroup, publicGroup)
	}
	{
		scoreRouter := router.RouterGroupApp.Score
		scoreRouter.InitProjectAssessmentScoreRouter(privateGroup, publicGroup) // 占位方法，保证文件可以正确加载，避免go空变量检测报错，请勿删除。
		scoreRouter.InitDepartmentManagerScoreRouter(privateGroup, publicGroup)
	}
}
