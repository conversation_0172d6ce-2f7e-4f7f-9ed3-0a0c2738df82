// 自动生成模板AssessmentCategories
package assessment

import (
	"time"
)

// 考核类型 结构体  AssessmentCategories
type AssessmentCategories struct {
	Id           *int       `json:"id" form:"id" gorm:"primarykey;column:id;size:20;"`                                     //id字段
	CategoryName *string    `json:"categoryName" form:"categoryName" gorm:"comment:考核类别名称;column:category_name;size:100;"` //考核类别名称
	Description  *string    `json:"description" form:"description" gorm:"comment:类别描述;column:description;"`                //类别描述
	SortOrder    *int       `json:"sortOrder" form:"sortOrder" gorm:"comment:排序;column:sort_order;size:19;"`               //排序
	CreatedAt    *time.Time `json:"createdAt" form:"createdAt" gorm:"column:created_at;"`                                  //createdAt字段
	UpdatedAt    *time.Time `json:"updatedAt" form:"updatedAt" gorm:"column:updated_at;"`                                  //updatedAt字段
}

// TableName 考核类型 AssessmentCategories自定义表名 assessment_categories
func (AssessmentCategories) TableName() string {
	return "assessment_categories"
}
