// 自动生成模板AssessmentCoefficientAllocation
package assessment

import (
	"time"
)

// 考核系数分配 结构体  AssessmentCoefficientAllocation
type AssessmentCoefficientAllocation struct {
	Id                    *int       `json:"id" form:"id" gorm:"primarykey;comment:主键ID;column:id;size:20;"`                                                                 //主键ID
	AssessmentConfigId    *int       `json:"assessmentConfigId" form:"assessmentConfigId" gorm:"comment:考核配置ID，关联assessment_config.id;column:assessment_config_id;size:20;"` //考核配置ID，关联assessment_config.id
	Username              *string    `json:"username" form:"username" gorm:"comment:被评价用户名;column:username;size:50;"`                                                        //被评价用户名
	ProjectId             *int       `json:"projectId" form:"projectId" gorm:"comment:项目ID，关联project_info.id;column:project_id;size:20;"`                                    //项目ID，关联project_info.id
	AssessmentCoefficient *float64   `json:"assessmentCoefficient" form:"assessmentCoefficient" gorm:"comment:考核系数;column:assessment_coefficient;size:5;"`                   //考核系数
	CalculationParameter  *string    `json:"calculationParameter" form:"calculationParameter" gorm:"comment:计算参数;column:calculation_parameter;size:50;"`                     //计算参数
	CreatedAt             *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                                                              //创建时间
	UpdatedAt             *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                                                              //更新时间
}

// TableName 考核系数分配 AssessmentCoefficientAllocation自定义表名 assessment_coefficient_allocation
func (AssessmentCoefficientAllocation) TableName() string {
	return "assessment_coefficient_allocation"
}
