// 自动生成模板AssessmentConfig
package assessment

import (
	"time"
)

// assessmentConfig表 结构体  AssessmentConfig
type AssessmentConfig struct {
	Id                  *int       `json:"id" form:"id" gorm:"primarykey;comment:考核配置ID;column:id;size:20;"`                                                              //考核配置ID
	AssessmentName      *string    `json:"assessmentName" form:"assessmentName" gorm:"comment:考核配置名称;column:assessment_name;size:100;" binding:"required"`                //考核配置名称
	AssessmentType      *string    `json:"assessmentType" form:"assessmentType" gorm:"comment:考核类型：月、季度、年;column:assessment_type;size:20;" binding:"required"`            //考核类型
	AssessmentPeriod    *string    `json:"assessmentPeriod" form:"assessmentPeriod" gorm:"comment:考核周期;column:assessment_period;size:50;" binding:"required"`             //考核周期
	IsArchived          *bool      `json:"isArchived" form:"isArchived" gorm:"comment:是否归档：0-未归档，1-已归档;column:is_archived;default:0;"`                                    //是否归档
	AlgorithmRelationId *int       `json:"algorithmRelationId" form:"algorithmRelationId" gorm:"comment:算法关联ID;column:algorithm_relation_id;size:20;" binding:"required"` //算法关联
	BonusRelationId     *int       `json:"bonusRelationId" form:"bonusRelationId" gorm:"comment:奖金关联ID;column:bonus_relation_id;size:20;" binding:"required"`             //奖金关联
	ScoreQuotaId        *int       `json:"scoreQuotaId" form:"scoreQuotaId" gorm:"comment:高分配额关联ID;column:score_quota_id;size:20;" binding:"required"`                    //高分配额关联
	CreatedAt           *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                                                             //创建时间
	UpdatedAt           *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                                                             //更新时间
}

// TableName assessmentConfig表 AssessmentConfig自定义表名 assessment_config
func (AssessmentConfig) TableName() string {
	return "assessment_config"
}
