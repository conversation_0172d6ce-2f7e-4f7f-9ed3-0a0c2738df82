// 自动生成模板BonusManagement
package assessment

import (
	"time"

	"gorm.io/datatypes"
)

// 奖金管理 结构体  BonusManagement
type BonusManagement struct {
	Id                    *int           `json:"id" form:"id" gorm:"primarykey;comment:奖金ID;column:id;size:20;"`                                                                                  //奖金ID
	BonusName             *string        `json:"bonusName" form:"bonusName" gorm:"comment:奖金名称;column:bonus_name;size:100;" binding:"required"`                                                   //名称
	DepartmentAllocations datatypes.JSON `json:"departmentAllocations" form:"departmentAllocations" gorm:"comment:部门奖金分配;column:department_allocations;" swaggertype:"object" binding:"required"` //部门奖金分配
	Description           *string        `json:"description" form:"description" gorm:"comment:描述;column:description;size:255;"`                                                                   //描述
	CreatedAt             *time.Time     `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                                                                               //创建时间
	UpdatedAt             *time.Time     `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                                                                               //更新时间
}

// TableName 奖金管理 BonusManagement自定义表名 bonus_management
func (BonusManagement) TableName() string {
	return "bonus_management"
}
