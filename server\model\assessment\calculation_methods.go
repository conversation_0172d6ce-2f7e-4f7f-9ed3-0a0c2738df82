// 自动生成模板CalculationMethods
package assessment

import (
	"time"
)

// calculationMethods表 结构体  CalculationMethods
type CalculationMethods struct {
	Id                   *int       `json:"id" form:"id" gorm:"primarykey;column:id;size:20;"`                                                               //id字段
	MethodName           *string    `json:"methodName" form:"methodName" gorm:"comment:计算方法名称;column:method_name;size:100;"`                                 //计算方法名称
	Description          *string    `json:"description" form:"description" gorm:"comment:方法描述;column:description;"`                                          //方法描述
	Formula              *string    `json:"formula" form:"formula" gorm:"comment:规则配置JSON或传统公式;column:formula;type:text"`                                    //规则配置JSON或传统公式
	RuleType             *string    `json:"ruleType" form:"ruleType" gorm:"comment:规则类型;column:rule_type;size:20;default:'formula'"`                        //规则类型：formula-传统公式，rule-规则引擎
	RuleVersion          *string    `json:"ruleVersion" form:"ruleVersion" gorm:"comment:规则版本;column:rule_version;size:10;default:'1.0'"`                   //规则版本
	AssessmentCategoryId *int       `json:"assessmentCategoryId" form:"assessmentCategoryId" gorm:"comment:关联考核类别ID;column:assessment_category_id;size:20;"` //关联考核类别ID
	CreatedAt            *time.Time `json:"createdAt" form:"createdAt" gorm:"column:created_at;"`                                                            //createdAt字段
	UpdatedAt            *time.Time `json:"updatedAt" form:"updatedAt" gorm:"column:updated_at;"`                                                            //updatedAt字段
}

// TableName calculationMethods表 CalculationMethods自定义表名 calculation_methods
func (CalculationMethods) TableName() string {
	return "calculation_methods"
}
