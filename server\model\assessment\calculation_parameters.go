// 自动生成模板CalculationParameters
package assessment

import (
	"time"
)

// 计算参数表 结构体  CalculationParameters
type CalculationParameters struct {
	Id                   *int       `json:"id" form:"id" gorm:"primarykey;column:id;size:20;"`                                                               //id字段
	ParameterName        *string    `json:"parameterName" form:"parameterName" gorm:"comment:英文参数名;column:parameter_name;size:100;"`                         //英文参数名
	ParameterNameCn      *string    `json:"parameterNameCn" form:"parameterNameCn" gorm:"comment:中文参数名;column:parameter_name_cn;size:100;"`                  //中文参数名
	RoleId               *string    `json:"roleId" form:"roleId" gorm:"comment:关联角色ID(支持多个角色,逗号分隔);column:role_id;size:255;"`                                //关联角色ID(支持多个角色,逗号分隔)
	AssessmentCategoryId *int       `json:"assessmentCategoryId" form:"assessmentCategoryId" gorm:"comment:关联考核类别ID;column:assessment_category_id;size:20;"` //关联考核类别ID
	CreatedAt            *time.Time `json:"createdAt" form:"createdAt" gorm:"column:created_at;"`                                                            //createdAt字段
	UpdatedAt            *time.Time `json:"updatedAt" form:"updatedAt" gorm:"column:updated_at;"`                                                            //updatedAt字段
}

// TableName 计算参数表 CalculationParameters自定义表名 calculation_parameters
func (CalculationParameters) TableName() string {
	return "calculation_parameters"
}
