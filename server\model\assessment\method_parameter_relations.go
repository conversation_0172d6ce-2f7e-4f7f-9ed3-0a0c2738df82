
// 自动生成模板MethodParameterRelations
package assessment
import (
	"time"
)

// 关联关系 结构体  MethodParameterRelations
type MethodParameterRelations struct {
  Id  *int `json:"id" form:"id" gorm:"primarykey;column:id;size:20;"`  //id字段
  MethodId  *int `json:"methodId" form:"methodId" gorm:"comment:计算方法ID;column:method_id;size:20;"`  //计算方法ID
  ParameterId  *int `json:"parameterId" form:"parameterId" gorm:"comment:参数ID;column:parameter_id;size:20;"`  //参数ID
  CreatedAt  *time.Time `json:"createdAt" form:"createdAt" gorm:"column:created_at;"`  //createdAt字段
}


// TableName 关联关系 MethodParameterRelations自定义表名 method_parameter_relations
func (MethodParameterRelations) TableName() string {
    return "method_parameter_relations"
}





