
// 自动生成模板MethodUserAssignments
package assessment
import (
	"time"
)

// 方法用户关联 结构体  MethodUserAssignments
type MethodUserAssignments struct {
  Id  *int `json:"id" form:"id" gorm:"primarykey;column:id;size:20;"`  //id字段
  MethodId  *int `json:"methodId" form:"methodId" gorm:"comment:计算方法ID;column:method_id;size:20;"`  //计算方法ID
  UserName  *string `json:"userName" form:"userName" gorm:"comment:用户名;column:user_name;size:191;"`  //用户名
  CreatedAt  *time.Time `json:"createdAt" form:"createdAt" gorm:"column:created_at;"`  //createdAt字段
  UpdatedAt  *time.Time `json:"updatedAt" form:"updatedAt" gorm:"column:updated_at;"`  //updatedAt字段
}


// TableName 方法用户关联 MethodUserAssignments自定义表名 method_user_assignments
func (MethodUserAssignments) TableName() string {
    return "method_user_assignments"
}





