// 自动生成模板ProjectManagerScore
package assessment

import (
	"time"
)

// 项目负责人评分 结构体  ProjectManagerScore
type ProjectManagerScore struct {
	Id                      *int       `json:"id" form:"id" gorm:"primarykey;comment:主键ID;column:id;size:20;"`                                                                                                  //主键ID
	CoefficientAllocationId *int       `json:"coefficientAllocationId" form:"coefficientAllocationId" gorm:"comment:考核系数分配ID，关联assessment_coefficient_allocation.id;column:coefficient_allocation_id;size:20;"` //考核系数分配ID，关联assessment_coefficient_allocation.id
	ManagerScore            *float64   `json:"managerScore" form:"managerScore" gorm:"comment:项目负责人评分;column:manager_score;size:5;"`                                                                            //项目负责人评分
	ScorerUsername          *string    `json:"scorerUsername" form:"scorerUsername" gorm:"comment:评分人用户名;column:scorer_username;size:50;"`                                                                      //评分人用户名
	CalculationParameter    *string    `json:"calculationParameter" form:"calculationParameter" gorm:"comment:计算参数;column:calculation_parameter;size:50;"`                                                      //计算参数
	CreatedAt               *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                                                                                               //创建时间
	UpdatedAt               *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                                                                                               //更新时间
}

// TableName 项目负责人评分 ProjectManagerScore自定义表名 project_manager_score
func (ProjectManagerScore) TableName() string {
	return "project_manager_score"
}
