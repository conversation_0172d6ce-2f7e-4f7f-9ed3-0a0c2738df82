package request

import "time"

// AssessmentCoefficientRecord 考核系数记录
type AssessmentCoefficientRecord struct {
	AssessmentConfigId    uint      `json:"assessmentConfigId" binding:"required"`                  // 考核配置ID
	Username              string    `json:"username" binding:"required"`                            // 用户名
	ProjectId             uint      `json:"projectId" binding:"required"`                           // 项目ID
	AssessmentCoefficient float64   `json:"assessmentCoefficient" binding:"required,min=0,max=100"` // 考核系数
	CalculationParameter  string    `json:"calculationParameter"`                                   // 计算参数
	ScorerUsername        string    `json:"scorerUsername"`                                         // 评分人用户名
	CreatedAt             time.Time `json:"createdAt"`                                              // 创建时间
}

// ReplaceAssessmentCoefficientsRequest 替换考核系数请求
type ReplaceAssessmentCoefficientsRequest struct {
	AssessmentConfigId uint                          `json:"assessmentConfigId" binding:"required"` // 考核配置ID
	Records            []AssessmentCoefficientRecord `json:"records" binding:"required"`            // 考核系数记录列表
}

// GetAssessmentCoefficientsRequest 获取考核系数请求
type GetAssessmentCoefficientsRequest struct {
	AssessmentConfigId uint `uri:"configId" binding:"required"` // 考核配置ID
}

// ValidationResult 校验结果
type ValidationResult struct {
	IsValid      bool                     `json:"isValid"`      // 是否校验通过
	Message      string                   `json:"message"`      // 校验消息
	InvalidUsers []InvalidUserCoefficient `json:"invalidUsers"` // 不通过的用户列表
	ValidUsers   []ValidUserCoefficient   `json:"validUsers"`   // 通过的用户列表
}

// InvalidUserCoefficient 不通过校验的用户系数信息
type InvalidUserCoefficient struct {
	Username   string               `json:"username"`   // 用户名
	NickName   string               `json:"nickName"`   // 用户昵称
	Total      float64              `json:"total"`      // 当前总和
	Difference float64              `json:"difference"` // 与100%的差值
	Details    []ProjectCoefficient `json:"details"`    // 各项目的系数详情
}

// ValidUserCoefficient 通过校验的用户系数信息
type ValidUserCoefficient struct {
	Username string  `json:"username"` // 用户名
	NickName string  `json:"nickName"` // 用户昵称
	Total    float64 `json:"total"`    // 当前总和
}

// ProjectCoefficient 项目系数详情
type ProjectCoefficient struct {
	ProjectId   uint    `json:"projectId"`   // 项目ID
	ProjectName string  `json:"projectName"` // 项目名称
	Coefficient float64 `json:"coefficient"` // 系数值
}
