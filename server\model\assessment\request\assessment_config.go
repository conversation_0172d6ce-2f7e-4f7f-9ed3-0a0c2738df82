package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type AssessmentConfigSearch struct {
	AssessmentName   *string `json:"assessmentName" form:"assessmentName"`
	AssessmentType   *string `json:"assessmentType" form:"assessmentType"`
	AssessmentPeriod *string `json:"assessmentPeriod" form:"assessmentPeriod"`
	IsArchived       *bool   `json:"isArchived" form:"isArchived"`
	request.PageInfo
}

// AssessmentConfigWithNames 包含关联名称的考核配置响应结构体
type AssessmentConfigWithNames struct {
	Id                  *int       `json:"id"`
	AssessmentName      *string    `json:"assessmentName"`
	AssessmentType      *string    `json:"assessmentType"`
	AssessmentPeriod    *string    `json:"assessmentPeriod"`
	IsArchived          *bool      `json:"isArchived"`
	AlgorithmRelationId *int       `json:"algorithmRelationId"`
	BonusRelationId     *int       `json:"bonusRelationId"`
	ScoreQuotaId        *int       `json:"scoreQuotaId"`
	AlgorithmName       *string    `json:"algorithmName"` // 算法关联名称
	BonusName           *string    `json:"bonusName"`     // 奖金关联名称
	QuotaName           *string    `json:"quotaName"`     // 高分配额关联名称
	CreatedAt           *time.Time `json:"createdAt"`
	UpdatedAt           *time.Time `json:"updatedAt"`
}
