package request

import (
	"time"
)

// GetAssessmentDataRequest 获取考核数据请求结构体
type GetAssessmentDataRequest struct {
	OrgId    int    `json:"orgId" binding:"required"`    // 组织ID
	IsAdmin  bool   `json:"isAdmin"`                     // 是否为管理员
	UserName string `json:"userName" binding:"required"` // 用户名
}

// AssessmentDataResponse 考核数据响应结构体
type AssessmentDataResponse struct {
	AssessmentConfigs     []AssessmentConfigInfo     `json:"assessmentConfigs"`     // 未归档的考核配置
	AdminData             *AdminData                 `json:"adminData"`             // 管理员数据（仅管理员可见）
	CoefficientData       CoefficientData            `json:"coefficientData"`       // 考核系数分配数据
	ManagerProjects       []ManagerProjectInfo       `json:"managerProjects"`       // 用户作为项目负责人的项目
	CalculationParameters []CalculationParameterInfo `json:"calculationParameters"` // 计算参数映射信息
}

// AssessmentConfigInfo 考核配置信息
type AssessmentConfigInfo struct {
	Id                  int       `json:"id"`
	AssessmentName      string    `json:"assessmentName"`
	AssessmentType      string    `json:"assessmentType"`
	AssessmentPeriod    string    `json:"assessmentPeriod"`
	IsArchived          bool      `json:"isArchived"`
	AlgorithmRelationId int       `json:"algorithmRelationId"`
	BonusRelationId     int       `json:"bonusRelationId"`
	ScoreQuotaId        int       `json:"scoreQuotaId"`
	CreatedAt           time.Time `json:"createdAt"`
}

// AdminData 管理员专属数据
type AdminData struct {
	BonusInfo               map[string]BonusInfo         `json:"bonusInfo"`               // 奖金信息（按考核配置ID分组）
	QuotaInfo               QuotaInfo                    `json:"quotaInfo"`               // 配额信息
	OrgMembers              []OrgMember                  `json:"orgMembers"`              // 组织成员信息
	DepartmentManagerScores []DepartmentManagerScoreInfo `json:"departmentManagerScores"` // 部门负责人评分数据
}

// BonusInfo 奖金信息
type BonusInfo struct {
	Id                    int                    `json:"id"`
	BonusName             string                 `json:"bonusName"`
	TotalAmount           float64                `json:"totalAmount"`
	DepartmentAllocations []DepartmentAllocation `json:"departmentAllocations"`
	Description           string                 `json:"description"`
}

// DepartmentAllocation 部门奖金分配
type DepartmentAllocation struct {
	DepartmentId    int     `json:"departmentId"`
	DepartmentName  string  `json:"departmentName"`
	AllocatedAmount float64 `json:"allocatedAmount"`
	UsedAmount      float64 `json:"usedAmount"`
	RemainingAmount float64 `json:"remainingAmount"`
}

// QuotaInfo 配额信息
type QuotaInfo struct {
	Id               int               `json:"id"`
	QuotaName        string            `json:"quotaName"`
	DepartmentQuotas []DepartmentQuota `json:"departmentQuotas"`
	Description      string            `json:"description"`
}

// DepartmentQuota 部门配额
type DepartmentQuota struct {
	DepartmentId    int    `json:"departmentId"`
	DepartmentName  string `json:"departmentName"`
	QuotaAmount     int    `json:"quotaAmount"`
	UsedAmount      int    `json:"usedAmount"`
	RemainingAmount int    `json:"remainingAmount"`
}

// OrgMember 组织成员信息
type OrgMember struct {
	UserId            int               `json:"userId"`
	UserName          string            `json:"userName"`
	NickName          string            `json:"nickName"`
	Email             string            `json:"email"`
	Phone             string            `json:"phone"`
	AuthorityId       int               `json:"authorityId"`
	IsAdmin           bool              `json:"isAdmin"`
	IsAgentManager    bool              `json:"isAgentManager"`   // 是否为代理负责人
	OrganizationName  string            `json:"organizationName"` // 组织名称
	CalculationMethod CalculationMethod `json:"calculationMethod"`
	Projects          ProjectInfo       `json:"projects"`
}

// CalculationMethod 计算方法信息
type CalculationMethod struct {
	MethodId             int                 `json:"methodId"`
	MethodName           string              `json:"methodName"`
	Description          string              `json:"description"`
	Formula              string              `json:"formula"`
	AssessmentCategoryId int                 `json:"assessmentCategoryId"`
	AssignedParameters   []AssignedParameter `json:"assignedParameters"`
}

// AssignedParameter 分配的参数
type AssignedParameter struct {
	Id                   int    `json:"id"`
	ParameterName        string `json:"parameterName"`
	ParameterNameCn      string `json:"parameterNameCn"`
	RoleId               string `json:"roleId"` // 修改为字符串类型，支持多个角色ID（逗号分隔）
	RoleName             string `json:"roleName"`
	AssessmentCategoryId int    `json:"assessmentCategoryId"`
}

// ProjectInfo 项目信息
type ProjectInfo struct {
	AsManager []ProjectDetail `json:"asManager"` // 作为负责人的项目
	AsMember  []ProjectDetail `json:"asMember"`  // 作为成员的项目
}

// ProjectDetail 项目详情
type ProjectDetail struct {
	ProjectId      int      `json:"projectId"`
	ProjectName    string   `json:"projectName"`
	DepartmentId   int      `json:"departmentId"`
	DepartmentName string   `json:"departmentName"`
	ManagerId      string   `json:"managerId,omitempty"`
	ManagerName    string   `json:"managerName,omitempty"`
	Type           string   `json:"type"`
	Members        []string `json:"members"`
	MemberNames    []string `json:"memberNames"`
}

// ProjectDetailDB 项目详情数据库查询结构体
type ProjectDetailDB struct {
	ProjectId      int    `json:"projectId"`
	ProjectName    string `json:"projectName"`
	DepartmentId   int    `json:"departmentId"`
	DepartmentName string `json:"departmentName"`
	ManagerId      string `json:"managerId,omitempty"`
	ManagerName    string `json:"managerName,omitempty"`
	Type           string `json:"type"`
	MembersJSON    string `json:"members"` // JSON字符串
}

// CoefficientData 考核系数分配数据
type CoefficientData struct {
	HasCurrentData       bool              `json:"hasCurrentData"`
	CurrentAssessmentId  int               `json:"currentAssessmentId"`
	Coefficients         []CoefficientInfo `json:"coefficients"`
	PreviousAssessmentId *int              `json:"previousAssessmentId"`
	PreviousCoefficients []CoefficientInfo `json:"previousCoefficients"`
	AllCoefficients      []CoefficientInfo `json:"allCoefficients"` // 所有未归档考核配置的系数数据
}

// CoefficientInfo 系数信息
type CoefficientInfo struct {
	Id                        int       `json:"id"`
	AssessmentConfigId        int       `json:"assessmentConfigId"`
	Username                  string    `json:"username"`
	NickName                  string    `json:"nickName"` // 用户昵称
	ProjectId                 int       `json:"projectId"`
	AssessmentCoefficient     float64   `json:"assessmentCoefficient"`
	ManagerScore              *float64  `json:"managerScore"`
	ScorerUsername            *string   `json:"scorerUsername"`
	CalculationParameter      string    `json:"calculationParameter"`
	UserCalculationParameters string    `json:"userCalculationParameters"` // 用户的计算方法参数（逗号分隔）
	CreatedAt                 time.Time `json:"createdAt"`
}

// ManagerProjectInfo 项目负责人项目信息
type ManagerProjectInfo struct {
	ProjectId      int               `json:"projectId"`
	ProjectName    string            `json:"projectName"`
	DepartmentId   int               `json:"departmentId"`
	DepartmentName string            `json:"departmentName"`
	Type           string            `json:"type"`
	Members        []string          `json:"members"`
	MemberNames    []string          `json:"memberNames"`
	Coefficients   []CoefficientInfo `json:"coefficients"`
}

// ManagerProjectInfoDB 项目负责人项目信息数据库查询结构体
type ManagerProjectInfoDB struct {
	ProjectId      int    `json:"projectId"`
	ProjectName    string `json:"projectName"`
	DepartmentId   int    `json:"departmentId"`
	DepartmentName string `json:"departmentName"`
	Type           string `json:"type"`
	MembersJSON    string `json:"members"` // JSON字符串
}

// DepartmentManagerScoreInfo 部门负责人评分信息
type DepartmentManagerScoreInfo struct {
	Id                   int       `json:"id"`
	AssessmentConfigId   int       `json:"assessmentConfigId"`
	AssessmentConfigName string    `json:"assessmentConfigName"`
	Username             string    `json:"username"`
	UserNickName         string    `json:"userNickName"`
	DepartmentId         int       `json:"departmentId"`
	DepartmentName       string    `json:"departmentName"`
	ManagerScore         *float64  `json:"managerScore"`
	BonusAmount          *float64  `json:"bonusAmount"`
	ScorerUsername       *string   `json:"scorerUsername"`
	ScorerNickName       *string   `json:"scorerNickName"`
	CalculationParameter *string   `json:"calculationParameter"`
	CreatedAt            time.Time `json:"createdAt"`
	UpdatedAt            time.Time `json:"updatedAt"`
}

// CalculationParameterInfo 计算参数信息
type CalculationParameterInfo struct {
	Id                     int    `json:"id"`
	ParameterName          string `json:"parameterName"`          // 参数名称（英文，如A、B、C）
	ParameterNameCn        string `json:"parameterNameCn"`        // 参数名称（中文）
	RoleId                 string `json:"roleId"`                 // 关联角色ID（支持多个角色，逗号分隔）
	RoleName               string `json:"roleName"`               // 角色名称
	AssessmentCategoryId   int    `json:"assessmentCategoryId"`   // 考核类别ID
	AssessmentCategoryName string `json:"assessmentCategoryName"` // 考核类别名称
}

// GetUserParameterScoresRequest 获取用户参数评分请求结构体
type GetUserParameterScoresRequest struct {
	UserNames           []string `json:"userNames" binding:"required"` // 用户名列表（支持批量）
	AssessmentConfigIds []int    `json:"assessmentConfigIds"`          // 考核配置ID列表，为空则查询所有未归档配置
	ParameterNames      []string `json:"parameterNames"`               // 参数名称列表，为空则查询用户所有分配的参数
}

// GetUserParameterScoresResponse 获取用户参数评分响应结构体
type GetUserParameterScoresResponse struct {
	Users   []UserParameterScores `json:"users"`   // 用户评分数据列表
	Summary BatchQuerySummary     `json:"summary"` // 批量查询汇总信息
}

// GetEvaluationDetailsByConfigRequest 根据考核配置获取评价详情请求结构体
type GetEvaluationDetailsByConfigRequest struct {
	AssessmentConfigId int `json:"assessmentConfigId" binding:"required"` // 考核配置ID
}

// EvaluationUserDetail 评价用户详情
type EvaluationUserDetail struct {
	UserName          string                 `json:"userName"`          // 用户名
	UserNickName      string                 `json:"userNickName"`      // 用户昵称
	Status            string                 `json:"status"`            // 状态: normal, incomplete, not_participating
	CalculationMethod *CalculationMethod     `json:"calculationMethod"` // 计算方法信息
	ParameterScores   []ParameterScoreDetail `json:"parameterScores"`   // 参数评分详情
	Issues            []string               `json:"issues"`            // 问题列表（仅当status为incomplete时）
	UserSummary       ParameterScoreSummary  `json:"userSummary"`       // 用户汇总信息
}

// EvaluationSummary 评价汇总信息
type EvaluationSummary struct {
	Total            int `json:"total"`            // 总用户数
	Normal           int `json:"normal"`           // 正常参与用户数
	Incomplete       int `json:"incomplete"`       // 数据异常用户数
	NotParticipating int `json:"notParticipating"` // 不参与考核用户数
}

// GetEvaluationDetailsByConfigResponse 根据考核配置获取评价详情响应结构体
type GetEvaluationDetailsByConfigResponse struct {
	AssessmentConfigId   int                    `json:"assessmentConfigId"`   // 考核配置ID
	AssessmentConfigName string                 `json:"assessmentConfigName"` // 考核配置名称
	Users                []EvaluationUserDetail `json:"users"`                // 用户详情列表
	Summary              EvaluationSummary      `json:"summary"`              // 汇总信息
}

// UserParameterScores 用户参数评分数据
type UserParameterScores struct {
	UserName          string                 `json:"userName"`          // 用户名
	UserNickName      string                 `json:"userNickName"`      // 用户昵称
	CalculationMethod *CalculationMethod     `json:"calculationMethod"` // 用户的计算方法信息，null表示未分配
	ParameterScores   []ParameterScoreDetail `json:"parameterScores"`   // 参数评分明细列表
	UserSummary       ParameterScoreSummary  `json:"userSummary"`       // 用户个人汇总信息
	HasError          bool                   `json:"hasError"`          // 是否有查询错误
	ErrorMessage      string                 `json:"errorMessage"`      // 错误信息

	// 新增校验相关字段
	ValidationStatus  string             `json:"validationStatus"`  // 校验状态: "success", "warning", "error"
	ValidationMessage string             `json:"validationMessage"` // 校验信息摘要
	ValidationDetails []ValidationDetail `json:"validationDetails"` // 详细的校验结果列表
}

// ValidationDetail 校验详情
type ValidationDetail struct {
	Type        string `json:"type"`        // 校验类型: "coefficient_allocation", "project_manager_score", "department_manager_score"
	Status      string `json:"status"`      // 校验状态: "success", "error"
	Message     string `json:"message"`     // 具体的校验信息
	ProjectId   *int   `json:"projectId"`   // 项目ID（如果是项目相关的校验）
	ProjectName string `json:"projectName"` // 项目名称（如果是项目相关的校验）
	ManagerName string `json:"managerName"` // 项目负责人姓名（如果是项目负责人相关的校验）
}

// ParameterScoreDetail 参数评分明细
type ParameterScoreDetail struct {
	ParameterName        string    `json:"parameterName"`        // 参数名称
	ParameterNameCn      string    `json:"parameterNameCn"`      // 参数中文名称
	DataSource           string    `json:"dataSource"`           // 数据来源表名
	ScoreValue           *float64  `json:"scoreValue"`           // 评分值，null表示无评分
	AssessmentConfigId   int       `json:"assessmentConfigId"`   // 考核配置ID
	AssessmentConfigName string    `json:"assessmentConfigName"` // 考核配置名称
	ProjectId            *int      `json:"projectId"`            // 项目ID（如果适用）
	ProjectName          *string   `json:"projectName"`          // 项目名称（如果适用）
	ScorerUsername       *string   `json:"scorerUsername"`       // 评分人用户名
	ScorerNickName       *string   `json:"scorerNickName"`       // 评分人昵称
	CreatedAt            time.Time `json:"createdAt"`            // 创建时间
	RecordId             int       `json:"recordId"`             // 记录ID
}

// ParameterScoreSummary 参数评分汇总信息
type ParameterScoreSummary struct {
	TotalParameters    int `json:"totalParameters"`    // 总参数数量
	ScoredParameters   int `json:"scoredParameters"`   // 已评分参数数量
	UnscoredParameters int `json:"unscoredParameters"` // 未评分参数数量
	TotalRecords       int `json:"totalRecords"`       // 总记录数量
}

// BatchQuerySummary 批量查询汇总信息
type BatchQuerySummary struct {
	TotalUsers   int `json:"totalUsers"`   // 总用户数
	SuccessUsers int `json:"successUsers"` // 成功查询的用户数
	ErrorUsers   int `json:"errorUsers"`   // 查询失败的用户数
	TotalRecords int `json:"totalRecords"` // 总记录数
}

// UserInfo 用户基本信息
type UserInfo struct {
	UserName         string `json:"userName"`         // 用户名
	NickName         string `json:"nickName"`         // 用户昵称
	UserId           int    `json:"userId"`           // 用户ID
	OrganizationName string `json:"organizationName"` // 组织名称
}

// ===== 新增：评估详情相关结构体 =====

// EvaluationDetailRequest 评估详情请求结构体
type EvaluationDetailRequest struct {
	AssessmentConfigId int `json:"assessmentConfigId" binding:"required"`
}

// EvaluationDetailResponse 评估详情响应结构体
type EvaluationDetailResponse struct {
	ConfigId   int                    `json:"configId"`   // 考核配置ID
	ConfigName string                 `json:"configName"` // 考核配置名称
	Users      []UserEvaluationDetail `json:"users"`      // 用户评估详情列表
	Statistics EvaluationStatistics   `json:"statistics"` // 统计信息
}

// UserEvaluationDetail 用户评估详情
type UserEvaluationDetail struct {
	// 用户基本信息
	UserNickName   string `json:"userNickName"`   // 用户姓名
	Username       string `json:"username"`       // 用户名
	EmployeeId     string `json:"employeeId"`     // 员工编号
	DepartmentId   *int   `json:"departmentId"`   // 部门ID
	DepartmentName string `json:"departmentName"` // 部门名称

	// 计算方法信息
	CalculationMethod *CalculationMethodInfo `json:"calculationMethod"` // 分配的计算方法

	// 计算参数列表（根据计算方法的参数动态生成）
	CalculationParameters []EvaluationParameterInfo `json:"calculationParameters"`

	// 项目详情（仅当有project_participation参数时存在）
	ProjectDetails []ProjectDetailInfo `json:"projectDetails"`

	// 部门负责人评分（仅当有department_manager_score参数时存在）
	DepartmentManagerScore *float64 `json:"departmentManagerScore"`

	// 奖金金额
	Bonus *float64 `json:"bonus"`

	// 用户状态
	Status string `json:"status"` // "normal", "incomplete", "not_participating"

	// 问题描述列表
	Issues []string `json:"issues"`
}

// CalculationMethodInfo 计算方法信息
type CalculationMethodInfo struct {
	MethodId             int    `json:"methodId"`             // 方法ID
	MethodName           string `json:"methodName"`           // 方法名称
	Description          string `json:"description"`          // 方法描述
	Formula              string `json:"formula"`              // 计算公式
	AssessmentCategoryId int    `json:"assessmentCategoryId"` // 考核类别ID
}

// EvaluationParameterInfo 评估参数信息
type EvaluationParameterInfo struct {
	ParameterId   int      `json:"parameterId"`   // 参数ID
	ParameterName string   `json:"parameterName"` // 参数名称
	ParameterCode string   `json:"parameterCode"` // 参数代码
	Value         *float64 `json:"value"`         // 参数值（来源根据参数类型而定）
}

// ProjectDetailInfo 项目详情信息（仅当有project_participation参数时存在）
type ProjectDetailInfo struct {
	ProjectId              int      `json:"projectId"`              // 项目ID
	ProjectName            string   `json:"projectName"`            // 项目名称
	Coefficient            float64  `json:"coefficient"`            // 考核系数
	CalculationParameter   string   `json:"calculationParameter"`   // 计算参数标识（如A、B、C）
	ProjectManagerScore    *float64 `json:"projectManagerScore"`    // 项目负责人评分（仅当同时有project_manager_score参数时存在）
	ProjectManagerUsername *string  `json:"projectManagerUsername"` // 评分的项目负责人用户名
	IsSubstituted          bool     `json:"isSubstituted"`          // 是否为部门负责人评分替代
}

// EvaluationStatistics 评估统计信息
type EvaluationStatistics struct {
	TotalUsers       int `json:"totalUsers"`       // 总用户数
	NormalUsers      int `json:"normalUsers"`      // 正常用户数
	IncompleteUsers  int `json:"incompleteUsers"`  // 数据不完整用户数
	NotParticipating int `json:"notParticipating"` // 未参与用户数
}
