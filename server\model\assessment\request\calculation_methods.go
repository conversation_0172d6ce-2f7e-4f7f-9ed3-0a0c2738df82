package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type CalculationMethodsSearch struct {
	request.PageInfo
}

// CreateCalculationMethodWithRelations 创建计算方法及关联关系的请求结构体
type CreateCalculationMethodWithRelations struct {
	MethodName           string `json:"methodName" binding:"required"` // 计算方法名称
	Description          string `json:"description"`                   // 方法描述
	Formula              string `json:"formula"`                       // 计算公式
	SelectedParameters   []int  `json:"selectedParameters"`            // 选中的参数ID数组
	AssignedUsers        []int  `json:"assignedUsers"`                 // 分配的用户ID数组
	AssessmentCategoryId *int   `json:"assessmentCategoryId"`          // 关联考核类别ID
}

// UpdateCalculationMethodWithRelations 更新计算方法及关联关系的请求结构体
type UpdateCalculationMethodWithRelations struct {
	ID                   int     `json:"id" binding:"required"`         // 计算方法ID
	MethodName           string  `json:"methodName" binding:"required"` // 计算方法名称
	Description          string  `json:"description"`                   // 方法描述
	Formula              string  `json:"formula"`                       // 计算公式
	RuleType             *string `json:"ruleType"`                      // 规则类型
	SelectedParameters   []int   `json:"selectedParameters"`            // 选中的参数ID数组
	AssignedUsers        []int   `json:"assignedUsers"`                 // 分配的用户ID数组
	AssessmentCategoryId *int    `json:"assessmentCategoryId"`          // 关联考核类别ID
}

// CalculationMethodWithRelationsResponse 计算方法及关联关系的响应结构体
type CalculationMethodWithRelationsResponse struct {
	ID                   int    `json:"id"`                   // 计算方法ID
	MethodName           string `json:"methodName"`           // 计算方法名称
	Description          string `json:"description"`          // 方法描述
	Formula              string `json:"formula"`              // 计算公式
	SelectedParameters   []int  `json:"selectedParameters"`   // 选中的参数ID数组
	AssignedUsers        []int  `json:"assignedUsers"`        // 分配的用户ID数组
	AssessmentCategoryId *int   `json:"assessmentCategoryId"` // 关联考核类别ID
	CreatedAt            string `json:"createdAt"`            // 创建时间
	UpdatedAt            string `json:"updatedAt"`            // 更新时间
}
