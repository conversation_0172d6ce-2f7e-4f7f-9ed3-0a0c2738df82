package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type CalculationParametersSearch struct {
	request.PageInfo
}

// CalculationParametersWithRoleName 包含角色名称的计算参数响应结构体
type CalculationParametersWithRoleName struct {
	Id                   *int       `json:"id"`
	ParameterName        *string    `json:"parameterName"`
	ParameterNameCn      *string    `json:"parameterNameCn"`
	RoleId               *string    `json:"roleId"`   // 修改为字符串类型，支持多个角色ID（逗号分隔）
	RoleName             *string    `json:"roleName"` // 角色名称
	AssessmentCategoryId *int       `json:"assessmentCategoryId"`
	CreatedAt            *time.Time `json:"createdAt"`
	UpdatedAt            *time.Time `json:"updatedAt"`
}
