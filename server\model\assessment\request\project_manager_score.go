package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type ProjectManagerScoreSearch struct {
	request.PageInfo
}

// BatchSubmitProjectMemberScoresRequest 批量提交项目成员评分请求结构体
type BatchSubmitProjectMemberScoresRequest struct {
	CoefficientAllocationId int     `json:"coefficientAllocationId" binding:"required"` // 考核系数分配ID
	ManagerScore            float64 `json:"managerScore" binding:"required"`            // 项目负责人评分
	ScorerUsername          string  `json:"scorerUsername" binding:"required"`          // 评分人用户名
	CalculationParameter    string  `json:"calculationParameter"`                       // 计算参数
}
