// 自动生成模板ScoreQuotaManagement
package assessment

import (
	"time"

	"gorm.io/datatypes"
)

// 高分配额管理 结构体  ScoreQuotaManagement
type ScoreQuotaManagement struct {
	Id               *int           `json:"id" form:"id" gorm:"primarykey;comment:高分配额ID;column:id;size:20;"`                                                                   //高分配额ID
	QuotaName        *string        `json:"quotaName" form:"quotaName" gorm:"comment:配额名称;column:quota_name;size:100;" binding:"required"`                                      //配额名称
	DepartmentQuotas datatypes.JSON `json:"departmentQuotas" form:"departmentQuotas" gorm:"comment:部门高分配额详情;column:department_quotas;" swaggertype:"object" binding:"required"` //部门高分配额详情
	Description      *string        `json:"description" form:"description" gorm:"comment:描述;column:description;size:255;"`                                                      //描述
	CreatedAt        *time.Time     `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                                                                  //创建时间
	UpdatedAt        *time.Time     `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                                                                  //更新时间
}

// TableName 高分配额管理 ScoreQuotaManagement自定义表名 score_quota_management
func (ScoreQuotaManagement) TableName() string {
	return "score_quota_management"
}
