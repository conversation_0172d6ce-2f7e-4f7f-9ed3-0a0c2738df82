// 自动生成模板ProjectInfo
package project

import (
	"time"

	"gorm.io/datatypes"
)

// 项目信息表 结构体  ProjectInfo
type ProjectInfo struct {
	Id           *int           `json:"id" form:"id" gorm:"primarykey;comment:项目ID;column:id;size:20;"`                                  //项目ID
	Name         *string        `json:"name" form:"name" gorm:"comment:项目名称;column:name;size:100;" binding:"required"`                   //项目名称
	DepartmentId *int           `json:"departmentId" form:"departmentId" gorm:"comment:所属部门ID;column:department_id;" binding:"required"` //所属部门ID
	ManagerId    *string        `json:"managerId" form:"managerId" gorm:"comment:项目经理用户名;column:manager_id;size:50;" binding:"required"` //项目负责人
	Type         *string        `json:"type" form:"type" gorm:"comment:项目类型：自研项目、承揽项目;column:type;size:20;"`                             //项目类型
	Members      datatypes.JSON `json:"members" form:"members" gorm:"comment:项目成员;column:members;" swaggertype:"object"`                 //项目成员
	CreatedAt    *time.Time     `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                               //创建时间
	UpdatedAt    *time.Time     `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                               //更新时间
}

// TableName 项目信息表 ProjectInfo自定义表名 project_info
func (ProjectInfo) TableName() string {
	return "project_info"
}

// ProjectInfoResponse 项目信息响应结构体，包含映射后的字段
type ProjectInfoResponse struct {
	ProjectInfo
	DepartmentName string   `json:"departmentName"` // 部门名称
	ManagerName    string   `json:"managerName"`    // 项目负责人姓名
	MemberNames    []string `json:"memberNames"`    // 项目成员姓名列表
}
