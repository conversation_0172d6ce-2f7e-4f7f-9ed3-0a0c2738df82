// 自动生成模板DepartmentManagerScore
package score

import (
	"time"
)

// 部门/机构负责人评分 结构体  DepartmentManagerScore
type DepartmentManagerScore struct {
	Id                   *int       `json:"id" form:"id" gorm:"primarykey;comment:主键ID;column:id;size:20;"`                                                                 //主键ID
	AssessmentConfigId   *int       `json:"assessmentConfigId" form:"assessmentConfigId" gorm:"comment:考核配置ID，关联assessment_config.id;column:assessment_config_id;size:20;"` //考核配置ID，关联assessment_config.id
	Username             *string    `json:"username" form:"username" gorm:"comment:被评价用户名;column:username;size:50;"`                                                        //被评价用户名
	DepartmentId         *int       `json:"departmentId" form:"departmentId" gorm:"comment:部门ID，关联org_org.id;column:department_id;size:20;"`                                //部门ID，关联org_org.id
	ManagerScore         *float64   `json:"managerScore" form:"managerScore" gorm:"comment:部门负责人评分;column:manager_score;size:5;"`                                           //部门负责人评分
	BonusAmount          *float64   `json:"bonusAmount" form:"bonusAmount" gorm:"comment:奖金金额;column:bonus_amount;size:12;"`                                                //奖金金额
	ScorerUsername       *string    `json:"scorerUsername" form:"scorerUsername" gorm:"comment:评分人用户名;column:scorer_username;size:50;"`                                     //评分人用户名
	CalculationParameter *string    `json:"calculationParameter" form:"calculationParameter" gorm:"comment:计算参数;column:calculation_parameter;size:50;"`                     //计算参数
	CreatedAt            *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`                                                              //创建时间
	UpdatedAt            *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`                                                              //更新时间
}

// TableName 部门/机构负责人评分 DepartmentManagerScore自定义表名 department_manager_score
func (DepartmentManagerScore) TableName() string {
	return "department_manager_score"
}
