
// 自动生成模板ProjectAssessmentScore
package score
import (
	"time"
)

// 精力分配评分表 结构体  ProjectAssessmentScore
type ProjectAssessmentScore struct {
  Id  *int `json:"id" form:"id" gorm:"primarykey;comment:主键ID;column:id;size:20;"`  //主键ID
  AssessmentConfigId  *int `json:"assessmentConfigId" form:"assessmentConfigId" gorm:"comment:考核配置ID，关联assessment_config.id;column:assessment_config_id;size:20;"`  //考核配置ID，关联assessment_config.id
  Username  *string `json:"username" form:"username" gorm:"comment:被评价用户名;column:username;size:50;"`  //被评价用户名
  ProjectId  *int `json:"projectId" form:"projectId" gorm:"comment:项目ID，关联project_info.id;column:project_id;size:20;"`  //项目ID，关联project_info.id
  AssessmentCoefficient  *float64 `json:"assessmentCoefficient" form:"assessmentCoefficient" gorm:"comment:考核系数;column:assessment_coefficient;size:5;"`  //考核系数
  ManagerScore  *float64 `json:"managerScore" form:"managerScore" gorm:"comment:项目负责人评分;column:manager_score;size:5;"`  //项目负责人评分
  ScorerUsername  *string `json:"scorerUsername" form:"scorerUsername" gorm:"comment:评分人用户名;column:scorer_username;size:50;"`  //评分人用户名
  CalculationParameter  *string `json:"calculationParameter" form:"calculationParameter" gorm:"comment:计算参数（英文字母，如A、B、C等）;column:calculation_parameter;size:10;"`  //计算参数（英文字母，如A、B、C等）
  CreatedAt  *time.Time `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"`  //创建时间
  UpdatedAt  *time.Time `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"`  //更新时间
}


// TableName 精力分配评分表 ProjectAssessmentScore自定义表名 project_assessment_score
func (ProjectAssessmentScore) TableName() string {
    return "project_assessment_score"
}





