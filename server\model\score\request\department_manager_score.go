package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type DepartmentManagerScoreSearch struct {
	request.PageInfo
}

// BatchSubmitDepartmentManagerScoresRequest 批量提交部门负责人评分请求结构体
type BatchSubmitDepartmentManagerScoresRequest struct {
	AssessmentConfigId   int      `json:"assessmentConfigId" binding:"required"`   // 考核配置ID
	Username             string   `json:"username" binding:"required"`             // 被评分员工用户名
	DepartmentId         int      `json:"departmentId" binding:"required"`         // 部门ID
	ManagerScore         *float64 `json:"managerScore"`                            // 部门负责人评分（可为空）
	BonusAmount          *float64 `json:"bonusAmount"`                             // 奖金金额（可为空）
	ScorerUsername       string   `json:"scorerUsername" binding:"required"`       // 评分者用户名
	CalculationParameter string   `json:"calculationParameter" binding:"required"` // 计算参数
}
