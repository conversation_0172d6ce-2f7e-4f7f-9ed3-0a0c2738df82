package api

import (
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/model"
	ReqModel "github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/model/request"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/permission"
	sys_utils "github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var Organizational = new(org)

type org struct{}

// GetOrganizationalPublic 不需要鉴权的组织架构接口
// @Tags Organizational
// @Summary 不需要鉴权的组织架构接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /org/getOrganizationalPublic [get]
func (a *org) GetOrganizationalPublic(c *gin.Context) {
	// err := permission.Init()
	var app = new(permission.Service)

	data := app.Test()
	response.OkWithDetailed(gin.H{
		"data": data,
		"err":  "err",
	}, "获取成功", c)
}

// 同步系统角色
func (a *org) SyncAuthority(c *gin.Context) {
	err := serviceOrganizational.SyncAuthority()
	if err != nil {
		global.GVA_LOG.Error("同步失败!", zap.Error(err))
		response.FailWithMessage("同步失败", c)
	} else {
		response.Ok(c)
	}
}

// 获取系统角色列表
func (a *org) GetSysAuthorityList(c *gin.Context) {
	res, err := serviceOrganizational.GetSysAuthorityList()
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// 设置角色权限
func (a *org) SetAuthorityLevel(c *gin.Context) {
	var req ReqModel.SetAuthorityLevel
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err := serviceOrganizational.SetAuthorityLevel(*req.AuthorityId, *req.Level)
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
	} else {
		response.Ok(c)
	}
}

// 获取组织树terr
func (a *org) GetOrganizationalTree(c *gin.Context) {
	orgid := sys_utils.GetOrgId(c)
	userid := sys_utils.GetUserID(c)
	res, err := serviceOrganizational.GetOrganizationalTree(orgid, userid)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// 创建组织
func (a *org) CreateOrganizational(c *gin.Context) {
	var req ReqModel.Organizational
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	if req.ParentID != nil && req.Type != nil && *req.ParentID == 0 && *req.Type != 1 {
		response.FailWithMessage("根组织类型必须为公司", c)
		return

	}

	err := serviceOrganizational.CreateOrganizational(&model.Organizational{
		Name:     req.Name,
		ParentID: *req.ParentID,
		Sort:     req.Sort,
		Status:   req.Status,
		Type:     req.Type,
	})
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.Ok(c)
	}
}

// 删除组织
func (a *org) DeleteOrganizational(c *gin.Context) {
	OrgId := c.Query("ID")
	if OrgId == "" {
		response.FailWithMessage("参数错误", c)
		return
	}

	err := serviceOrganizational.DeleteOrganizational(OrgId)
	if err != nil {
		global.GVA_LOG.Error("删除失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// 更新组织
func (a *org) UpdateOrganizational(c *gin.Context) {
	var req ReqModel.Organizational
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	if req.ID == nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	if *req.ID == *req.ParentID {
		response.FailWithMessage("上级节点不是能当前节点", c)
		return
	}

	err := serviceOrganizational.UpdateOrganizational(&req)
	if err != nil {
		global.GVA_LOG.Error("更新失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// 获取组织成员(分页)
func (a *org) GetOrganizationalMember(c *gin.Context) {
	var req ReqModel.OrganizationalSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 设置默认分页值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	total, list, err := serviceOrganizational.GetOrganizationalMember(
		req.ID,
		req.Page,
		req.PageSize,
	)
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, "获取成功", c)
}

// 获取所有用户
func (a *org) GetUser(c *gin.Context) {
	res, err := serviceOrganizational.GetUser()
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(res, "获取成功", c)
}

// 添加组织成员
func (a *org) JoinOrganizationalMember(c *gin.Context) {
	var req ReqModel.JoinOrganizationalMember
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	err := serviceOrganizational.JoinOrganizationalMember(req.UserIds, req.OrgId)
	if err != nil {
		global.GVA_LOG.Error("添加失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// 移除组织成员
func (a *org) RemoveOrganizationalMember(c *gin.Context) {
	var req ReqModel.JoinOrganizationalMember
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	err := serviceOrganizational.RemoveOrganizationalMember(req.UserIds, req.OrgId)
	if err != nil {
		global.GVA_LOG.Error("移除失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// 设置组织成员角色
func (a *org) SetOrganizationalMemberAuthority(c *gin.Context) {
	var req ReqModel.SetOrganizationalMemberAuthority
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	err := serviceOrganizational.SetOrganizationalMemberAuthority(req.UserIds, req.OrgId, req.AuthorityId)
	if err != nil {
		global.GVA_LOG.Error("设置失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// 节点管理员获取可支配角色列表
func (a *org) GetNodeAdminAuthorityList(c *gin.Context) {
	userId := sys_utils.GetUserID(c)
	nodeId := sys_utils.GetOrgId(c)
	res, err := serviceOrganizational.GetNodeAdminAuthorityList(userId, nodeId)
	if err != nil {
		global.GVA_LOG.Error("获取失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(res, "获取成功", c)
}

// ChangeLoginOrg 切换当前登录组织节点
func (a *org) ChangeLoginOrg(c *gin.Context) {
	var OrgId ReqModel.OrgId
	if err := c.ShouldBindJSON(&OrgId); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}

	userId := sys_utils.GetUserID(c)
	token, err := serviceOrganizational.ChangeLoginOrg(userId, OrgId.OrgId)
	if err != nil {
		global.GVA_LOG.Error("切换失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	claims := sys_utils.GetUserInfo(c)
	c.Header("new-token", token)
	c.Header("new-expires-at", strconv.FormatInt(claims.ExpiresAt.Unix(), 10))
	sys_utils.SetToken(c, token, int((claims.ExpiresAt.Unix()-time.Now().Unix())/60))
	response.OkWithMessage("切换成功", c)
}

// 获取用户可登录节点列表
func (r *org) GetUserLoginList(c *gin.Context) {
	userId := sys_utils.GetUserID(c)
	list, err := serviceOrganizational.GetUserLoginList(userId)
	if err != nil {
		global.GVA_LOG.Error("获取用户可登录节点列表失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(list, "获取用户可登录节点列表成功", c)
}

// 设置节点管理员
func (a *org) SetNodeAdmin(c *gin.Context) {
	var req ReqModel.SetNodeAdmin
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	err := serviceOrganizational.SetNodeAdmin(req.UserId, req.OrgId, *req.IsAdmin)
	if err != nil {
		global.GVA_LOG.Error("设置失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}

// 设置代理负责人
func (a *org) SetAgentManager(c *gin.Context) {
	var req ReqModel.SetAgentManager
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误:"+err.Error(), c)
		return
	}
	err := serviceOrganizational.SetAgentManager(req.UserId, req.OrgId, *req.IsAgentManager)
	if err != nil {
		global.GVA_LOG.Error("设置代理负责人失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.Ok(c)
}
