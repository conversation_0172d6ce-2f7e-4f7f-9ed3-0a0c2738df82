package initialize

import (
	"context"

	model "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/plugin-tool/utils"
)

func Api(ctx context.Context) {
	entities := []model.SysApi{{Path: "/org/syncAuthority", Description: "同步权限", ApiGroup: "组织", Method: "PUT"}, {Path: "/org/setAuthorityLevel", Description: "设置权限等级", ApiGroup: "组织", Method: "PUT"}, {Path: "/org/createOrganizational", Description: "创建组织", ApiGroup: "组织", Method: "POST"}, {Path: "/org/getOrganizationalTree", Description: "获取组织树", ApiGroup: "组织", Method: "GET"}, {Path: "/org/deleteOrganizational", Description: "删组织", ApiGroup: "组织", Method: "DELETE"}, {Path: "/org/updateOrganizational", Description: "改组织", ApiGroup: "组织", Method: "PUT"}, {Path: "/org/getOrganizationalMember", Description: "获取组织成员", ApiGroup: "组织", Method: "GET"}, {Path: "/org/getUser", Description: "获取用户", ApiGroup: "组织", Method: "GET"}, {Path: "/org/joinOrganizationalMember", Description: "加入组织", ApiGroup: "组织", Method: "POST"}, {Path: "/org/removeOrganizationalMember", Description: "移除成员", ApiGroup: "组织", Method: "DELETE"}, {Path: "/org/getSysAuthorityList", Description: "获取系统权限", ApiGroup: "组织", Method: "GET"}, {Path: "/org/getNodeAdminAuthorityList", Description: "获取节点管理员权限", ApiGroup: "组织", Method: "GET"}, {Path: "/org/test", Description: "测试API", ApiGroup: "组织", Method: "GET"}, {Path: "/org/setOrganizationalMemberAuthority", Description: "设置组织成员权限", ApiGroup: "组织", Method: "PUT"}, {Path: "/org/changeLoginOrg", Description: "变更登录组织(必选)", ApiGroup: "组织", Method: "PUT"}, {Path: "/org/getUserLoginList", Description: "获取登录列表(必选)", ApiGroup: "组织", Method: "GET"}, {Path: "/org/setNodeAdmin", Description: "设置节点管理员", ApiGroup: "组织", Method: "PUT"}}
	utils.RegisterApis(entities...)
}
