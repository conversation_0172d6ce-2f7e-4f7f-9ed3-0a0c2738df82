package initialize

import (
	"context"
	model "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/plugin-tool/utils"
)

func Menu(ctx context.Context) {
	entities := []model.SysBaseMenu{{ParentId: 0, Path: "organizationalMenu", Name: "organizationalMenu", Hidden: false, Component: "view/routerHolder.vue", Sort: 0, Meta: model.Meta{Title: "组织架构管理", Icon: "school"}}, {ParentId: 0, Path: "org", Name: "org", Hidden: false, Component: "plugin/organizational/view/organizational.vue", Sort: 0, Meta: model.Meta{Title: "组织架构", Icon: "user-filled"}}, {ParentId: 0, Path: "syncAuthority", Name: "syncAuthority", Hidden: false, Component: "plugin/organizational/view/SyncAuthority.vue", Sort: 0, Meta: model.Meta{Title: "角色权限设置", Icon: "operation"}}}
	utils.RegisterMenus(entities...)
}
