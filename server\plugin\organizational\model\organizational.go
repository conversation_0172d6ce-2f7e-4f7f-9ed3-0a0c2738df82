package model

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// Organizational 组织架构 结构体
type Organizational struct {
	global.GVA_MODEL
	Name     *string           `json:"name" gorm:"column:name;comment:名称" `
	Type     *int              `json:"type" gorm:"column:type;comment:类型"` //类型 1:公司 2:部门 3:其它
	ParentID uint              `json:"parent_id" gorm:"column:parent_id;comment:父节点;index:parent_id"`
	Sort     *int              `json:"sort" gorm:"column:sort;comment:排序"`
	Status   *int              `json:"status" form:"status" gorm:"default:1;column:status;comment:状态"` //状态 1:正常 2:禁用
	Children []*Organizational `json:"children" gorm:"-"`
}

// TableName 组织架构 Organizational自定义表名 org_org
func (Organizational) TableName() string {
	return "org_org"
}

// 自定义关联表
type OrgOrganizationalUser struct {
	OrgID          *uint                `json:"org_id" gorm:"column:org_id;comment:组织ID"`
	Organizational *Organizational      `json:"organizational" gorm:"foreignKey:ID;references:OrgID;"`
	UserID         *uint                `json:"user_id" gorm:"column:user_id;comment:用户ID"`
	User           *system.SysUser      `json:"user" gorm:"foreignKey:UserID;references:id;"`
	AuthorityId    *uint                `json:"authority_id" gorm:"column:authority_id;comment:角色ID"`
	Authority      *system.SysAuthority `json:"authority" gorm:"foreignKey:AuthorityId;references:AuthorityId;"`
	IsAdmin        *bool                `json:"is_admin" gorm:"column:is_admin;comment:是否管理员"`
	IsAgentManager *bool                `json:"is_agent_manager" gorm:"column:is_agent_manager;comment:是否为代理负责人"`
}

func (OrgOrganizationalUser) TableName() string {
	return "org_organizational_user"
}

// 角色表权限表
type OrgAuthorityPermission struct {
	AuthorityId *uint                `json:"authority_id" gorm:"column:authority_id;comment:角色ID"`
	ParentID    *uint                `json:"parent_id" gorm:"column:parent_id;comment:父节点"`
	Authority   *system.SysAuthority `json:"authority" gorm:"foreignKey:AuthorityId;references:AuthorityId;"`
	Level       *int                 `json:"level" gorm:"default:0;column:level;comment:权限等级"` // 0:无权限
}

func (OrgAuthorityPermission) TableName() string {
	return "org_authority_permission"
}
