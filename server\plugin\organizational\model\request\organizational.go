package request

type PageInfo struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"pageSize" form:"pageSize"`
}

type OrganizationalSearch struct {
	PageInfo
	ID uint `json:"ID" form:"ID"`
}
type SetAuthorityLevel struct {
	AuthorityId *uint `json:"authority_id" binding:"required" `
	Level       *int  `json:"level" binding:"required" `
}

type Organizational struct {
	ID       *uint   `json:"ID" `
	Name     *string `json:"name" binding:"required"`
	Type     *int    `json:"type" ` //类型 1:公司 2:部门 3:其它
	ParentID *uint   `json:"parent_id" binding:"required"`
	Sort     *int    `json:"sort" `
	Status   *int    `json:"status" ` //状态 1:正常 2:禁用
}

type JoinOrganizationalMember struct {
	UserIds []uint `json:"user_ids" binding:"required" `
	OrgId   uint   `json:"org_id" binding:"required" `
}
type SetOrganizationalMemberAuthority struct {
	UserIds     []uint `json:"user_ids" binding:"required" `
	OrgId       uint   `json:"org_id" binding:"required" `
	AuthorityId uint   `json:"authority_id" binding:"required" `
}

type OrgId struct {
	OrgId uint `json:"org_id" binding:"required" `
}

// //设置节点管理员
type SetNodeAdmin struct {
	UserId  uint  `json:"user_id" binding:"required" `
	OrgId   uint  `json:"org_id" binding:"required" `
	IsAdmin *bool `json:"is_admin" binding:"required" `
}

// 设置代理负责人
type SetAgentManager struct {
	UserId         uint  `json:"user_id" binding:"required" `
	OrgId          uint  `json:"org_id" binding:"required" `
	IsAgentManager *bool `json:"is_agent_manager" binding:"required" `
}
