package permission

import (
	"fmt"
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/utils"
)

type Level int
type NODE_TYPE int

const (
	NONE            Level = 0 //无权限
	ALL             Level = 1 //所有权限
	COMPANY         Level = 2 //本公司及子公司
	COMPANY_ONLY    Level = 3 //本公司
	DEPARTMENT      Level = 4 //部门及子部门 本公司范围
	DEPARTMENT_ONLY Level = 5 //本部门
	SELF            Level = 6 //仅自己
)

// 组织架构节点类型
const (
	COMPANY_TYPE    NODE_TYPE = 1 // 公司
	DEPARTMENT_TYPE NODE_TYPE = 2 // 部门
)

type Permission struct {
	mu sync.RWMutex // 读写锁保护所有缓存字段

	AuthorityLevelCache map[uint]Level //角色id -> 权限等级
	//用户在该组织的权限等级
	NodeUserLevelCache map[uint]map[uint]Level // 节点id -> 用户id -> 权限等级

	/*
		该节点所属公司ID
	*/
	NodeCompanyIDCache map[uint]uint // 节点id -> 公司id

	/*
		组织架构树 含子公司
		key:节点id
		说明:该map存储了所有节点的children 含子公司
	*/
	OrgTerrCache map[uint]*model.Organizational // 组织架构树

	/*
		组织架构树 不含子公司
		key:节点id
		说明:该map存储了所有节点的children 不含子公司
	*/
	OrgTerrNoSubsCache map[uint]*model.Organizational

	/*
		组织展平 含子公司  展平 ids
		key:节点id
		说明:该map存储了所有节点 的所有子节点ids
		如 nodeId = 1  ->  [1,2,3,4,5,6,7,8,9,10]
		如 nodeId = 5  ->  [5,6,8,10]
	*/
	OrgTerrIDsCache map[uint][]uint

	/*
		组织展平 不含子公司 ids
		key:节点id
		说明:该map存储了所有节点 的所有子节点ids 不含子公司
		如 nodeId = 1  ->  [1,2,3,6,7,9,10]
		如 nodeId = 5  ->  [5,6,10]
	*/
	OrgTerrNoSubsIDsCache map[uint][]uint
}
type Service struct{}

var (
	permissionService = Permission{}
)

// 输出缓存
func (s *Service) Test() map[string]interface{} {
	return map[string]interface{}{
		"NodeUserLevelCache": permissionService.NodeUserLevelCache,
		"OrgTerrNoSubsCache": permissionService.OrgTerrNoSubsCache,
	}
}
func (s *Service) Init() (err error) {
	permissionService.mu.Lock()
	defer permissionService.mu.Unlock()

	// 初始化缓存
	permissionService.AuthorityLevelCache = make(map[uint]Level)
	permissionService.NodeUserLevelCache = make(map[uint]map[uint]Level) //第二层map未初始化 直接访问会panic
	permissionService.NodeCompanyIDCache = make(map[uint]uint)
	permissionService.OrgTerrCache = make(map[uint]*model.Organizational)
	permissionService.OrgTerrIDsCache = make(map[uint][]uint)
	permissionService.OrgTerrNoSubsCache = make(map[uint]*model.Organizational)
	permissionService.OrgTerrNoSubsIDsCache = make(map[uint][]uint)

	var AuthorityLevel []*model.OrgAuthorityPermission
	err = global.GVA_DB.Model(&model.OrgAuthorityPermission{}).Find(&AuthorityLevel).Error
	if err != nil {
		return fmt.Errorf("获取角色权限失败: %v", err)
	}
	authorityLevelMap := make(map[uint]Level)
	for _, v := range AuthorityLevel {
		if v.Level != nil {
			authorityLevelMap[*v.AuthorityId] = Level(*v.Level)
		} else {
			authorityLevelMap[*v.AuthorityId] = Level(0)
		}
	}
	permissionService.AuthorityLevelCache = authorityLevelMap

	var user []*model.OrgOrganizationalUser
	err = global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Find(&user).Error
	if err != nil {
		return fmt.Errorf("获取用户权限失败: %v", err)
	}

	NodeUserLevelCache := make(map[uint]map[uint]Level)
	for _, v := range user {
		if NodeUserLevelCache[*v.OrgID] == nil {
			NodeUserLevelCache[*v.OrgID] = make(map[uint]Level)
		}
		if v.AuthorityId != nil {
			NodeUserLevelCache[*v.OrgID][*v.UserID] = authorityLevelMap[*v.AuthorityId]
		} else {
			NodeUserLevelCache[*v.OrgID][*v.UserID] = Level(0)
		}
	}

	permissionService.NodeUserLevelCache = NodeUserLevelCache

	var org []*model.Organizational
	err = global.GVA_DB.Model(&model.Organizational{}).Find(&org).Error
	if err != nil {
		return fmt.Errorf("获取组织架构失败: %v", err)
	}

	//复制一份org
	org2 := utils.OrgCopy(org)

	//构建组织架构树 含子公司 及 不含子公司
	permissionService.OrgTerrCache = utils.BuildTreeMap(org)
	permissionService.OrgTerrNoSubsCache = utils.BuildTreeMapNoSubs(org2)

	//展平组织架构树ID  含子公司 及 不含子公司
	permissionService.OrgTerrIDsCache = utils.DFS(permissionService.OrgTerrCache)
	permissionService.OrgTerrNoSubsIDsCache = utils.DFS(permissionService.OrgTerrNoSubsCache)

	//获取节点的companyID
	permissionService.NodeCompanyIDCache = utils.BuildNodeCompanyIDsMap(permissionService.OrgTerrCache)

	return nil
}

// GetAuthorityLevel 安全获取角色权限等级
func (s *Service) GetAuthorityLevel(authorityID uint) (Level, bool) {
	permissionService.mu.RLock()
	defer permissionService.mu.RUnlock()

	v, ok := permissionService.AuthorityLevelCache[authorityID]
	return v, ok
}

// GetNodeCompanyID 安全获取节点所属公司ID
func (s *Service) GetNodeCompanyID(nodeID uint) (uint, bool) {
	permissionService.mu.RLock()
	defer permissionService.mu.RUnlock()

	companyID, ok := permissionService.NodeCompanyIDCache[nodeID]
	return companyID, ok
}

// 安全获取用户在该组织的权限等级
func (s *Service) GetUserAuthorityLevel(userID uint, orgID uint) (Level, bool) {
	permissionService.mu.RLock()
	defer permissionService.mu.RUnlock()

	authorityLevel, ok := permissionService.NodeUserLevelCache[orgID][userID]
	return authorityLevel, ok
}

// 安全获取节点及子节点 Ids
func (s *Service) GetOrgTerrIDs(nodeID uint, sub bool) ([]uint, bool) {
	permissionService.mu.RLock()
	defer permissionService.mu.RUnlock()
	if sub {
		ids, ok := permissionService.OrgTerrIDsCache[nodeID]
		return ids, ok
	} else {
		ids, ok := permissionService.OrgTerrNoSubsIDsCache[nodeID]
		return ids, ok
	}
}

// 安全获取组织架构树
func (s *Service) GetOrgTerr(nodeID uint, sub bool) (model.Organizational, bool) {
	permissionService.mu.RLock()
	defer permissionService.mu.RUnlock()

	if sub {
		if org, ok := permissionService.OrgTerrCache[nodeID]; ok {
			return *org, ok
		} else {
			return model.Organizational{}, false
		}
	} else {
		if org, ok := permissionService.OrgTerrNoSubsCache[nodeID]; ok {
			return *org, ok
		} else {
			return model.Organizational{}, false
		}
	}
}

// 安全获取多节点组织成员(去重)
func (s *Service) GetNodesUserIDs(nodeIDs []uint) ([]uint, bool) {
	if len(nodeIDs) == 0 {
		return nil, false
	}

	permissionService.mu.RLock()
	defer permissionService.mu.RUnlock()

	seen := make(map[uint]struct{})
	var CheckuserIDs []uint
	for _, v := range nodeIDs {
		UserIDsMap, ok := permissionService.NodeUserLevelCache[v]
		if !ok {
			continue
		}
		for k := range UserIDsMap {
			if _, exists := seen[k]; !exists {
				seen[k] = struct{}{}
				CheckuserIDs = append(CheckuserIDs, k)
			}
		}
	}

	return CheckuserIDs, true
}

// 更新缓存 部门成员 NodeUserLevelCache 删除成员
func (s *Service) CacheDelNodeUser(nodeID uint, userID uint) {
	permissionService.mu.Lock()
	defer permissionService.mu.Unlock()

	if _, ok := permissionService.NodeUserLevelCache[nodeID]; ok {
		delete(permissionService.NodeUserLevelCache[nodeID], userID)
	}
}

// 更新缓存 部门成员 NodeUserLevelCache 新增成员
func (s *Service) CacheAddNodeUser(nodeID uint, userID uint, AuthorityId uint) {
	permissionService.mu.Lock()
	defer permissionService.mu.Unlock()

	if permissionService.NodeUserLevelCache[nodeID] == nil {
		permissionService.NodeUserLevelCache[nodeID] = make(map[uint]Level)
	}

	Level := permissionService.AuthorityLevelCache[AuthorityId]

	permissionService.NodeUserLevelCache[nodeID][userID] = Level
}

// 更新缓存 部门成员 NodeUserLevelCache 修改成员Level
func (s *Service) CacheUpdateNodeUserLevel(nodeID uint, userID uint, AuthorityId uint) {
	permissionService.mu.Lock()
	defer permissionService.mu.Unlock()

	Level := permissionService.AuthorityLevelCache[AuthorityId]
	permissionService.NodeUserLevelCache[nodeID][userID] = Level
}

/*
CheckPermissionNodeIds 辅助函数
  - 参数:Level 节点id 访问节点ids
  - 返回:bool
*/
func (s *Service) CheckPermissionNodeIds(level Level, nodeID uint, ToNodeIds []uint) bool {
	switch level {
	case NONE:
		return false
	case ALL:
		return true
	case COMPANY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return false
		}
		CheckNodeIds, ok := s.GetOrgTerrIDs(companyID, true)
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToNodeIds, CheckNodeIds)
		return len(only) == 0
	case COMPANY_ONLY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return false
		}
		CheckNodeIds, ok := s.GetOrgTerrIDs(companyID, false)
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToNodeIds, CheckNodeIds)
		return len(only) == 0

	case DEPARTMENT:
		CheckNodeIds, ok := s.GetOrgTerrIDs(nodeID, false)
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToNodeIds, CheckNodeIds)
		return len(only) == 0
	case DEPARTMENT_ONLY, SELF:
		if len(ToNodeIds) > 0 {
			return nodeID == ToNodeIds[0]
		}
		return false
	default:
		return false
	}
}

/*
CheckPermissionUserIds 辅助函数
  - 参数:Level 节点id 访问UserIds
  - 返回:bool
*/
func (s *Service) CheckPermissionUserIds(level Level, nodeID uint, userID uint, ToUserIds []uint) bool {
	switch level {
	case NONE:
		return false
	case ALL:
		return true
	case COMPANY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return false
		}
		CheckNodeIds, ok := s.GetOrgTerrIDs(companyID, true)
		if !ok {
			return false
		}
		CheckuserIDs, ok := s.GetNodesUserIDs(CheckNodeIds)
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToUserIds, CheckuserIDs)
		return len(only) == 0
	case COMPANY_ONLY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return false
		}
		CheckNodeIds, ok := s.GetOrgTerrIDs(companyID, false)
		if !ok {
			return false
		}
		CheckuserIDs, ok := s.GetNodesUserIDs(CheckNodeIds)
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToUserIds, CheckuserIDs)
		return len(only) == 0
	case DEPARTMENT:
		CheckNodeIds, ok := s.GetOrgTerrIDs(nodeID, false)
		if !ok {
			return false
		}
		CheckuserIDs, ok := s.GetNodesUserIDs(CheckNodeIds)
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToUserIds, CheckuserIDs)
		return len(only) == 0
	case DEPARTMENT_ONLY:
		CheckuserIDs, ok := s.GetNodesUserIDs([]uint{nodeID})
		if !ok {
			return false
		}
		only, _, _ := utils.DiffSets(ToUserIds, CheckuserIDs)
		return len(only) == 0
	case SELF:
		if len(ToUserIds) > 0 {
			return userID == ToUserIds[0]
		}
		return false
	default:
		return false
	}

}

// 判断当前用户是否有部门或用户权限
//   - 参数: JWT当前登录用户id JWT当前登录节点id 访问节点ids 访问用户ids
//   - 返回: bool
//   - 说明: ToNodeIds ToUserIds 不能同时为空
func (s *Service) CheckPermission(userID uint, nodeID uint, ToNodeIds []uint, ToUserIds []uint) bool {
	if len(ToNodeIds) == 0 && len(ToUserIds) == 0 {
		return false
	}
	level, ok := s.GetUserAuthorityLevel(userID, nodeID)
	if !ok {
		return false
	}

	if len(ToNodeIds) > 0 {
		ok = s.CheckPermissionNodeIds(level, nodeID, ToNodeIds)
	}

	if len(ToUserIds) > 0 && ok {
		ok = s.CheckPermissionUserIds(level, nodeID, userID, ToUserIds)
	}
	return ok
}

/*
节点管理员获取可支配角色列表
  - 参数:JWT当前登录用户id JWT当前登录节点id
  - 返回:[]*model.OrgAuthorityPermission , error
*/
func (s *Service) GetNodeAdminAuthorityList(userID uint, nodeID uint) (list []*model.OrgAuthorityPermission, err error) {
	level, ok := s.GetUserAuthorityLevel(userID, nodeID)
	if !ok {
		return nil, fmt.Errorf("用户权限不足")
	}

	var AuthorityIds []Level
	switch level {
	// case NONE:
	case ALL:
		AuthorityIds = []Level{NONE, ALL, COMPANY, COMPANY_ONLY, DEPARTMENT, DEPARTMENT_ONLY, SELF}
	case COMPANY:
		AuthorityIds = []Level{COMPANY, COMPANY_ONLY, DEPARTMENT, DEPARTMENT_ONLY, SELF}
	case COMPANY_ONLY:
		AuthorityIds = []Level{COMPANY_ONLY, DEPARTMENT, DEPARTMENT_ONLY, SELF}
	case DEPARTMENT:
		AuthorityIds = []Level{DEPARTMENT, DEPARTMENT_ONLY, SELF}
	case DEPARTMENT_ONLY:
		AuthorityIds = []Level{DEPARTMENT_ONLY, SELF}
	case SELF:
		AuthorityIds = []Level{SELF}

	default:
		return nil, fmt.Errorf("用户权限不足")
	}

	err = global.GVA_DB.Model(&model.OrgAuthorityPermission{}).Where("level in ?", AuthorityIds).Preload("Authority").Find(&list).Error
	return
}

/*
节点管理员获取可支配节点ids
- 参数:JWT当前登录用户id JWT当前登录节点id
- 返回:[]uint, error
*/
func (s *Service) GetNodeAdminNodeIds(userID uint, nodeID uint) (nodeIds []uint, err error) {
	level, ok := s.GetUserAuthorityLevel(userID, nodeID)
	if !ok {
		return nil, fmt.Errorf("用户权限不足")
	}
	switch level {
	case ALL:
		var Ids []uint
		global.GVA_DB.Model(&model.Organizational{}).Where("parent_id = ?", 0).Pluck("id", &Ids)
		for _, v := range Ids {
			o, ok := s.GetOrgTerrIDs(v, true)
			if ok {
				nodeIds = append(nodeIds, o...)
			}
		}
	case COMPANY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		nodeIds, ok := s.GetOrgTerrIDs(companyID, true)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		return nodeIds, nil
	case COMPANY_ONLY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		nodeIds, ok := s.GetOrgTerrIDs(companyID, false)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		return nodeIds, nil
	case DEPARTMENT:
		nodeIds, ok := s.GetOrgTerrIDs(nodeID, false)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		return nodeIds, nil
	case DEPARTMENT_ONLY:
		return []uint{nodeID}, nil

	default: //NONE SELF
		return nil, fmt.Errorf("用户权限不足")

	}
	return
}

/*
节点管理员获取可支配用户ids
- 参数:JWT当前登录用户id JWT当前登录节点id
- 返回:[]uint, error
*/
func (s *Service) GetNodeAdminUserIds(userID uint, nodeID uint) (userIds []uint, err error) {
	level, ok := s.GetUserAuthorityLevel(userID, nodeID)
	if !ok {
		return nil, fmt.Errorf("用户权限不足")
	}
	switch level {
	case ALL:
		var Ids []uint
		global.GVA_DB.Model(&model.Organizational{}).Where("parent_id = ?", 0).Pluck("id", &Ids)
		var nIds []uint
		for _, v := range Ids {
			o, ok := s.GetOrgTerrIDs(v, true)
			if ok {
				nIds = append(nIds, o...)
			}
		}
		userIds, ok = s.GetNodesUserIDs(nIds)
		if !ok {
			return nil, fmt.Errorf("用户权限不足")
		}

		return userIds, nil
	case COMPANY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		nIds, ok := s.GetOrgTerrIDs(companyID, true)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		userIds, ok := s.GetNodesUserIDs(nIds)
		if !ok {
			return nil, fmt.Errorf("用户权限不足")
		}
		return userIds, nil
	case COMPANY_ONLY:
		companyID, ok := s.GetNodeCompanyID(nodeID)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		nIds, ok := s.GetOrgTerrIDs(companyID, false)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		userIds, ok := s.GetNodesUserIDs(nIds)
		if !ok {
			return nil, fmt.Errorf("用户权限不足")
		}
		return userIds, nil
	case DEPARTMENT:
		nIds, ok := s.GetOrgTerrIDs(nodeID, false)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		userIds, ok := s.GetNodesUserIDs(nIds)
		if !ok {
			return nil, fmt.Errorf("用户权限不足")
		}
		return userIds, nil
	case DEPARTMENT_ONLY:
		userIds, ok := s.GetNodesUserIDs([]uint{nodeID})
		if !ok {
			return nil, fmt.Errorf("用户权限不足")
		}
		return userIds, nil
	case SELF:
		return []uint{userID}, nil

	default: //NONE
		return nil, fmt.Errorf("用户权限不足")
	}
}
