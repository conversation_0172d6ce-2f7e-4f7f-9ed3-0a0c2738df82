package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

var Organizational = new(org)

type org struct{}

// Init 初始化 组织架构 路由信息
func (r *org) Init(public *gin.RouterGroup, private *gin.RouterGroup) {
	{
		group := private.Group("org").Use(middleware.OperationRecord())
		group.PUT("syncAuthority", apiOrganizational.SyncAuthority)                                       // 同步系统角色
		group.PUT("setAuthorityLevel", apiOrganizational.SetAuthorityLevel)                               // 设置角色权限
		group.POST("createOrganizational", apiOrganizational.CreateOrganizational)                        // 创建组织
		group.DELETE("deleteOrganizational", apiOrganizational.DeleteOrganizational)                      // 删除组织
		group.PUT("updateOrganizational", apiOrganizational.UpdateOrganizational)                         // 更新组织
		group.POST("joinOrganizationalMember", apiOrganizational.JoinOrganizationalMember)                // 添加组织成员
		group.DELETE("removeOrganizationalMember", apiOrganizational.RemoveOrganizationalMember)          // 移除组织成员
		group.PUT("setOrganizationalMemberAuthority", apiOrganizational.SetOrganizationalMemberAuthority) // 设置组织成员角色
		group.PUT("changeLoginOrg", apiOrganizational.ChangeLoginOrg)                                     // 切换登录组织
		group.PUT("setNodeAdmin", apiOrganizational.SetNodeAdmin)                                         // 设置节点管理员
		group.PUT("setAgentManager", apiOrganizational.SetAgentManager)                                   // 设置代理负责人

	}
	{
		group := private.Group("org")
		group.GET("getSysAuthorityList", apiOrganizational.GetSysAuthorityList)             // 获取系统角色列表
		group.GET("getOrganizationalTree", apiOrganizational.GetOrganizationalTree)         // 获取组织树terr
		group.GET("getOrganizationalMember", apiOrganizational.GetOrganizationalMember)     // 获取组织成员
		group.GET("getUser", apiOrganizational.GetUser)                                     // 获取用户列表
		group.GET("getNodeAdminAuthorityList", apiOrganizational.GetNodeAdminAuthorityList) // 获取节点管理员角色列表
		group.GET("getUserLoginList", apiOrganizational.GetUserLoginList)                   // 获取用户可登录节点列表

	}
	{
		group := public.Group("org")
		group.GET("test", apiOrganizational.GetOrganizationalPublic) // 组织架构开放接口
	}
}
