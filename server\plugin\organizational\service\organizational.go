package service

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/model"
	ReqModel "github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/model/request"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/permission"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/utils"
	sys_utils "github.com/flipped-aurora/gin-vue-admin/server/utils"

	"gorm.io/gorm"
)

var Organizational = new(org)

type org struct{}

func (s *org) GetOrganizationalPublic() {

}

// 同步系统角色
func (s *org) SyncAuthority() error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) (err error) {
		var sys_authorities []system.SysAuthority
		var org_authority []model.OrgAuthorityPermission
		tx.Find(&sys_authorities)
		tx.Find(&org_authority)

		var sysIds []uint
		var orgIds []uint
		sysMap := make(map[uint]*system.SysAuthority, len(sys_authorities))
		orgMap := make(map[uint]*model.OrgAuthorityPermission, len(org_authority))

		for i, v := range sys_authorities {
			sysIds = append(sysIds, v.AuthorityId)
			sysMap[v.AuthorityId] = &sys_authorities[i]
		}
		for i, v := range org_authority {
			orgIds = append(orgIds, *v.AuthorityId)
			orgMap[*v.AuthorityId] = &org_authority[i]
		}

		ToAdd, ToDel, ToUpdata := utils.DiffSets(sysIds, orgIds)
		if len(ToAdd) > 0 {
			for _, v := range ToAdd {
				if err = s.AddAuthority(tx, sysMap[v]); err != nil {
					return err
				}
			}
		}
		if len(ToDel) > 0 {
			for _, v := range ToDel {
				if err = s.DeleteAuthority(tx, v); err != nil {
					return err
				}
			}
		}
		if len(ToUpdata) > 0 {
			for _, v := range ToUpdata {
				if err = s.UpdateAuthority(tx, v, *orgMap[v].ParentID); err != nil {
					return err
				}
			}
		}
		PermissionService.Init() // 更新缓存
		return nil
	})
}

// 添加角色
func (s *org) AddAuthority(tx *gorm.DB, authority *system.SysAuthority) error {
	return tx.Create(&model.OrgAuthorityPermission{
		AuthorityId: &authority.AuthorityId,
		ParentID:    authority.ParentId,
	}).Error
}

// 更新角色
func (s *org) UpdateAuthority(tx *gorm.DB, AuthorityId uint, ParentId uint) error {
	return tx.Model(&model.OrgAuthorityPermission{}).Where("authority_id = ?", AuthorityId).Update("parent_id", ParentId).Error
}

// 删除角色
func (s *org) DeleteAuthority(tx *gorm.DB, AuthorityId uint) error {
	return tx.Where("authority_id = ?", AuthorityId).Delete(&model.OrgAuthorityPermission{}).Error
}

// 获取系统角色列表
func (s *org) GetSysAuthorityList() (list []*model.OrgAuthorityPermission, err error) {
	err = global.GVA_DB.Model(&model.OrgAuthorityPermission{}).Order("authority_id").Preload("Authority").Find(&list).Error
	return
}

// 设置角色level
func (s *org) SetAuthorityLevel(AuthorityId uint, Level int) error {
	err := global.GVA_DB.Model(&model.OrgAuthorityPermission{}).Where("authority_id = ?", AuthorityId).Update("level", Level).Error
	if err != nil {
		return err
	}
	PermissionService.Init() // 更新缓存
	return err
}

// 获取组织树
func (s *org) GetOrganizationalTree(orgid uint, userid uint) (tree []*model.Organizational, err error) {
	level, ok := PermissionService.GetUserAuthorityLevel(userid, orgid)
	if !ok {
		return nil, fmt.Errorf("用户未加入组织")
	}

	switch level {
	case permission.ALL:
		var root []uint
		global.GVA_DB.Model(&model.Organizational{}).Where("parent_id = ?", 0).Pluck("id", &root)
		for _, v := range root {
			o, ok := PermissionService.GetOrgTerr(v, true)
			if ok {
				tree = append(tree, &o)
			}
		}
		return tree, nil
	case permission.COMPANY:
		cid, ok := PermissionService.GetNodeCompanyID(orgid)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		o, ok := PermissionService.GetOrgTerr(cid, true)
		if ok {
			tree = append(tree, &o)
		}
		return tree, nil
	case permission.COMPANY_ONLY:
		cid, ok := PermissionService.GetNodeCompanyID(orgid)
		if !ok {
			return nil, fmt.Errorf("获取公司失败")
		}
		o, ok := PermissionService.GetOrgTerr(cid, false)
		if ok {
			tree = append(tree, &o)
		}
		return tree, nil
	case permission.DEPARTMENT:
		o, ok := PermissionService.GetOrgTerr(orgid, false)
		if ok {
			tree = append(tree, &o)
		}
		return tree, nil
	case permission.DEPARTMENT_ONLY:
		err = global.GVA_DB.Model(&model.Organizational{}).Where("id = ?", orgid).Find(&tree).Error
		if err != nil {
			return nil, err
		}
	default:
		return
	}
	return
}

// 创建组织
func (s *org) CreateOrganizational(organizational *model.Organizational) error {
	err := global.GVA_DB.Create(organizational).Error
	if err != nil {
		return err
	}
	PermissionService.Init() // 更新缓存
	return err
}

// 删除组织
func (s *org) DeleteOrganizational(ID string) (err error) {
	var count int64
	err = global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ?", ID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("当前节点存在用户")
	}

	err = global.GVA_DB.Model(&model.Organizational{}).Where("parent_id = ?", ID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("当前节点存在子节点")
	}

	err = global.GVA_DB.Delete(&model.Organizational{}, ID).Error
	if err != nil {
		return err
	}
	PermissionService.Init() // 更新缓存
	return err
}

// 更新组织
func (s *org) UpdateOrganizational(req *ReqModel.Organizational) (err error) {
	var org model.Organizational
	if err = global.GVA_DB.Where("id = ?", req.ID).First(&org).Error; err != nil {
		return err
	}

	// 检查父节点是否存在
	if req.ParentID != nil && *req.ParentID != 0 {
		var p_count int64
		if err = global.GVA_DB.Model(&model.Organizational{}).Where("id = ?", *req.ParentID).Count(&p_count).Error; err != nil {
			return err
		}
		if p_count == 0 {
			return fmt.Errorf("父节点不存在")
		}
	}

	if org.ParentID == 0 {
		var count int64
		if err = global.GVA_DB.Model(&model.Organizational{}).Where("parent_id = ?", 0).Count(&count).Error; err != nil {
			return err
		}

		if count <= 1 {
			if req.ParentID != nil && *req.ParentID != 0 {
				return fmt.Errorf("唯一的根节点,不允许修改父节点")
			}
			if req.Type != nil && *req.Type != 1 {
				return fmt.Errorf("唯一的根节点,不允许修改节点类型")
			}
		} else {
			if req.ParentID != nil && *req.ParentID == 0 {
				if req.Type != nil && *req.Type != 1 {
					return fmt.Errorf("根节点,不允许修改节点类型")
				}
			}
		}
	}
	err = global.GVA_DB.Model(&model.Organizational{}).Where("id = ?", org.ID).Updates(req).Error
	if err != nil {
		return err
	}
	PermissionService.Init()
	return
}

// 获取组织成员(分页)
func (s *org) GetOrganizationalMember(ID uint, page, pageSize int) (total int64, list []*model.OrgOrganizationalUser, err error) {
	db := global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ?", ID)

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return 0, nil, err
	}

	// 分页查询
	err = db.Preload("User").
		Preload("Authority").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&list).Error

	return total, list, err
}

// 获取所有用户
func (s *org) GetUser() (list []*system.SysUser, err error) {
	err = global.GVA_DB.Select("id", "nick_name").Model(&system.SysUser{}).Find(&list).Error
	return
}

// 添加组织成员
func (s *org) JoinOrganizationalMember(UserIds []uint, orgId uint) (err error) {
	//检查部门状态
	var org model.Organizational
	if err = global.GVA_DB.Where("id = ?", orgId).First(&org).Error; err != nil {
		return fmt.Errorf("查询部门错误或不存在")
	}
	if *org.Status == 2 {
		return fmt.Errorf("部门已禁用")
	}

	//检查成员
	var addUserIds []uint
	global.GVA_DB.Model(&system.SysUser{}).Where("id in ?", UserIds).Pluck("id", &addUserIds)
	if len(addUserIds) == 0 {
		return fmt.Errorf("成员不存在")
	}

	//已存在成员
	var existUserIds []uint
	global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ? and user_id in ?", orgId, UserIds).
		Pluck("user_id", &existUserIds)

	ToAdd, _, _ := utils.DiffSets(addUserIds, existUserIds)
	if len(ToAdd) > 0 {
		var user []model.OrgOrganizationalUser
		for _, v := range ToAdd {
			user = append(user, model.OrgOrganizationalUser{
				UserID: &v,
				OrgID:  &orgId,
			})
		}
		err = global.GVA_DB.Create(&user).Error
		if err != nil {
			return err
		}
		for _, v := range ToAdd {
			PermissionService.CacheAddNodeUser(orgId, v, 0)
		}
	}

	return
}

// 移除组织成员
func (s *org) RemoveOrganizationalMember(UserIds []uint, orgId uint) (err error) {
	err = global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ? and user_id in ?", orgId, UserIds).
		Delete(&model.OrgOrganizationalUser{}).Error
	if err != nil {
		return err
	}
	for _, v := range UserIds {
		PermissionService.CacheDelNodeUser(orgId, v)
	}
	return
}

// 设置组织成员权限
func (s *org) SetOrganizationalMemberAuthority(UserIds []uint, orgId uint, AuthorityId uint) (err error) {
	var user []model.OrgOrganizationalUser
	err = global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ? and user_id in ?", orgId, UserIds).Find(&user).Error
	if err != nil {
		return err
	}

	var pIds []uint
	for i := 0; i < len(user); i++ {
		user[i].AuthorityId = &AuthorityId
		pIds = append(pIds, *user[i].UserID)
	}

	err = global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ? and user_id in ?", orgId, UserIds).Update("authority_id", AuthorityId).Error
	if err != nil {
		return err
	}
	for _, v := range pIds {
		PermissionService.CacheUpdateNodeUserLevel(orgId, v, AuthorityId)
	}

	return
}

// 节点管理员获取可支配角色列表
func (s *org) GetNodeAdminAuthorityList(userID uint, nodeID uint) (list []*model.OrgAuthorityPermission, err error) {
	var Isadmin bool
	global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("org_id = ? and user_id = ?", nodeID, userID).Pluck("is_admin", &Isadmin)

	if Isadmin {
		list, err = PermissionService.GetNodeAdminAuthorityList(userID, nodeID)
		return list, err
	}
	//即使不是管理员,也不返回错误
	return list, nil
}

// ChangeLoginOrg 切换当前登录组织节点
func (s *org) ChangeLoginOrg(userId uint, orgId uint) (token string, err error) {
	var OrgUser model.OrgOrganizationalUser
	err = global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("user_id = ? AND org_id =?", userId, orgId).
		First(&OrgUser).Error
	if err != nil {
		return "", fmt.Errorf("切换组织失败")
	}

	if OrgUser.AuthorityId == nil {
		return "", fmt.Errorf("当前组织未设置角色")
	}

	result := global.GVA_DB.Model(&system.SysUser{}).Where("id = ?", userId).
		Updates(map[string]interface{}{"org_id": orgId, "authority_id": OrgUser.AuthorityId})
	if result.RowsAffected == 0 {
		return "", fmt.Errorf("切换组织失败")
	}

	// 获取用户信息
	var sysUser system.SysUser
	err = global.GVA_DB.Model(&system.SysUser{}).Where("id = ?", userId).First(&sysUser).Error
	if err != nil {
		return "", fmt.Errorf("切换组织失败")
	}

	// 重新签发token
	j := sys_utils.NewJWT()
	claims := j.CreateClaims(systemReq.BaseClaims{
		UUID:        sysUser.UUID,
		ID:          sysUser.ID,
		Username:    sysUser.Username,
		AuthorityId: *OrgUser.AuthorityId,
		OrgId:       orgId,
	})
	token, err = j.CreateToken(claims)
	if err != nil {
		return "", fmt.Errorf("token签发失败")
	}

	return

}

// 获取用户可登录节点列表
func (s *org) GetUserLoginList(userID uint) (list []*model.OrgOrganizationalUser, err error) {
	// 首先获取用户的直接组织关联
	var userOrgs []*model.OrgOrganizationalUser
	err = global.GVA_DB.Where("user_id = ?", userID).Preload("Authority").Preload("Organizational").
		Find(&userOrgs).Error
	if err != nil {
		return nil, err
	}

	// 用于存储所有有权限的组织ID
	accessibleOrgIDs := make(map[uint]bool)

	// 遍历用户的每个组织关联，根据权限级别计算可访问的组织
	for _, userOrg := range userOrgs {
		if userOrg.AuthorityId == nil || userOrg.OrgID == nil {
			continue
		}

		orgID := *userOrg.OrgID

		// 获取权限级别
		level, ok := PermissionService.GetAuthorityLevel(*userOrg.AuthorityId)
		if !ok {
			// 如果没有找到权限级别，只添加当前组织
			accessibleOrgIDs[orgID] = true
			continue
		}

		// 根据权限级别计算可访问的组织
		switch level {
		case permission.ALL:
			// 所有权限：获取所有组织
			var allOrgIDs []uint
			global.GVA_DB.Model(&model.Organizational{}).Pluck("id", &allOrgIDs)
			for _, id := range allOrgIDs {
				accessibleOrgIDs[id] = true
			}
		case permission.COMPANY:
			// 公司及子公司：获取公司及其所有子组织
			companyID, ok := PermissionService.GetNodeCompanyID(orgID)
			if ok {
				orgIDs, ok := PermissionService.GetOrgTerrIDs(companyID, true)
				if ok {
					for _, id := range orgIDs {
						accessibleOrgIDs[id] = true
					}
				}
			}
		case permission.COMPANY_ONLY:
			// 仅公司：只获取公司本身
			companyID, ok := PermissionService.GetNodeCompanyID(orgID)
			if ok {
				accessibleOrgIDs[companyID] = true
			}
		case permission.DEPARTMENT:
			// 部门及子部门：获取部门及其子部门
			orgIDs, ok := PermissionService.GetOrgTerrIDs(orgID, true)
			if ok {
				for _, id := range orgIDs {
					accessibleOrgIDs[id] = true
				}
			}
		case permission.DEPARTMENT_ONLY:
			// 仅部门：只获取当前部门
			accessibleOrgIDs[orgID] = true
		case permission.SELF:
			// 仅自己：只获取当前组织
			accessibleOrgIDs[orgID] = true
		default:
			// 默认只添加当前组织
			accessibleOrgIDs[orgID] = true
		}
	}

	// 根据可访问的组织ID构建结果列表
	if len(accessibleOrgIDs) == 0 {
		return list, nil
	}

	var orgIDs []uint
	for orgID := range accessibleOrgIDs {
		orgIDs = append(orgIDs, orgID)
	}

	// 查询所有可访问的组织，并为每个组织创建用户关联记录
	var organizations []model.Organizational
	err = global.GVA_DB.Where("id IN ?", orgIDs).Find(&organizations).Error
	if err != nil {
		return nil, err
	}

	// 为每个可访问的组织创建用户关联记录
	for _, org := range organizations {
		// 查找用户在该组织中的权限（如果存在）
		var existingUserOrg *model.OrgOrganizationalUser
		for _, userOrg := range userOrgs {
			if userOrg.OrgID != nil && *userOrg.OrgID == org.ID {
				existingUserOrg = userOrg
				break
			}
		}

		if existingUserOrg != nil {
			// 如果用户在该组织中有直接关联，使用现有记录
			list = append(list, existingUserOrg)
		} else {
			// 如果用户在该组织中没有直接关联，创建一个虚拟记录（用于显示）
			orgIDPtr := org.ID
			virtualUserOrg := &model.OrgOrganizationalUser{
				UserID:         &userID,
				OrgID:          &orgIDPtr,
				Organizational: &org,
				// 不设置 AuthorityId，表示这是通过权限继承获得的访问权限
			}
			list = append(list, virtualUserOrg)
		}
	}

	return list, nil
}

// 设置节点管理员
func (s *org) SetNodeAdmin(userID uint, nodeID uint, isAdmin bool) (err error) {
	return global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("user_id = ? AND org_id =?", userID, nodeID).
		Update("is_admin", isAdmin).Error
}

// 设置代理负责人
func (s *org) SetAgentManager(userID uint, nodeID uint, isAgentManager bool) (err error) {
	return global.GVA_DB.Model(&model.OrgOrganizationalUser{}).Where("user_id = ? AND org_id =?", userID, nodeID).
		Update("is_agent_manager", isAgentManager).Error
}
