package utils

import (
	"sort"

	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organizational/model"
)

// DiffSets 比较两个集合的差异，返回仅在对比集合存在的元素、仅在基础集合存在的元素以及两个集合共有的元素
// 参数：
//   - baseSet: 基础集合（通常表示原始集合或旧集合）
//   - compareSet: 对比集合（通常表示新集合或要对比的集合）
//
// 返回值：
//   - onlyInBase: 仅在基础集合中存在的元素（即基础集合需要删除的元素）
//   - onlyInCompare: 仅在对比集合中存在的元素（即对比集合新增的元素）
//   - inBoth: 两个集合中都存在的元素（重叠元素）
func DiffSets[T comparable](baseSet, compareSet []T) (onlyInBase, onlyInCompare, inBoth []T) {
	// 如果基础集合为空，则所有对比集合元素都是新增的
	if len(baseSet) == 0 {
		return nil, compareSet, nil
	}
	// 如果对比集合为空，则所有基础集合元素都是需要删除的
	if len(compareSet) == 0 {
		return baseSet, nil, nil
	}

	baseMap := make(map[T]struct{}, len(baseSet))
	compareMap := make(map[T]struct{}, len(compareSet))

	// 预分配切片容量（优化内存分配）
	onlyInCompare = make([]T, 0, len(compareSet))
	onlyInBase = make([]T, 0, len(baseSet))
	inBoth = make([]T, 0, min(len(baseSet), len(compareSet)))

	// 构建对比集合的快速查找map
	for _, v := range compareSet {
		compareMap[v] = struct{}{}
	}

	// 检查基础集合中的元素
	for _, v := range baseSet {
		baseMap[v] = struct{}{} // 记录基础集合元素
		if _, ok := compareMap[v]; ok {
			inBoth = append(inBoth, v) // 记录两个集合共有的元素
		} else {
			onlyInBase = append(onlyInBase, v) // 记录仅在基础集合存在的元素
		}
	}

	// 检查对比集合中的元素
	for _, v := range compareSet {
		if _, ok := baseMap[v]; !ok {
			onlyInCompare = append(onlyInCompare, v) // 记录仅在对比集合存在的元素
		}
	}

	return
}

// BuildTreeById  根据id构建树形结构
func BuildTreeById(nodes []*model.Organizational, orgId uint) []*model.Organizational {
	nodeMap := make(map[uint]*model.Organizational)
	// 创建一个基本map
	for i, v := range nodes {
		nodeMap[v.ID] = nodes[i]
	}

	for i, v := range nodes {
		if v.ParentID != 0 {
			nodeMap[nodes[i].ParentID].Children = append(nodeMap[nodes[i].ParentID].Children, v)
		}
	}

	var rootNodes []*model.Organizational

	if node, exists := nodeMap[orgId]; exists {
		rootNodes = append(rootNodes, node)
	}
	return rootNodes
}

// BuildTreeByPid 根据父节点id构建树形结构
func BuildTreeByPid(nodes []*model.Organizational, pid uint) []*model.Organizational {
	nodeMap := make(map[uint]*model.Organizational)
	// 创建一个基本map
	for i, v := range nodes {
		nodeMap[v.ID] = nodes[i]
	}

	for i, v := range nodes {
		if v.ParentID != 0 {
			nodeMap[nodes[i].ParentID].Children = append(nodeMap[nodes[i].ParentID].Children, v)
		}
	}

	var rootNodes []*model.Organizational

	for _, v := range nodeMap {
		if v.ParentID == pid {
			rootNodes = append(rootNodes, v)
		}
	}
	SortNodes(rootNodes)

	return rootNodes
}

// OrgCopy拷贝
func OrgCopy(src []*model.Organizational) []*model.Organizational {
	var nodes []*model.Organizational
	for _, v := range src {
		new := *v
		nodes = append(nodes, &new)
	}
	return nodes
}

// BuildTreeMap 构建树地图 含子公司
func BuildTreeMap(nodes []*model.Organizational) map[uint]*model.Organizational {
	nodeMap := make(map[uint]*model.Organizational)
	// 创建一个基本map
	for i, v := range nodes {
		nodeMap[v.ID] = nodes[i]
	}

	for i, v := range nodes {
		if v.ParentID != 0 {
			nodeMap[nodes[i].ParentID].Children = append(nodeMap[nodes[i].ParentID].Children, v)
		}
	}
	return nodeMap
}

// 构建树地图 不含子公司
func BuildTreeMapNoSubs(nodes []*model.Organizational) map[uint]*model.Organizational {
	nodeMap := make(map[uint]*model.Organizational)
	// 创建一个基本map
	for i, v := range nodes {
		nodeMap[v.ID] = nodes[i]
	}

	for i, v := range nodes {
		if v.ParentID != 0 && *v.Type != 1 {
			nodeMap[nodes[i].ParentID].Children = append(nodeMap[nodes[i].ParentID].Children, v)
		}
	}
	return nodeMap
}

// 深度优先搜索 返回ids
func DFS(nodeMap map[uint]*model.Organizational) map[uint][]uint {
	idTreeMap := make(map[uint][]uint)
	var dfs func(nodeID uint, result *[]uint)
	dfs = func(nodeID uint, result *[]uint) {
		if node, ok := nodeMap[nodeID]; ok {
			for _, child := range node.Children {
				*result = append(*result, child.ID)
				dfs(child.ID, result)
			}
		}
	}
	for _, rootID := range nodeMap {
		var ids []uint
		dfs(rootID.ID, &ids)
		idTreeMap[rootID.ID] = ids
	}
	return idTreeMap
}

// 所有节点归属公司ID
func BuildNodeCompanyIDsMap(orgTerrCache map[uint]*model.Organizational) map[uint]uint {
	nodeCompanyIDCache := make(map[uint]uint)

	// 第一遍遍历，找出所有公司节点并建立初步映射
	for id, org := range orgTerrCache {
		if *org.Type == 1 { // 公司节点
			nodeCompanyIDCache[id] = id // 公司节点映射到自身
		}
	}

	// 第二遍遍历，处理部门节点
	for id, org := range orgTerrCache {
		if *org.Type == 2 { // 部门节点
			// 向上查找直到找到公司节点
			current := org
			for current != nil && *current.Type != 1 {
				parentID := current.ParentID
				parent, exists := orgTerrCache[parentID]
				if !exists {
					break
				}
				current = parent
			}

			if current != nil && *current.Type == 1 {
				nodeCompanyIDCache[id] = current.ID
			}
		}
	}

	return nodeCompanyIDCache
}

// 组织节点排序排序
func SortNodes(nodes []*model.Organizational) {
	if len(nodes) == 0 {
		return
	}

	// 对当前层级节点按ID排序
	sort.Slice(nodes, func(i, j int) bool {
		return *nodes[i].Sort < *nodes[j].Sort
	})

	// 递归对每个节点的子节点排序
	for _, node := range nodes {
		if len(node.Children) > 0 {
			SortNodes(node.Children)
		}
	}
}
