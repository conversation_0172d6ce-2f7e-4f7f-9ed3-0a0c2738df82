package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AssessmentCategoriesRouter struct {}

// InitAssessmentCategoriesRouter 初始化 考核类型 路由信息
func (s *AssessmentCategoriesRouter) InitAssessmentCategoriesRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	assessmentCategoriesRouter := Router.Group("assessmentCategories").Use(middleware.OperationRecord())
	assessmentCategoriesRouterWithoutRecord := Router.Group("assessmentCategories")
	assessmentCategoriesRouterWithoutAuth := PublicRouter.Group("assessmentCategories")
	{
		assessmentCategoriesRouter.POST("createAssessmentCategories", assessmentCategoriesApi.CreateAssessmentCategories)   // 新建考核类型
		assessmentCategoriesRouter.DELETE("deleteAssessmentCategories", assessmentCategoriesApi.DeleteAssessmentCategories) // 删除考核类型
		assessmentCategoriesRouter.DELETE("deleteAssessmentCategoriesByIds", assessmentCategoriesApi.DeleteAssessmentCategoriesByIds) // 批量删除考核类型
		assessmentCategoriesRouter.PUT("updateAssessmentCategories", assessmentCategoriesApi.UpdateAssessmentCategories)    // 更新考核类型
	}
	{
		assessmentCategoriesRouterWithoutRecord.GET("findAssessmentCategories", assessmentCategoriesApi.FindAssessmentCategories)        // 根据ID获取考核类型
		assessmentCategoriesRouterWithoutRecord.GET("getAssessmentCategoriesList", assessmentCategoriesApi.GetAssessmentCategoriesList)  // 获取考核类型列表
	}
	{
	    assessmentCategoriesRouterWithoutAuth.GET("getAssessmentCategoriesPublic", assessmentCategoriesApi.GetAssessmentCategoriesPublic)  // 考核类型开放接口
	}
}
