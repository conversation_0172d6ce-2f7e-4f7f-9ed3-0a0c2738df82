package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AssessmentCoefficientRouter struct{}

// InitAssessmentCoefficientRouter 初始化考核系数路由信息
func (s *AssessmentCoefficientRouter) InitAssessmentCoefficientRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	assessmentCoefficientRouter := Router.Group("assessment").Use(middleware.OperationRecord())
	assessmentCoefficientRouterWithoutRecord := Router.Group("assessment")

	// 使用全局定义的API变量
	{
		// 需要记录操作日志的接口
		assessmentCoefficientRouter.POST("coefficients/replace", assessmentCoefficientApi.ReplaceAssessmentCoefficients) // 替换考核系数
	}
	{
		// 不需要记录操作日志的接口
		assessmentCoefficientRouterWithoutRecord.GET("coefficients/:configId", assessmentCoefficientApi.GetAssessmentCoefficients)               // 获取考核系数
		assessmentCoefficientRouterWithoutRecord.GET("coefficients/:configId/validate", assessmentCoefficientApi.ValidateAssessmentCoefficients) // 校验考核系数
		assessmentCoefficientRouterWithoutRecord.GET("coefficients/:configId/export", assessmentCoefficientApi.ExportAssessmentCoefficients)     // 导出考核系数
	}
}
