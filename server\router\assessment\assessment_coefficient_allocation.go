package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AssessmentCoefficientAllocationRouter struct {}

// InitAssessmentCoefficientAllocationRouter 初始化 考核系数分配 路由信息
func (s *AssessmentCoefficientAllocationRouter) InitAssessmentCoefficientAllocationRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	assessmentCoefficientAllocationRouter := Router.Group("assessmentCoefficientAllocation").Use(middleware.OperationRecord())
	assessmentCoefficientAllocationRouterWithoutRecord := Router.Group("assessmentCoefficientAllocation")
	assessmentCoefficientAllocationRouterWithoutAuth := PublicRouter.Group("assessmentCoefficientAllocation")
	{
		assessmentCoefficientAllocationRouter.POST("createAssessmentCoefficientAllocation", assessmentCoefficientAllocationApi.CreateAssessmentCoefficientAllocation)   // 新建考核系数分配
		assessmentCoefficientAllocationRouter.DELETE("deleteAssessmentCoefficientAllocation", assessmentCoefficientAllocationApi.DeleteAssessmentCoefficientAllocation) // 删除考核系数分配
		assessmentCoefficientAllocationRouter.DELETE("deleteAssessmentCoefficientAllocationByIds", assessmentCoefficientAllocationApi.DeleteAssessmentCoefficientAllocationByIds) // 批量删除考核系数分配
		assessmentCoefficientAllocationRouter.PUT("updateAssessmentCoefficientAllocation", assessmentCoefficientAllocationApi.UpdateAssessmentCoefficientAllocation)    // 更新考核系数分配
	}
	{
		assessmentCoefficientAllocationRouterWithoutRecord.GET("findAssessmentCoefficientAllocation", assessmentCoefficientAllocationApi.FindAssessmentCoefficientAllocation)        // 根据ID获取考核系数分配
		assessmentCoefficientAllocationRouterWithoutRecord.GET("getAssessmentCoefficientAllocationList", assessmentCoefficientAllocationApi.GetAssessmentCoefficientAllocationList)  // 获取考核系数分配列表
	}
	{
	    assessmentCoefficientAllocationRouterWithoutAuth.GET("getAssessmentCoefficientAllocationPublic", assessmentCoefficientAllocationApi.GetAssessmentCoefficientAllocationPublic)  // 考核系数分配开放接口
	}
}
