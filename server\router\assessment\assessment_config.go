package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AssessmentConfigRouter struct{}

// InitAssessmentConfigRouter 初始化 assessmentConfig表 路由信息
func (s *AssessmentConfigRouter) InitAssessmentConfigRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	assessmentConfigRouter := Router.Group("assessmentConfig").Use(middleware.OperationRecord())
	assessmentConfigRouterWithoutRecord := Router.Group("assessmentConfig")
	assessmentConfigRouterWithoutAuth := PublicRouter.Group("assessmentConfig")
	{
		assessmentConfigRouter.POST("createAssessmentConfig", assessmentConfigApi.CreateAssessmentConfig)             // 新建assessmentConfig表
		assessmentConfigRouter.DELETE("deleteAssessmentConfig", assessmentConfigApi.DeleteAssessmentConfig)           // 删除assessmentConfig表
		assessmentConfigRouter.DELETE("deleteAssessmentConfigByIds", assessmentConfigApi.DeleteAssessmentConfigByIds) // 批量删除assessmentConfig表
		assessmentConfigRouter.PUT("updateAssessmentConfig", assessmentConfigApi.UpdateAssessmentConfig)              // 更新assessmentConfig表
	}
	{
		assessmentConfigRouterWithoutRecord.GET("findAssessmentConfig", assessmentConfigApi.FindAssessmentConfig)                         // 根据ID获取assessmentConfig表
		assessmentConfigRouterWithoutRecord.GET("findAssessmentConfigWithNames", assessmentConfigApi.FindAssessmentConfigWithNames)       // 根据ID获取包含关联名称的考核配置
		assessmentConfigRouterWithoutRecord.GET("getAssessmentConfigList", assessmentConfigApi.GetAssessmentConfigList)                   // 获取assessmentConfig表列表
		assessmentConfigRouterWithoutRecord.GET("getAssessmentConfigWithNamesList", assessmentConfigApi.GetAssessmentConfigWithNamesList) // 获取包含关联名称的考核配置列表
	}
	{
		assessmentConfigRouterWithoutAuth.GET("getAssessmentConfigPublic", assessmentConfigApi.GetAssessmentConfigPublic) // assessmentConfig表开放接口
	}
}
