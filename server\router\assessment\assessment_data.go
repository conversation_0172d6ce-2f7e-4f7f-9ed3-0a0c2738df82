package assessment

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AssessmentDataRouter struct{}

// InitAssessmentDataRouter 初始化考核数据路由信息
func (s *AssessmentDataRouter) InitAssessmentDataRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	assessmentDataRouter := Router.Group("assessmentData").Use(middleware.OperationRecord())

	var assessmentDataApi = v1.ApiGroupApp.AssessmentApiGroup.AssessmentDataApi
	{
		assessmentDataRouter.POST("getAssessmentData", assessmentDataApi.GetAssessmentData)                           // 获取考核数据
		assessmentDataRouter.POST("getUserParameterScores", assessmentDataApi.GetUserParameterScores)                 // 获取用户参数评分数据
		assessmentDataRouter.POST("getEvaluationDetailsByConfig", assessmentDataApi.GetEvaluationDetailsByConfig)     // 根据考核配置获取评价详情
		assessmentDataRouter.POST("getEvaluationDetailsByConfigId", assessmentDataApi.GetEvaluationDetailsByConfigId) // 根据考核配置ID获取评估详情
	}
}
