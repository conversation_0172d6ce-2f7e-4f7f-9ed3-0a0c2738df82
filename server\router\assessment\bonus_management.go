package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type BonusManagementRouter struct {}

// InitBonusManagementRouter 初始化 奖金管理 路由信息
func (s *BonusManagementRouter) InitBonusManagementRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	bonusManagementRouter := Router.Group("bonusManagement").Use(middleware.OperationRecord())
	bonusManagementRouterWithoutRecord := Router.Group("bonusManagement")
	bonusManagementRouterWithoutAuth := PublicRouter.Group("bonusManagement")
	{
		bonusManagementRouter.POST("createBonusManagement", bonusManagementApi.CreateBonusManagement)   // 新建奖金管理
		bonusManagementRouter.DELETE("deleteBonusManagement", bonusManagementApi.DeleteBonusManagement) // 删除奖金管理
		bonusManagementRouter.DELETE("deleteBonusManagementByIds", bonusManagementApi.DeleteBonusManagementByIds) // 批量删除奖金管理
		bonusManagementRouter.PUT("updateBonusManagement", bonusManagementApi.UpdateBonusManagement)    // 更新奖金管理
	}
	{
		bonusManagementRouterWithoutRecord.GET("findBonusManagement", bonusManagementApi.FindBonusManagement)        // 根据ID获取奖金管理
		bonusManagementRouterWithoutRecord.GET("getBonusManagementList", bonusManagementApi.GetBonusManagementList)  // 获取奖金管理列表
	}
	{
	    bonusManagementRouterWithoutAuth.GET("getBonusManagementPublic", bonusManagementApi.GetBonusManagementPublic)  // 奖金管理开放接口
	}
}
