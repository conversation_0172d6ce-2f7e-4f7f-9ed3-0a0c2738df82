package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CalculationMethodsRouter struct {}

// InitCalculationMethodsRouter 初始化 calculationMethods表 路由信息
func (s *CalculationMethodsRouter) InitCalculationMethodsRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	calculationMethodsRouter := Router.Group("calculationMethods").Use(middleware.OperationRecord())
	calculationMethodsRouterWithoutRecord := Router.Group("calculationMethods")
	calculationMethodsRouterWithoutAuth := PublicRouter.Group("calculationMethods")
	{
		calculationMethodsRouter.POST("createCalculationMethods", calculationMethodsApi.CreateCalculationMethods)   // 新建calculationMethods表
		calculationMethodsRouter.DELETE("deleteCalculationMethods", calculationMethodsApi.DeleteCalculationMethods) // 删除calculationMethods表
		calculationMethodsRouter.DELETE("deleteCalculationMethodsByIds", calculationMethodsApi.DeleteCalculationMethodsByIds) // 批量删除calculationMethods表
		calculationMethodsRouter.PUT("updateCalculationMethods", calculationMethodsApi.UpdateCalculationMethods)    // 更新calculationMethods表
	}
	{
		calculationMethodsRouterWithoutRecord.GET("findCalculationMethods", calculationMethodsApi.FindCalculationMethods)        // 根据ID获取calculationMethods表
		calculationMethodsRouterWithoutRecord.GET("getCalculationMethodsList", calculationMethodsApi.GetCalculationMethodsList)  // 获取calculationMethods表列表
		calculationMethodsRouterWithoutRecord.POST("calculateByRule", calculationMethodsApi.CalculateByRule)                     // 根据规则计算结果
		calculationMethodsRouterWithoutRecord.POST("testRule", calculationMethodsApi.TestRule)                                   // 测试规则配置
	}
	{
	    calculationMethodsRouterWithoutAuth.GET("getCalculationMethodsPublic", calculationMethodsApi.GetCalculationMethodsPublic)  // calculationMethods表开放接口
	}
}
