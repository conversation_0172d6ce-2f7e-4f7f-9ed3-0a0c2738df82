package assessment

import api "github.com/flipped-aurora/gin-vue-admin/server/api/v1"

type RouterGroup struct {
	AssessmentConfigRouter
	BonusManagementRouter
	ScoreQuotaManagementRouter
	AssessmentCategoriesRouter
	CalculationParametersRouter
	CalculationMethodsRouter
	MethodParameterRelationsRouter
	MethodUserAssignmentsRouter
	AssessmentDataRouter
	AssessmentCoefficientRouter
	ProjectManagerScoreRouter
	AssessmentCoefficientAllocationRouter
}

var (
	assessmentConfigApi                = api.ApiGroupApp.AssessmentApiGroup.AssessmentConfigApi
	bonusManagementApi                 = api.ApiGroupApp.AssessmentApiGroup.BonusManagementApi
	scoreQuotaManagementApi            = api.ApiGroupApp.AssessmentApiGroup.ScoreQuotaManagementApi
	assessmentCategoriesApi            = api.ApiGroupApp.AssessmentApiGroup.AssessmentCategoriesApi
	calculationParametersApi           = api.ApiGroupApp.AssessmentApiGroup.CalculationParametersApi
	calculationMethodsApi              = api.ApiGroupApp.AssessmentApiGroup.CalculationMethodsApi
	methodParameterRelationsApi        = api.ApiGroupApp.AssessmentApiGroup.MethodParameterRelationsApi
	methodUserAssignmentsApi           = api.ApiGroupApp.AssessmentApiGroup.MethodUserAssignmentsApi
	assessmentDataApi                  = api.ApiGroupApp.AssessmentApiGroup.AssessmentDataApi
	assessmentCoefficientApi           = api.ApiGroupApp.AssessmentApiGroup.AssessmentCoefficientApi
	projectManagerScoreApi             = api.ApiGroupApp.AssessmentApiGroup.ProjectManagerScoreApi
	assessmentCoefficientAllocationApi = api.ApiGroupApp.AssessmentApiGroup.AssessmentCoefficientAllocationApi
)
