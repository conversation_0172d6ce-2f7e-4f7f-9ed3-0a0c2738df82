package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MethodParameterRelationsRouter struct {}

// InitMethodParameterRelationsRouter 初始化 关联关系 路由信息
func (s *MethodParameterRelationsRouter) InitMethodParameterRelationsRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	methodParameterRelationsRouter := Router.Group("methodParameterRelations").Use(middleware.OperationRecord())
	methodParameterRelationsRouterWithoutRecord := Router.Group("methodParameterRelations")
	methodParameterRelationsRouterWithoutAuth := PublicRouter.Group("methodParameterRelations")
	{
		methodParameterRelationsRouter.POST("createMethodParameterRelations", methodParameterRelationsApi.CreateMethodParameterRelations)   // 新建关联关系
		methodParameterRelationsRouter.DELETE("deleteMethodParameterRelations", methodParameterRelationsApi.DeleteMethodParameterRelations) // 删除关联关系
		methodParameterRelationsRouter.DELETE("deleteMethodParameterRelationsByIds", methodParameterRelationsApi.DeleteMethodParameterRelationsByIds) // 批量删除关联关系
		methodParameterRelationsRouter.PUT("updateMethodParameterRelations", methodParameterRelationsApi.UpdateMethodParameterRelations)    // 更新关联关系
	}
	{
		methodParameterRelationsRouterWithoutRecord.GET("findMethodParameterRelations", methodParameterRelationsApi.FindMethodParameterRelations)        // 根据ID获取关联关系
		methodParameterRelationsRouterWithoutRecord.GET("getMethodParameterRelationsList", methodParameterRelationsApi.GetMethodParameterRelationsList)  // 获取关联关系列表
	}
	{
	    methodParameterRelationsRouterWithoutAuth.GET("getMethodParameterRelationsPublic", methodParameterRelationsApi.GetMethodParameterRelationsPublic)  // 关联关系开放接口
	}
}
