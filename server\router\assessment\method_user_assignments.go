package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MethodUserAssignmentsRouter struct {}

// InitMethodUserAssignmentsRouter 初始化 方法用户关联 路由信息
func (s *MethodUserAssignmentsRouter) InitMethodUserAssignmentsRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	methodUserAssignmentsRouter := Router.Group("methodUserAssignments").Use(middleware.OperationRecord())
	methodUserAssignmentsRouterWithoutRecord := Router.Group("methodUserAssignments")
	methodUserAssignmentsRouterWithoutAuth := PublicRouter.Group("methodUserAssignments")
	{
		methodUserAssignmentsRouter.POST("createMethodUserAssignments", methodUserAssignmentsApi.CreateMethodUserAssignments)   // 新建方法用户关联
		methodUserAssignmentsRouter.DELETE("deleteMethodUserAssignments", methodUserAssignmentsApi.DeleteMethodUserAssignments) // 删除方法用户关联
		methodUserAssignmentsRouter.DELETE("deleteMethodUserAssignmentsByIds", methodUserAssignmentsApi.DeleteMethodUserAssignmentsByIds) // 批量删除方法用户关联
		methodUserAssignmentsRouter.PUT("updateMethodUserAssignments", methodUserAssignmentsApi.UpdateMethodUserAssignments)    // 更新方法用户关联
	}
	{
		methodUserAssignmentsRouterWithoutRecord.GET("findMethodUserAssignments", methodUserAssignmentsApi.FindMethodUserAssignments)        // 根据ID获取方法用户关联
		methodUserAssignmentsRouterWithoutRecord.GET("getMethodUserAssignmentsList", methodUserAssignmentsApi.GetMethodUserAssignmentsList)  // 获取方法用户关联列表
	}
	{
	    methodUserAssignmentsRouterWithoutAuth.GET("getMethodUserAssignmentsPublic", methodUserAssignmentsApi.GetMethodUserAssignmentsPublic)  // 方法用户关联开放接口
	}
}
