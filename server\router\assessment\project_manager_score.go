package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProjectManagerScoreRouter struct{}

// InitProjectManagerScoreRouter 初始化 项目负责人评分 路由信息
func (s *ProjectManagerScoreRouter) InitProjectManagerScoreRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	projectManagerScoreRouter := Router.Group("projectManagerScore").Use(middleware.OperationRecord())
	projectManagerScoreRouterWithoutRecord := Router.Group("projectManagerScore")
	projectManagerScoreRouterWithoutAuth := PublicRouter.Group("projectManagerScore")
	{
		projectManagerScoreRouter.POST("createProjectManagerScore", projectManagerScoreApi.CreateProjectManagerScore)             // 新建项目负责人评分
		projectManagerScoreRouter.DELETE("deleteProjectManagerScore", projectManagerScoreApi.DeleteProjectManagerScore)           // 删除项目负责人评分
		projectManagerScoreRouter.DELETE("deleteProjectManagerScoreByIds", projectManagerScoreApi.DeleteProjectManagerScoreByIds) // 批量删除项目负责人评分
		projectManagerScoreRouter.PUT("updateProjectManagerScore", projectManagerScoreApi.UpdateProjectManagerScore)              // 更新项目负责人评分
		projectManagerScoreRouter.POST("batchSubmitProjectMemberScores", projectManagerScoreApi.BatchSubmitProjectMemberScores)   // 批量提交项目成员评分
	}
	{
		projectManagerScoreRouterWithoutRecord.GET("findProjectManagerScore", projectManagerScoreApi.FindProjectManagerScore)       // 根据ID获取项目负责人评分
		projectManagerScoreRouterWithoutRecord.GET("getProjectManagerScoreList", projectManagerScoreApi.GetProjectManagerScoreList) // 获取项目负责人评分列表
	}
	{
		projectManagerScoreRouterWithoutAuth.GET("getProjectManagerScorePublic", projectManagerScoreApi.GetProjectManagerScorePublic) // 项目负责人评分开放接口
	}
}
