package assessment

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ScoreQuotaManagementRouter struct {}

// InitScoreQuotaManagementRouter 初始化 高分配额管理 路由信息
func (s *ScoreQuotaManagementRouter) InitScoreQuotaManagementRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	scoreQuotaManagementRouter := Router.Group("scoreQuotaManagement").Use(middleware.OperationRecord())
	scoreQuotaManagementRouterWithoutRecord := Router.Group("scoreQuotaManagement")
	scoreQuotaManagementRouterWithoutAuth := PublicRouter.Group("scoreQuotaManagement")
	{
		scoreQuotaManagementRouter.POST("createScoreQuotaManagement", scoreQuotaManagementApi.CreateScoreQuotaManagement)   // 新建高分配额管理
		scoreQuotaManagementRouter.DELETE("deleteScoreQuotaManagement", scoreQuotaManagementApi.DeleteScoreQuotaManagement) // 删除高分配额管理
		scoreQuotaManagementRouter.DELETE("deleteScoreQuotaManagementByIds", scoreQuotaManagementApi.DeleteScoreQuotaManagementByIds) // 批量删除高分配额管理
		scoreQuotaManagementRouter.PUT("updateScoreQuotaManagement", scoreQuotaManagementApi.UpdateScoreQuotaManagement)    // 更新高分配额管理
	}
	{
		scoreQuotaManagementRouterWithoutRecord.GET("findScoreQuotaManagement", scoreQuotaManagementApi.FindScoreQuotaManagement)        // 根据ID获取高分配额管理
		scoreQuotaManagementRouterWithoutRecord.GET("getScoreQuotaManagementList", scoreQuotaManagementApi.GetScoreQuotaManagementList)  // 获取高分配额管理列表
	}
	{
	    scoreQuotaManagementRouterWithoutAuth.GET("getScoreQuotaManagementPublic", scoreQuotaManagementApi.GetScoreQuotaManagementPublic)  // 高分配额管理开放接口
	}
}
