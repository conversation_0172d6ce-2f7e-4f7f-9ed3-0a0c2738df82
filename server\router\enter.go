package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router/assessment"
	"github.com/flipped-aurora/gin-vue-admin/server/router/example"
	"github.com/flipped-aurora/gin-vue-admin/server/router/project"
	"github.com/flipped-aurora/gin-vue-admin/server/router/score"
	"github.com/flipped-aurora/gin-vue-admin/server/router/system"
)

var RouterGroupApp = new(RouterGroup)

type RouterGroup struct {
	System     system.RouterGroup
	Example    example.RouterGroup
	Project    project.RouterGroup
	Assessment assessment.RouterGroup
	Score      score.RouterGroup
}
