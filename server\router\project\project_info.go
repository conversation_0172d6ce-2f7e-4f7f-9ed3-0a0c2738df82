package project

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProjectInfoRouter struct {}

// InitProjectInfoRouter 初始化 项目信息表 路由信息
func (s *ProjectInfoRouter) InitProjectInfoRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	projectInfoRouter := Router.Group("projectInfo").Use(middleware.OperationRecord())
	projectInfoRouterWithoutRecord := Router.Group("projectInfo")
	projectInfoRouterWithoutAuth := PublicRouter.Group("projectInfo")
	{
		projectInfoRouter.POST("createProjectInfo", projectInfoApi.CreateProjectInfo)   // 新建项目信息表
		projectInfoRouter.DELETE("deleteProjectInfo", projectInfoApi.DeleteProjectInfo) // 删除项目信息表
		projectInfoRouter.DELETE("deleteProjectInfoByIds", projectInfoApi.DeleteProjectInfoByIds) // 批量删除项目信息表
		projectInfoRouter.PUT("updateProjectInfo", projectInfoApi.UpdateProjectInfo)    // 更新项目信息表
	}
	{
		projectInfoRouterWithoutRecord.GET("findProjectInfo", projectInfoApi.FindProjectInfo)        // 根据ID获取项目信息表
		projectInfoRouterWithoutRecord.GET("getProjectInfoList", projectInfoApi.GetProjectInfoList)  // 获取项目信息表列表
	}
	{
	    projectInfoRouterWithoutAuth.GET("getProjectInfoPublic", projectInfoApi.GetProjectInfoPublic)  // 项目信息表开放接口
	}
}
