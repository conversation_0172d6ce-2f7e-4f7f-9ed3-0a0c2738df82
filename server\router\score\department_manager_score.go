package score

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DepartmentManagerScoreRouter struct{}

// InitDepartmentManagerScoreRouter 初始化 部门/机构负责人评分 路由信息
func (s *DepartmentManagerScoreRouter) InitDepartmentManagerScoreRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	departmentManagerScoreRouter := Router.Group("departmentManagerScore").Use(middleware.OperationRecord())
	departmentManagerScoreRouterWithoutRecord := Router.Group("departmentManagerScore")
	departmentManagerScoreRouterWithoutAuth := PublicRouter.Group("departmentManagerScore")
	{
		departmentManagerScoreRouter.POST("createDepartmentManagerScore", departmentManagerScoreApi.CreateDepartmentManagerScore)             // 新建部门/机构负责人评分
		departmentManagerScoreRouter.DELETE("deleteDepartmentManagerScore", departmentManagerScoreApi.DeleteDepartmentManagerScore)           // 删除部门/机构负责人评分
		departmentManagerScoreRouter.DELETE("deleteDepartmentManagerScoreByIds", departmentManagerScoreApi.DeleteDepartmentManagerScoreByIds) // 批量删除部门/机构负责人评分
		departmentManagerScoreRouter.PUT("updateDepartmentManagerScore", departmentManagerScoreApi.UpdateDepartmentManagerScore)              // 更新部门/机构负责人评分
		departmentManagerScoreRouter.POST("batchSubmitDepartmentManagerScores", departmentManagerScoreApi.BatchSubmitDepartmentManagerScores) // 批量提交部门负责人评分
	}
	{
		departmentManagerScoreRouterWithoutRecord.GET("findDepartmentManagerScore", departmentManagerScoreApi.FindDepartmentManagerScore)       // 根据ID获取部门/机构负责人评分
		departmentManagerScoreRouterWithoutRecord.GET("getDepartmentManagerScoreList", departmentManagerScoreApi.GetDepartmentManagerScoreList) // 获取部门/机构负责人评分列表
	}
	{
		departmentManagerScoreRouterWithoutAuth.GET("getDepartmentManagerScorePublic", departmentManagerScoreApi.GetDepartmentManagerScorePublic) // 部门/机构负责人评分开放接口
	}
}
