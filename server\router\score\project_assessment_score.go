package score

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProjectAssessmentScoreRouter struct {}

// InitProjectAssessmentScoreRouter 初始化 精力分配评分表 路由信息
func (s *ProjectAssessmentScoreRouter) InitProjectAssessmentScoreRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	projectAssessmentScoreRouter := Router.Group("projectAssessmentScore").Use(middleware.OperationRecord())
	projectAssessmentScoreRouterWithoutRecord := Router.Group("projectAssessmentScore")
	projectAssessmentScoreRouterWithoutAuth := PublicRouter.Group("projectAssessmentScore")
	{
		projectAssessmentScoreRouter.POST("createProjectAssessmentScore", projectAssessmentScoreApi.CreateProjectAssessmentScore)   // 新建精力分配评分表
		projectAssessmentScoreRouter.DELETE("deleteProjectAssessmentScore", projectAssessmentScoreApi.DeleteProjectAssessmentScore) // 删除精力分配评分表
		projectAssessmentScoreRouter.DELETE("deleteProjectAssessmentScoreByIds", projectAssessmentScoreApi.DeleteProjectAssessmentScoreByIds) // 批量删除精力分配评分表
		projectAssessmentScoreRouter.PUT("updateProjectAssessmentScore", projectAssessmentScoreApi.UpdateProjectAssessmentScore)    // 更新精力分配评分表
	}
	{
		projectAssessmentScoreRouterWithoutRecord.GET("findProjectAssessmentScore", projectAssessmentScoreApi.FindProjectAssessmentScore)        // 根据ID获取精力分配评分表
		projectAssessmentScoreRouterWithoutRecord.GET("getProjectAssessmentScoreList", projectAssessmentScoreApi.GetProjectAssessmentScoreList)  // 获取精力分配评分表列表
	}
	{
	    projectAssessmentScoreRouterWithoutAuth.GET("getProjectAssessmentScorePublic", projectAssessmentScoreApi.GetProjectAssessmentScorePublic)  // 精力分配评分表开放接口
	}
}
