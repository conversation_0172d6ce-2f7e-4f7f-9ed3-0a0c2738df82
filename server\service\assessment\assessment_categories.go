
package assessment

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type AssessmentCategoriesService struct {}
// CreateAssessmentCategories 创建考核类型记录
// Author [yourname](https://github.com/yourname)
func (assessmentCategoriesService *AssessmentCategoriesService) CreateAssessmentCategories(ctx context.Context, assessmentCategories *assessment.AssessmentCategories) (err error) {
	err = global.GVA_DB.Create(assessmentCategories).Error
	return err
}

// DeleteAssessmentCategories 删除考核类型记录
// Author [yourname](https://github.com/yourname)
func (assessmentCategoriesService *AssessmentCategoriesService)DeleteAssessmentCategories(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.AssessmentCategories{},"id = ?",id).Error
	return err
}

// DeleteAssessmentCategoriesByIds 批量删除考核类型记录
// Author [yourname](https://github.com/yourname)
func (assessmentCategoriesService *AssessmentCategoriesService)DeleteAssessmentCategoriesByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.AssessmentCategories{},"id in ?",ids).Error
	return err
}

// UpdateAssessmentCategories 更新考核类型记录
// Author [yourname](https://github.com/yourname)
func (assessmentCategoriesService *AssessmentCategoriesService)UpdateAssessmentCategories(ctx context.Context, assessmentCategories assessment.AssessmentCategories) (err error) {
	err = global.GVA_DB.Model(&assessment.AssessmentCategories{}).Where("id = ?",assessmentCategories.Id).Updates(&assessmentCategories).Error
	return err
}

// GetAssessmentCategories 根据id获取考核类型记录
// Author [yourname](https://github.com/yourname)
func (assessmentCategoriesService *AssessmentCategoriesService)GetAssessmentCategories(ctx context.Context, id string) (assessmentCategories assessment.AssessmentCategories, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&assessmentCategories).Error
	return
}
// GetAssessmentCategoriesInfoList 分页获取考核类型记录
// Author [yourname](https://github.com/yourname)
func (assessmentCategoriesService *AssessmentCategoriesService)GetAssessmentCategoriesInfoList(ctx context.Context, info assessmentReq.AssessmentCategoriesSearch) (list []assessment.AssessmentCategories, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&assessment.AssessmentCategories{})
    var assessmentCategoriess []assessment.AssessmentCategories
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&assessmentCategoriess).Error
	return  assessmentCategoriess, total, err
}
func (assessmentCategoriesService *AssessmentCategoriesService)GetAssessmentCategoriesPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
