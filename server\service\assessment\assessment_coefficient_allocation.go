
package assessment

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type AssessmentCoefficientAllocationService struct {}
// CreateAssessmentCoefficientAllocation 创建考核系数分配记录
// Author [yourname](https://github.com/yourname)
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService) CreateAssessmentCoefficientAllocation(ctx context.Context, assessmentCoefficientAllocation *assessment.AssessmentCoefficientAllocation) (err error) {
	err = global.GVA_DB.Create(assessmentCoefficientAllocation).Error
	return err
}

// DeleteAssessmentCoefficientAllocation 删除考核系数分配记录
// Author [yourname](https://github.com/yourname)
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService)DeleteAssessmentCoefficientAllocation(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.AssessmentCoefficientAllocation{},"id = ?",id).Error
	return err
}

// DeleteAssessmentCoefficientAllocationByIds 批量删除考核系数分配记录
// Author [yourname](https://github.com/yourname)
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService)DeleteAssessmentCoefficientAllocationByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.AssessmentCoefficientAllocation{},"id in ?",ids).Error
	return err
}

// UpdateAssessmentCoefficientAllocation 更新考核系数分配记录
// Author [yourname](https://github.com/yourname)
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService)UpdateAssessmentCoefficientAllocation(ctx context.Context, assessmentCoefficientAllocation assessment.AssessmentCoefficientAllocation) (err error) {
	err = global.GVA_DB.Model(&assessment.AssessmentCoefficientAllocation{}).Where("id = ?",assessmentCoefficientAllocation.Id).Updates(&assessmentCoefficientAllocation).Error
	return err
}

// GetAssessmentCoefficientAllocation 根据id获取考核系数分配记录
// Author [yourname](https://github.com/yourname)
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService)GetAssessmentCoefficientAllocation(ctx context.Context, id string) (assessmentCoefficientAllocation assessment.AssessmentCoefficientAllocation, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&assessmentCoefficientAllocation).Error
	return
}
// GetAssessmentCoefficientAllocationInfoList 分页获取考核系数分配记录
// Author [yourname](https://github.com/yourname)
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService)GetAssessmentCoefficientAllocationInfoList(ctx context.Context, info assessmentReq.AssessmentCoefficientAllocationSearch) (list []assessment.AssessmentCoefficientAllocation, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&assessment.AssessmentCoefficientAllocation{})
    var assessmentCoefficientAllocations []assessment.AssessmentCoefficientAllocation
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&assessmentCoefficientAllocations).Error
	return  assessmentCoefficientAllocations, total, err
}
func (assessmentCoefficientAllocationService *AssessmentCoefficientAllocationService)GetAssessmentCoefficientAllocationPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
