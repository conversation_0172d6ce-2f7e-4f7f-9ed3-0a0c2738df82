package assessment

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type AssessmentConfigService struct{}

// CreateAssessmentConfig 创建assessmentConfig表记录
// Author [yourname](https://github.com/yourname)
func (assessmentConfigService *AssessmentConfigService) CreateAssessmentConfig(ctx context.Context, assessmentConfig *assessment.AssessmentConfig) (err error) {
	// 如果 IsArchived 为 nil，设置默认值为 false（未归档）
	if assessmentConfig.IsArchived == nil {
		defaultValue := false
		assessmentConfig.IsArchived = &defaultValue
	}
	err = global.GVA_DB.Create(assessmentConfig).Error
	return err
}

// DeleteAssessmentConfig 删除assessmentConfig表记录
// Author [yourname](https://github.com/yourname)
func (assessmentConfigService *AssessmentConfigService) DeleteAssessmentConfig(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.AssessmentConfig{}, "id = ?", id).Error
	return err
}

// DeleteAssessmentConfigByIds 批量删除assessmentConfig表记录
// Author [yourname](https://github.com/yourname)
func (assessmentConfigService *AssessmentConfigService) DeleteAssessmentConfigByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.AssessmentConfig{}, "id in ?", ids).Error
	return err
}

// UpdateAssessmentConfig 更新assessmentConfig表记录
// Author [yourname](https://github.com/yourname)
func (assessmentConfigService *AssessmentConfigService) UpdateAssessmentConfig(ctx context.Context, assessmentConfig assessment.AssessmentConfig) (err error) {
	err = global.GVA_DB.Model(&assessment.AssessmentConfig{}).Where("id = ?", assessmentConfig.Id).Updates(&assessmentConfig).Error
	return err
}

// GetAssessmentConfig 根据id获取assessmentConfig表记录
// Author [yourname](https://github.com/yourname)
func (assessmentConfigService *AssessmentConfigService) GetAssessmentConfig(ctx context.Context, id string) (assessmentConfig assessment.AssessmentConfig, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&assessmentConfig).Error
	return
}

// GetAssessmentConfigWithNames 根据id获取包含关联名称的考核配置记录
func (assessmentConfigService *AssessmentConfigService) GetAssessmentConfigWithNames(ctx context.Context, id string) (result assessmentReq.AssessmentConfigWithNames, err error) {
	err = global.GVA_DB.Table("assessment_config ac").
		Select(`ac.id, ac.assessment_name, ac.assessment_type, ac.assessment_period, ac.is_archived,
				ac.algorithm_relation_id, ac.bonus_relation_id, ac.score_quota_id,
				ac.created_at, ac.updated_at,
				COALESCE(acg.category_name, '') as algorithm_name,
				COALESCE(bm.bonus_name, '') as bonus_name,
				COALESCE(sqm.quota_name, '') as quota_name`).
		Joins("LEFT JOIN assessment_categories acg ON ac.algorithm_relation_id = acg.id").
		Joins("LEFT JOIN bonus_management bm ON ac.bonus_relation_id = bm.id").
		Joins("LEFT JOIN score_quota_management sqm ON ac.score_quota_id = sqm.id").
		Where("ac.id = ?", id).
		Scan(&result).Error
	return
}

// GetAssessmentConfigInfoList 分页获取assessmentConfig表记录
// Author [yourname](https://github.com/yourname)
func (assessmentConfigService *AssessmentConfigService) GetAssessmentConfigInfoList(ctx context.Context, info assessmentReq.AssessmentConfigSearch) (list []assessment.AssessmentConfig, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&assessment.AssessmentConfig{})
	var assessmentConfigs []assessment.AssessmentConfig
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.AssessmentName != nil && *info.AssessmentName != "" {
		db = db.Where("assessment_name LIKE ?", "%"+*info.AssessmentName+"%")
	}
	if info.AssessmentType != nil && *info.AssessmentType != "" {
		db = db.Where("assessment_type = ?", *info.AssessmentType)
	}
	if info.AssessmentPeriod != nil && *info.AssessmentPeriod != "" {
		db = db.Where("assessment_period = ?", *info.AssessmentPeriod)
	}
	if info.IsArchived != nil {
		db = db.Where("is_archived = ?", *info.IsArchived)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&assessmentConfigs).Error
	return assessmentConfigs, total, err
}

// GetAssessmentConfigWithNamesList 分页获取包含关联名称的考核配置列表
func (assessmentConfigService *AssessmentConfigService) GetAssessmentConfigWithNamesList(ctx context.Context, info assessmentReq.AssessmentConfigSearch) (list []assessmentReq.AssessmentConfigWithNames, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建基础查询
	db := global.GVA_DB.Table("assessment_config ac").
		Select(`ac.id, ac.assessment_name, ac.assessment_type, ac.assessment_period, ac.is_archived,
				ac.algorithm_relation_id, ac.bonus_relation_id, ac.score_quota_id,
				ac.created_at, ac.updated_at,
				COALESCE(acg.category_name, '') as algorithm_name,
				COALESCE(bm.bonus_name, '') as bonus_name,
				COALESCE(sqm.quota_name, '') as quota_name`).
		Joins("LEFT JOIN assessment_categories acg ON ac.algorithm_relation_id = acg.id").
		Joins("LEFT JOIN bonus_management bm ON ac.bonus_relation_id = bm.id").
		Joins("LEFT JOIN score_quota_management sqm ON ac.score_quota_id = sqm.id")

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.AssessmentName != nil && *info.AssessmentName != "" {
		db = db.Where("ac.assessment_name LIKE ?", "%"+*info.AssessmentName+"%")
	}
	if info.AssessmentType != nil && *info.AssessmentType != "" {
		db = db.Where("ac.assessment_type = ?", *info.AssessmentType)
	}
	if info.AssessmentPeriod != nil && *info.AssessmentPeriod != "" {
		db = db.Where("ac.assessment_period = ?", *info.AssessmentPeriod)
	}
	if info.IsArchived != nil {
		db = db.Where("ac.is_archived = ?", *info.IsArchived)
	}

	// 统计总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 分页查询
	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	var results []assessmentReq.AssessmentConfigWithNames
	err = db.Scan(&results).Error
	return results, total, err
}

func (assessmentConfigService *AssessmentConfigService) GetAssessmentConfigPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
