package assessment

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type AssessmentDataService struct{}

// GetAssessmentData 获取考核数据
func (s *AssessmentDataService) GetAssessmentData(ctx context.Context, req assessmentReq.GetAssessmentDataRequest) (result assessmentReq.AssessmentDataResponse, err error) {
	// 1. 首先获取未归档的考核配置
	configs, err := s.getAssessmentConfigs(ctx)
	if err != nil {
		return result, fmt.Errorf("获取考核配置失败: %v", err)
	}
	result.AssessmentConfigs = configs

	// 2. 如果没有未归档的考核配置，直接返回基础数据
	if len(configs) == 0 {
		// 只获取用户作为项目负责人的项目数据
		projects, err := s.getManagerProjects(ctx, req.UserName)
		if err != nil {
			return result, fmt.Errorf("获取项目负责人数据失败: %v", err)
		}
		result.ManagerProjects = projects
		return result, nil
	}

	// 3. 如果有考核配置，则并发获取相关数据
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 获取管理员数据（如果是管理员且有考核配置）
	if req.IsAdmin {
		wg.Add(1)
		go func() {
			defer wg.Done()
			adminData, err := s.getAdminDataWithConfigs(ctx, req.OrgId, configs)
			if err != nil {
				mu.Lock()
				errors = append(errors, fmt.Errorf("获取管理员数据失败: %v", err))
				mu.Unlock()
				return
			}
			mu.Lock()
			result.AdminData = adminData
			mu.Unlock()
		}()
	}

	// 获取考核系数分配数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		coeffData, err := s.getCoefficientData(ctx, req.UserName, req.OrgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("获取系数数据失败: %v", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		result.CoefficientData = coeffData
		mu.Unlock()
	}()

	// 获取用户作为项目负责人的项目
	wg.Add(1)
	go func() {
		defer wg.Done()
		projects, err := s.getManagerProjects(ctx, req.UserName)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("获取项目负责人数据失败: %v", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		result.ManagerProjects = projects
		mu.Unlock()
	}()

	// 获取计算参数映射信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		parameters, err := s.getCalculationParameters(ctx)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("获取计算参数失败: %v", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		result.CalculationParameters = parameters
		mu.Unlock()
	}()

	// 等待所有协程完成
	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		return result, errors[0]
	}

	return result, nil
}

// GetUserParameterScores 获取用户参数评分数据（支持批量查询）
func (s *AssessmentDataService) GetUserParameterScores(ctx context.Context, req assessmentReq.GetUserParameterScoresRequest) (*assessmentReq.GetUserParameterScoresResponse, error) {
	// 步骤1: 批量获取用户信息和计算方法
	userInfoMap, calcMethodMap, err := s.batchGetUserInfoAndCalculationMethods(ctx, req.UserNames)
	if err != nil {
		return nil, fmt.Errorf("批量获取用户信息失败: %v", err)
	}

	// 步骤2: 确定查询的考核配置范围
	configIds, err := s.determineAssessmentConfigs(ctx, req.AssessmentConfigIds)
	if err != nil {
		return nil, fmt.Errorf("确定考核配置范围失败: %v", err)
	}

	// 步骤3: 收集需要校验的用户
	userValidationMap := make(map[string][]int)      // userName -> configIds
	userDeptManagerScoreMap := make(map[string]bool) // userName -> hasDepartmentManagerScore

	for _, userName := range req.UserNames {
		calcMethod := calcMethodMap[userName]
		if calcMethod != nil {
			// 检查用户的计算参数是否包含 project_participation 和 project_manager_score
			hasProjectParticipation := false
			hasProjectManagerScore := false
			hasDepartmentManagerScore := false

			for _, param := range calcMethod.AssignedParameters {
				switch param.ParameterName {
				case "project_participation":
					hasProjectParticipation = true
				case "project_manager_score":
					hasProjectManagerScore = true
				case "department_manager_score":
					hasDepartmentManagerScore = true
				}
			}

			// 如果用户的计算参数包含 project_participation 和 project_manager_score，记录需要校验的用户
			if hasProjectParticipation && hasProjectManagerScore {
				userValidationMap[userName] = configIds
				userDeptManagerScoreMap[userName] = hasDepartmentManagerScore
			}
		}
	}

	// 步骤4: 批量校验用户评分完整性
	validationResults := make(map[string][]assessmentReq.ValidationDetail)
	for userName, configIds := range userValidationMap {
		hasDeptManagerScore := userDeptManagerScoreMap[userName]
		var allValidationDetails []assessmentReq.ValidationDetail

		for _, configId := range configIds {
			validationDetails := s.validateUserScoreCompleteness(ctx, userName, configId, hasDeptManagerScore)
			allValidationDetails = append(allValidationDetails, validationDetails...)
		}

		validationResults[userName] = allValidationDetails
	}

	// 步骤5: 批量查询所有用户的参数评分数据
	allParameterScores, err := s.batchQueryParameterScoresFromAllTables(ctx, req.UserNames, configIds, req.ParameterNames)
	if err != nil {
		return nil, fmt.Errorf("批量查询参数评分失败: %v", err)
	}

	// 步骤6: 组装每个用户的返回结果
	users := make([]assessmentReq.UserParameterScores, 0, len(req.UserNames))
	totalRecords := 0

	for _, userName := range req.UserNames {
		// 获取该用户的校验结果
		userValidationDetails := validationResults[userName]
		if userValidationDetails == nil {
			userValidationDetails = []assessmentReq.ValidationDetail{}
		}

		userScores := s.assembleUserParameterScores(
			userName,
			userInfoMap[userName],
			calcMethodMap[userName],
			allParameterScores[userName],
			req.ParameterNames,
			userValidationDetails,
		)
		users = append(users, userScores)
		totalRecords += len(userScores.ParameterScores)
	}

	// 步骤5: 计算批量查询汇总信息
	summary := s.calculateBatchSummary(users, totalRecords)

	response := &assessmentReq.GetUserParameterScoresResponse{
		Users:   users,
		Summary: summary,
	}

	return response, nil
}

// batchGetUserInfoAndCalculationMethods 批量获取用户信息和计算方法
func (s *AssessmentDataService) batchGetUserInfoAndCalculationMethods(ctx context.Context, userNames []string) (map[string]*assessmentReq.UserInfo, map[string]*assessmentReq.CalculationMethod, error) {
	userInfoMap := make(map[string]*assessmentReq.UserInfo)
	calcMethodMap := make(map[string]*assessmentReq.CalculationMethod)

	// 批量获取用户基本信息，包含组织名称
	var users []struct {
		UserName         string `json:"userName"`
		NickName         string `json:"nickName"`
		UserId           int    `json:"userId"`
		OrganizationName string `json:"organizationName"`
	}

	err := global.GVA_DB.Table("sys_users u").
		Select("u.username as user_name, u.nick_name, u.id as user_id, COALESCE(o.name, '') as organization_name").
		Joins("LEFT JOIN org_organizational_user ou ON u.id = ou.user_id").
		Joins("LEFT JOIN org_org o ON ou.org_id = o.id").
		Where("u.username IN ?", userNames).
		Scan(&users).Error
	if err != nil {
		return nil, nil, err
	}

	// 构建用户信息映射
	for _, user := range users {
		userInfoMap[user.UserName] = &assessmentReq.UserInfo{
			UserName:         user.UserName,
			NickName:         user.NickName,
			UserId:           user.UserId,
			OrganizationName: user.OrganizationName,
		}
	}

	// 批量获取计算方法信息
	// 注意：method_user_assignments表中的user_name字段存储的是用户ID，不是用户名
	// 需要先将用户名转换为用户ID
	var userIds []string
	for _, user := range users {
		userIds = append(userIds, fmt.Sprintf("%d", user.UserId))
	}

	var methodAssignments []struct {
		UserName             string `json:"userName"`
		UserId               int    `json:"userId"`
		MethodId             int    `json:"methodId"`
		MethodName           string `json:"methodName"`
		Description          string `json:"description"`
		Formula              string `json:"formula"`
		AssessmentCategoryId int    `json:"assessmentCategoryId"`
	}

	if len(userIds) > 0 {
		err = global.GVA_DB.Table("method_user_assignments mua").
			Select("mua.user_name, su.id as user_id, cm.id as method_id, cm.method_name, cm.description, cm.formula, cm.assessment_category_id").
			Joins("LEFT JOIN calculation_methods cm ON mua.method_id = cm.id").
			Joins("LEFT JOIN sys_users su ON mua.user_name = CAST(su.id AS CHAR)").
			Where("mua.user_name IN ?", userIds).
			Scan(&methodAssignments).Error
		if err != nil {
			return nil, nil, err
		}
	}

	// 创建用户ID到用户名的映射
	userIdToNameMap := make(map[int]string)
	for _, user := range users {
		userIdToNameMap[user.UserId] = user.UserName
	}

	// 为每个用户获取计算方法和参数
	for _, assignment := range methodAssignments {
		// 获取方法关联的参数
		parameters, err := s.getMethodParameters(ctx, assignment.MethodId)
		if err != nil {
			return nil, nil, err
		}

		// 通过用户ID找到对应的用户名
		if userName, exists := userIdToNameMap[assignment.UserId]; exists {
			calcMethodMap[userName] = &assessmentReq.CalculationMethod{
				MethodId:             assignment.MethodId,
				MethodName:           assignment.MethodName,
				Description:          assignment.Description,
				Formula:              assignment.Formula,
				AssessmentCategoryId: assignment.AssessmentCategoryId,
				AssignedParameters:   parameters,
			}
		}
	}

	// 为没有计算方法的用户设置 nil
	for _, userName := range userNames {
		if _, exists := calcMethodMap[userName]; !exists {
			calcMethodMap[userName] = nil
		}
		if _, exists := userInfoMap[userName]; !exists {
			userInfoMap[userName] = &assessmentReq.UserInfo{
				UserName: userName,
				NickName: "未知用户",
				UserId:   0,
			}
		}
	}

	return userInfoMap, calcMethodMap, nil
}

// determineAssessmentConfigs 确定查询的考核配置范围
func (s *AssessmentDataService) determineAssessmentConfigs(ctx context.Context, requestedConfigIds []int) ([]int, error) {
	// 如果指定了考核配置ID，直接使用
	if len(requestedConfigIds) > 0 {
		return requestedConfigIds, nil
	}

	// 否则查询所有未归档的考核配置
	var configIds []int
	err := global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("is_archived = ?", false).
		Pluck("id", &configIds).Error

	if err != nil {
		return nil, err
	}

	return configIds, nil
}

// batchQueryParameterScoresFromAllTables 批量查询三个表的参数评分数据
func (s *AssessmentDataService) batchQueryParameterScoresFromAllTables(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	allUserScores := make(map[string][]assessmentReq.ParameterScoreDetail)

	// 初始化每个用户的评分列表
	for _, userName := range userNames {
		allUserScores[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 并发查询三个表
	wg.Add(3)

	// 查询 assessment_coefficient_allocation 表
	go func() {
		defer wg.Done()
		scores, err := s.batchQueryFromCoefficientAllocation(ctx, userNames, configIds, parameterNames)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		for userName, userScores := range scores {
			allUserScores[userName] = append(allUserScores[userName], userScores...)
		}
		mu.Unlock()
	}()

	// 查询 project_manager_score 表
	go func() {
		defer wg.Done()
		scores, err := s.batchQueryFromProjectManagerScore(ctx, userNames, configIds, parameterNames)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		for userName, userScores := range scores {
			allUserScores[userName] = append(allUserScores[userName], userScores...)
		}
		mu.Unlock()
	}()

	// 查询 department_manager_score 表
	go func() {
		defer wg.Done()
		scores, err := s.batchQueryFromDepartmentManagerScore(ctx, userNames, configIds, parameterNames)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		for userName, userScores := range scores {
			allUserScores[userName] = append(allUserScores[userName], userScores...)
		}
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return allUserScores, nil
}

// batchQueryFromCoefficientAllocation 批量查询考核系数分配表
func (s *AssessmentDataService) batchQueryFromCoefficientAllocation(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	var rawScores []struct {
		UserName             string    `json:"user_name"`
		RecordId             int       `json:"record_id"`
		AssessmentConfigId   int       `json:"assessment_config_id"`
		AssessmentConfigName string    `json:"assessment_config_name"`
		ParameterName        string    `json:"parameter_name"`
		ParameterNameCn      string    `json:"parameter_name_cn"`
		ScoreValue           *float64  `json:"score_value"`
		ProjectId            *int      `json:"project_id"`
		ProjectName          *string   `json:"project_name"`
		CreatedAt            time.Time `json:"created_at"`
	}

	// 构建查询条件
	query := `
		SELECT
			aca.username as user_name,
			aca.id as record_id,
			aca.assessment_config_id,
			ac.assessment_name as assessment_config_name,
			aca.calculation_parameter as parameter_name,
			COALESCE(cp.parameter_name_cn, aca.calculation_parameter) as parameter_name_cn,
			aca.assessment_coefficient as score_value,
			aca.project_id,
			pi.name as project_name,
			aca.created_at
		FROM assessment_coefficient_allocation aca
		LEFT JOIN assessment_config ac ON aca.assessment_config_id = ac.id
		LEFT JOIN project_info pi ON aca.project_id = pi.id
		LEFT JOIN calculation_parameters cp ON aca.calculation_parameter = cp.parameter_name
		WHERE aca.username IN ?
		AND aca.assessment_config_id IN ?`

	args := []interface{}{userNames, configIds}

	// 如果指定了参数名称，添加参数过滤条件
	if len(parameterNames) > 0 {
		query += " AND aca.calculation_parameter IN ?"
		args = append(args, parameterNames)
	}

	query += " ORDER BY aca.username, aca.assessment_config_id, aca.project_id, aca.created_at DESC"

	err := global.GVA_DB.Raw(query, args...).Scan(&rawScores).Error
	if err != nil {
		return nil, err
	}

	// 按用户名分组
	result := make(map[string][]assessmentReq.ParameterScoreDetail)
	for _, userName := range userNames {
		result[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	for _, raw := range rawScores {
		score := assessmentReq.ParameterScoreDetail{
			ParameterName:        raw.ParameterName,
			ParameterNameCn:      raw.ParameterNameCn,
			DataSource:           "assessment_coefficient_allocation",
			ScoreValue:           raw.ScoreValue,
			AssessmentConfigId:   raw.AssessmentConfigId,
			AssessmentConfigName: raw.AssessmentConfigName,
			ProjectId:            raw.ProjectId,
			ProjectName:          raw.ProjectName,
			ScorerUsername:       nil,
			ScorerNickName:       nil,
			CreatedAt:            raw.CreatedAt,
			RecordId:             raw.RecordId,
		}
		result[raw.UserName] = append(result[raw.UserName], score)
	}

	return result, nil
}

// batchQueryFromProjectManagerScore 批量查询项目经理评分表
func (s *AssessmentDataService) batchQueryFromProjectManagerScore(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	var rawScores []struct {
		UserName             string    `json:"user_name"`
		RecordId             int       `json:"record_id"`
		AssessmentConfigId   int       `json:"assessment_config_id"`
		AssessmentConfigName string    `json:"assessment_config_name"`
		ParameterName        string    `json:"parameter_name"`
		ParameterNameCn      string    `json:"parameter_name_cn"`
		ScoreValue           *float64  `json:"score_value"`
		ProjectId            *int      `json:"project_id"`
		ProjectName          *string   `json:"project_name"`
		ScorerUsername       *string   `json:"scorer_username"`
		ScorerNickName       *string   `json:"scorer_nick_name"`
		CreatedAt            time.Time `json:"created_at"`
	}

	// 构建查询条件
	query := `
		SELECT
			aca.username as user_name,
			pms.id as record_id,
			aca.assessment_config_id,
			ac.assessment_name as assessment_config_name,
			pms.calculation_parameter as parameter_name,
			COALESCE(cp.parameter_name_cn, pms.calculation_parameter) as parameter_name_cn,
			pms.manager_score as score_value,
			aca.project_id,
			pi.name as project_name,
			pms.scorer_username,
			su.nick_name as scorer_nick_name,
			pms.created_at
		FROM project_manager_score pms
		LEFT JOIN assessment_coefficient_allocation aca ON pms.coefficient_allocation_id = aca.id
		LEFT JOIN assessment_config ac ON aca.assessment_config_id = ac.id
		LEFT JOIN project_info pi ON aca.project_id = pi.id
		LEFT JOIN calculation_parameters cp ON pms.calculation_parameter = cp.parameter_name
		LEFT JOIN sys_users su ON pms.scorer_username = su.username
		WHERE aca.username IN ?
		AND aca.assessment_config_id IN ?`

	args := []interface{}{userNames, configIds}

	// 如果指定了参数名称，添加参数过滤条件
	if len(parameterNames) > 0 {
		query += " AND pms.calculation_parameter IN ?"
		args = append(args, parameterNames)
	}

	query += " ORDER BY aca.username, aca.assessment_config_id, aca.project_id, pms.created_at DESC"

	err := global.GVA_DB.Raw(query, args...).Scan(&rawScores).Error
	if err != nil {
		return nil, err
	}

	// 按用户名分组
	result := make(map[string][]assessmentReq.ParameterScoreDetail)
	for _, userName := range userNames {
		result[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	for _, raw := range rawScores {
		score := assessmentReq.ParameterScoreDetail{
			ParameterName:        raw.ParameterName,
			ParameterNameCn:      raw.ParameterNameCn,
			DataSource:           "project_manager_score",
			ScoreValue:           raw.ScoreValue,
			AssessmentConfigId:   raw.AssessmentConfigId,
			AssessmentConfigName: raw.AssessmentConfigName,
			ProjectId:            raw.ProjectId,
			ProjectName:          raw.ProjectName,
			ScorerUsername:       raw.ScorerUsername,
			ScorerNickName:       raw.ScorerNickName,
			CreatedAt:            raw.CreatedAt,
			RecordId:             raw.RecordId,
		}
		result[raw.UserName] = append(result[raw.UserName], score)
	}

	return result, nil
}

// batchQueryFromDepartmentManagerScore 批量查询部门经理评分表
func (s *AssessmentDataService) batchQueryFromDepartmentManagerScore(ctx context.Context, userNames []string, configIds []int, parameterNames []string) (map[string][]assessmentReq.ParameterScoreDetail, error) {
	var rawScores []struct {
		UserName             string    `json:"user_name"`
		RecordId             int       `json:"record_id"`
		AssessmentConfigId   int       `json:"assessment_config_id"`
		AssessmentConfigName string    `json:"assessment_config_name"`
		ParameterName        string    `json:"parameter_name"`
		ParameterNameCn      string    `json:"parameter_name_cn"`
		ScoreValue           *float64  `json:"score_value"`
		ScorerUsername       *string   `json:"scorer_username"`
		ScorerNickName       *string   `json:"scorer_nick_name"`
		CreatedAt            time.Time `json:"created_at"`
	}

	// 构建查询条件
	query := `
		SELECT
			dms.username as user_name,
			dms.id as record_id,
			dms.assessment_config_id,
			ac.assessment_name as assessment_config_name,
			dms.calculation_parameter as parameter_name,
			COALESCE(cp.parameter_name_cn, dms.calculation_parameter) as parameter_name_cn,
			dms.manager_score as score_value,
			dms.scorer_username,
			su.nick_name as scorer_nick_name,
			dms.created_at
		FROM department_manager_score dms
		LEFT JOIN assessment_config ac ON dms.assessment_config_id = ac.id
		LEFT JOIN calculation_parameters cp ON dms.calculation_parameter = cp.parameter_name
		LEFT JOIN sys_users su ON dms.scorer_username = su.username
		WHERE dms.username IN ?
		AND dms.assessment_config_id IN ?`

	args := []interface{}{userNames, configIds}

	// 如果指定了参数名称，添加参数过滤条件
	if len(parameterNames) > 0 {
		query += " AND dms.calculation_parameter IN ?"
		args = append(args, parameterNames)
	}

	query += " ORDER BY dms.username, dms.assessment_config_id, dms.created_at DESC"

	err := global.GVA_DB.Raw(query, args...).Scan(&rawScores).Error
	if err != nil {
		return nil, err
	}

	// 按用户名分组
	result := make(map[string][]assessmentReq.ParameterScoreDetail)
	for _, userName := range userNames {
		result[userName] = make([]assessmentReq.ParameterScoreDetail, 0)
	}

	for _, raw := range rawScores {
		score := assessmentReq.ParameterScoreDetail{
			ParameterName:        raw.ParameterName,
			ParameterNameCn:      raw.ParameterNameCn,
			DataSource:           "department_manager_score",
			ScoreValue:           raw.ScoreValue,
			AssessmentConfigId:   raw.AssessmentConfigId,
			AssessmentConfigName: raw.AssessmentConfigName,
			ProjectId:            nil, // 部门评分没有项目关联
			ProjectName:          nil,
			ScorerUsername:       raw.ScorerUsername,
			ScorerNickName:       raw.ScorerNickName,
			CreatedAt:            raw.CreatedAt,
			RecordId:             raw.RecordId,
		}
		result[raw.UserName] = append(result[raw.UserName], score)
	}

	return result, nil
}

// getAssessmentConfigs 获取未归档的考核配置
func (s *AssessmentDataService) getAssessmentConfigs(ctx context.Context) ([]assessmentReq.AssessmentConfigInfo, error) {
	var configs []assessmentReq.AssessmentConfigInfo

	err := global.GVA_DB.Table("assessment_config").
		Select("id, assessment_name, assessment_type, assessment_period, is_archived, algorithm_relation_id, bonus_relation_id, score_quota_id, created_at").
		Where("is_archived = ?", false).
		Scan(&configs).Error

	return configs, err
}

// getAdminDataWithConfigs 根据考核配置获取管理员数据
func (s *AssessmentDataService) getAdminDataWithConfigs(ctx context.Context, orgId int, configs []assessmentReq.AssessmentConfigInfo) (*assessmentReq.AdminData, error) {
	adminData := &assessmentReq.AdminData{}
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 从考核配置中提取奖金和配额ID
	bonusIds := make(map[int]bool)
	quotaIds := make(map[int]bool)

	for _, config := range configs {
		if config.BonusRelationId > 0 {
			bonusIds[config.BonusRelationId] = true
		}
		if config.ScoreQuotaId > 0 {
			quotaIds[config.ScoreQuotaId] = true
		}
	}

	// 并发获取奖金、配额、组织成员、部门负责人评分信息
	wg.Add(4)

	// 获取奖金信息（基于考核配置中的关联ID，按考核配置分组）
	go func() {
		defer wg.Done()
		bonusInfoMap, err := s.getBonusInfoByConfigs(ctx, configs, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.BonusInfo = bonusInfoMap
		mu.Unlock()
	}()

	// 获取配额信息（基于考核配置中的关联ID，只返回当前组织的信息）
	go func() {
		defer wg.Done()
		quotaInfo, err := s.getQuotaInfoByIds(ctx, quotaIds, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.QuotaInfo = quotaInfo
		mu.Unlock()
	}()

	// 获取组织成员信息
	go func() {
		defer wg.Done()
		orgMembers, err := s.getOrgMembers(ctx, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.OrgMembers = orgMembers
		mu.Unlock()
	}()

	// 获取部门负责人评分信息
	go func() {
		defer wg.Done()
		departmentScores, err := s.getDepartmentManagerScores(ctx, configs, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.DepartmentManagerScores = departmentScores
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return adminData, nil
}

// getBonusInfoByIds 根据ID集合获取奖金信息，只返回指定组织的信息
func (s *AssessmentDataService) getBonusInfoByIds(ctx context.Context, bonusIds map[int]bool, orgId int) (assessmentReq.BonusInfo, error) {
	var bonusInfo assessmentReq.BonusInfo

	// 如果没有奖金ID，返回空数据
	if len(bonusIds) == 0 {
		return bonusInfo, nil
	}

	// 将map转换为切片
	ids := make([]int, 0, len(bonusIds))
	for id := range bonusIds {
		ids = append(ids, id)
	}

	// 获取最新的奖金记录（从指定ID中选择）
	var bonus assessment.BonusManagement
	err := global.GVA_DB.Where("id IN ?", ids).Order("created_at DESC").First(&bonus).Error
	if err != nil {
		return bonusInfo, err
	}

	bonusInfo.Id = *bonus.Id
	bonusInfo.BonusName = *bonus.BonusName
	bonusInfo.Description = *bonus.Description

	// 解析部门分配JSON
	var allAllocations []assessmentReq.DepartmentAllocation
	if bonus.DepartmentAllocations != nil {
		err = json.Unmarshal(bonus.DepartmentAllocations, &allAllocations)
		if err != nil {
			return bonusInfo, err
		}
	}

	// 只返回当前组织ID对应的分配信息
	var filteredAllocations []assessmentReq.DepartmentAllocation
	var totalAmount float64
	for _, alloc := range allAllocations {
		if alloc.DepartmentId == orgId {
			filteredAllocations = append(filteredAllocations, alloc)
			totalAmount += alloc.AllocatedAmount
		}
	}

	bonusInfo.DepartmentAllocations = filteredAllocations
	bonusInfo.TotalAmount = totalAmount

	return bonusInfo, nil
}

// getBonusInfoByConfigs 根据考核配置获取奖金信息，按考核配置ID分组
func (s *AssessmentDataService) getBonusInfoByConfigs(ctx context.Context, configs []assessmentReq.AssessmentConfigInfo, orgId int) (map[string]assessmentReq.BonusInfo, error) {
	bonusInfoMap := make(map[string]assessmentReq.BonusInfo)

	for _, config := range configs {
		configKey := fmt.Sprintf("%d", config.Id)

		if config.BonusRelationId > 0 {
			// 查询奖金信息
			var bonus assessment.BonusManagement
			err := global.GVA_DB.Where("id = ?", config.BonusRelationId).First(&bonus).Error
			if err != nil {
				// 如果找不到奖金记录，跳过这个配置
				continue
			}

			bonusInfo := assessmentReq.BonusInfo{
				Id:          *bonus.Id,
				BonusName:   *bonus.BonusName,
				Description: *bonus.Description,
			}

			// 解析部门分配JSON
			var allAllocations []assessmentReq.DepartmentAllocation
			if bonus.DepartmentAllocations != nil {
				err = json.Unmarshal(bonus.DepartmentAllocations, &allAllocations)
				if err != nil {
					continue
				}
			}

			// 只返回当前组织ID对应的分配信息
			var filteredAllocations []assessmentReq.DepartmentAllocation
			var totalAmount float64
			for _, alloc := range allAllocations {
				if alloc.DepartmentId == orgId {
					filteredAllocations = append(filteredAllocations, alloc)
					totalAmount += alloc.AllocatedAmount
				}
			}

			bonusInfo.DepartmentAllocations = filteredAllocations
			bonusInfo.TotalAmount = totalAmount

			bonusInfoMap[configKey] = bonusInfo
		}
	}

	return bonusInfoMap, nil
}

// getQuotaInfoByIds 根据ID集合获取配额信息，只返回指定组织的信息
func (s *AssessmentDataService) getQuotaInfoByIds(ctx context.Context, quotaIds map[int]bool, orgId int) (assessmentReq.QuotaInfo, error) {
	var quotaInfo assessmentReq.QuotaInfo

	// 如果没有配额ID，返回空数据
	if len(quotaIds) == 0 {
		return quotaInfo, nil
	}

	// 将map转换为切片
	ids := make([]int, 0, len(quotaIds))
	for id := range quotaIds {
		ids = append(ids, id)
	}

	// 获取最新的配额记录（从指定ID中选择）
	var quota assessment.ScoreQuotaManagement
	err := global.GVA_DB.Where("id IN ?", ids).Order("created_at DESC").First(&quota).Error
	if err != nil {
		return quotaInfo, err
	}

	quotaInfo.Id = *quota.Id
	quotaInfo.QuotaName = *quota.QuotaName
	quotaInfo.Description = *quota.Description

	// 解析部门配额JSON
	var allQuotas []assessmentReq.DepartmentQuota
	if quota.DepartmentQuotas != nil {
		err = json.Unmarshal(quota.DepartmentQuotas, &allQuotas)
		if err != nil {
			return quotaInfo, err
		}
	}

	// 只返回当前组织ID对应的配额信息
	var filteredQuotas []assessmentReq.DepartmentQuota
	for _, q := range allQuotas {
		if q.DepartmentId == orgId {
			filteredQuotas = append(filteredQuotas, q)
		}
	}

	quotaInfo.DepartmentQuotas = filteredQuotas

	return quotaInfo, nil
}

// getAdminData 获取管理员数据（保留原方法以兼容）
func (s *AssessmentDataService) getAdminData(ctx context.Context, orgId int) (*assessmentReq.AdminData, error) {
	adminData := &assessmentReq.AdminData{}
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// 并发获取奖金、配额、组织成员信息
	wg.Add(3)

	// 获取奖金信息（兼容性方法，使用默认配置）
	go func() {
		defer wg.Done()
		bonusInfo, err := s.getBonusInfo(ctx)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		// 为兼容性，将单个奖金信息放入map中，使用"default"作为key
		adminData.BonusInfo = map[string]assessmentReq.BonusInfo{
			"default": bonusInfo,
		}
		mu.Unlock()
	}()

	// 获取配额信息
	go func() {
		defer wg.Done()
		quotaInfo, err := s.getQuotaInfo(ctx)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.QuotaInfo = quotaInfo
		mu.Unlock()
	}()

	// 获取组织成员信息
	go func() {
		defer wg.Done()
		orgMembers, err := s.getOrgMembers(ctx, orgId)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		adminData.OrgMembers = orgMembers
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return adminData, nil
}

// getBonusInfo 获取奖金信息
func (s *AssessmentDataService) getBonusInfo(ctx context.Context) (assessmentReq.BonusInfo, error) {
	var bonusInfo assessmentReq.BonusInfo

	// 获取最新的奖金记录
	var bonus assessment.BonusManagement
	err := global.GVA_DB.Order("created_at DESC").First(&bonus).Error
	if err != nil {
		return bonusInfo, err
	}

	bonusInfo.Id = *bonus.Id
	bonusInfo.BonusName = *bonus.BonusName
	bonusInfo.Description = *bonus.Description

	// 解析部门分配JSON
	var allocations []assessmentReq.DepartmentAllocation
	if bonus.DepartmentAllocations != nil {
		err = json.Unmarshal(bonus.DepartmentAllocations, &allocations)
		if err != nil {
			return bonusInfo, err
		}
	}
	bonusInfo.DepartmentAllocations = allocations

	// 计算总金额
	var totalAmount float64
	for _, alloc := range allocations {
		totalAmount += alloc.AllocatedAmount
	}
	bonusInfo.TotalAmount = totalAmount

	return bonusInfo, nil
}

// getQuotaInfo 获取配额信息
func (s *AssessmentDataService) getQuotaInfo(ctx context.Context) (assessmentReq.QuotaInfo, error) {
	var quotaInfo assessmentReq.QuotaInfo

	// 获取最新的配额记录
	var quota assessment.ScoreQuotaManagement
	err := global.GVA_DB.Order("created_at DESC").First(&quota).Error
	if err != nil {
		return quotaInfo, err
	}

	quotaInfo.Id = *quota.Id
	quotaInfo.QuotaName = *quota.QuotaName
	quotaInfo.Description = *quota.Description

	// 解析部门配额JSON
	var quotas []assessmentReq.DepartmentQuota
	if quota.DepartmentQuotas != nil {
		err = json.Unmarshal(quota.DepartmentQuotas, &quotas)
		if err != nil {
			return quotaInfo, err
		}
	}
	quotaInfo.DepartmentQuotas = quotas

	return quotaInfo, nil
}

// getCoefficientData 获取考核系数分配数据 - 返回所有考核配置的系数数据
func (s *AssessmentDataService) getCoefficientData(ctx context.Context, userName string, orgId int) (assessmentReq.CoefficientData, error) {
	var coeffData assessmentReq.CoefficientData

	// 获取最新的考核配置ID
	var latestAssessmentId int
	err := global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("is_archived = ?", false).
		Order("created_at DESC").
		Limit(1).
		Scan(&latestAssessmentId).Error

	if err != nil {
		return coeffData, err
	}

	coeffData.CurrentAssessmentId = latestAssessmentId

	// 获取当前部门的所有用户名
	var departmentUsernames []string
	err = global.GVA_DB.Table("org_organizational_user ou").
		Select("u.username").
		Joins("LEFT JOIN sys_users u ON ou.user_id = u.id").
		Where("ou.org_id = ? AND u.deleted_at IS NULL", orgId).
		Pluck("username", &departmentUsernames).Error

	if err != nil {
		return coeffData, err
	}

	// 获取所有未归档考核配置的ID列表
	var allAssessmentIds []int
	err = global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("is_archived = ?", false).
		Order("created_at DESC").
		Pluck("id", &allAssessmentIds).Error

	if err != nil {
		return coeffData, err
	}

	// 查询考核系数数据：包括本部门成员 + 当前用户负责项目中的其他部门成员
	var allCoeffs []assessmentReq.CoefficientInfo
	if len(allAssessmentIds) > 0 {
		// 获取当前用户负责的项目ID列表
		var managerProjectIds []int
		err = global.GVA_DB.Table("project_info").
			Select("id").
			Where("manager_id = ?", userName).
			Pluck("id", &managerProjectIds).Error

		if err != nil {
			return coeffData, err
		}

		// 构建查询条件：本部门成员 OR 当前用户负责项目中的成员
		var whereConditions []string
		var whereArgs []interface{}

		// 条件1：本部门成员的考核系数
		if len(departmentUsernames) > 0 {
			whereConditions = append(whereConditions, "(aca.assessment_config_id IN ? AND aca.username IN ?)")
			whereArgs = append(whereArgs, allAssessmentIds, departmentUsernames)
		}

		// 条件2：当前用户负责项目中的考核系数（包括其他部门成员）
		if len(managerProjectIds) > 0 {
			whereConditions = append(whereConditions, "(aca.assessment_config_id IN ? AND aca.project_id IN ?)")
			whereArgs = append(whereArgs, allAssessmentIds, managerProjectIds)
		}

		// 如果有查询条件，执行查询
		if len(whereConditions) > 0 {
			whereClause := strings.Join(whereConditions, " OR ")

			err = global.GVA_DB.Table("assessment_coefficient_allocation aca").
				Select(`aca.id, aca.assessment_config_id, aca.username, su.nick_name,
						aca.project_id, aca.assessment_coefficient, pms.manager_score,
						pms.scorer_username, aca.calculation_parameter, aca.created_at,
						GROUP_CONCAT(DISTINCT cp.parameter_name) as user_calculation_parameters`).
				Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
				Joins("LEFT JOIN sys_users su ON aca.username = su.username").
				Joins("LEFT JOIN method_user_assignments mua ON mua.user_name = CAST(su.id AS CHAR)").
				Joins("LEFT JOIN method_parameter_relations mpr ON mua.method_id = mpr.method_id").
				Joins("LEFT JOIN calculation_parameters cp ON mpr.parameter_id = cp.id").
				Where(whereClause, whereArgs...).
				Group("aca.id, aca.assessment_config_id, aca.username, su.nick_name, aca.project_id, aca.assessment_coefficient, pms.manager_score, pms.scorer_username, aca.calculation_parameter, aca.created_at").
				Order("aca.assessment_config_id DESC, aca.created_at DESC").
				Scan(&allCoeffs).Error

			if err != nil {
				return coeffData, err
			}
		}
	}

	// 分离当前考核配置和其他考核配置的数据
	var currentCoeffs []assessmentReq.CoefficientInfo
	var otherCoeffs []assessmentReq.CoefficientInfo

	for _, coeff := range allCoeffs {
		if coeff.AssessmentConfigId == latestAssessmentId {
			currentCoeffs = append(currentCoeffs, coeff)
		} else {
			otherCoeffs = append(otherCoeffs, coeff)
		}
	}

	// 设置当前数据
	if len(currentCoeffs) > 0 {
		coeffData.HasCurrentData = true
		coeffData.Coefficients = currentCoeffs
	} else {
		coeffData.HasCurrentData = false
		coeffData.Coefficients = nil
	}

	// 设置所有未归档考核配置的数据
	coeffData.AllCoefficients = allCoeffs

	// 设置其他考核配置的数据（作为扩展数据返回）
	if len(otherCoeffs) > 0 {
		// 找到最近的一个有数据的考核配置作为previous
		var prevId int
		for _, id := range allAssessmentIds {
			if id != latestAssessmentId {
				for _, coeff := range otherCoeffs {
					if coeff.AssessmentConfigId == id {
						prevId = id
						break
					}
				}
				if prevId != 0 {
					break
				}
			}
		}

		if prevId != 0 {
			var prevCoeffs []assessmentReq.CoefficientInfo
			for _, coeff := range otherCoeffs {
				if coeff.AssessmentConfigId == prevId {
					prevCoeffs = append(prevCoeffs, coeff)
				}
			}
			coeffData.PreviousAssessmentId = &prevId
			coeffData.PreviousCoefficients = prevCoeffs
		}
	}

	return coeffData, nil
}

// getPreviousCoefficients 获取上一次的系数数据 - 返回当前部门所有成员的历史数据
func (s *AssessmentDataService) getPreviousCoefficients(ctx context.Context, userName string, currentAssessmentId int, orgId int) ([]assessmentReq.CoefficientInfo, int, error) {
	var prevCoeffs []assessmentReq.CoefficientInfo
	var prevAssessmentId int

	// 获取上一个考核配置ID
	err := global.GVA_DB.Table("assessment_config").
		Select("id").
		Where("id < ?", currentAssessmentId).
		Order("id DESC").
		Limit(1).
		Scan(&prevAssessmentId).Error

	if err != nil {
		return prevCoeffs, 0, err
	}

	// 获取当前部门的所有用户名
	var departmentUsernames []string
	err = global.GVA_DB.Table("org_organizational_user ou").
		Select("u.username").
		Joins("LEFT JOIN sys_users u ON ou.user_id = u.id").
		Where("ou.org_id = ? AND u.deleted_at IS NULL", orgId).
		Pluck("username", &departmentUsernames).Error

	if err != nil {
		return prevCoeffs, 0, err
	}

	// 获取上一次的系数数据 - 从新的两个表联合查询，查询部门所有成员
	if len(departmentUsernames) > 0 {
		err = global.GVA_DB.Table("assessment_coefficient_allocation aca").
			Select(`aca.id, aca.assessment_config_id, aca.username, su.nick_name,
					aca.project_id, aca.assessment_coefficient, pms.manager_score,
					pms.scorer_username, aca.calculation_parameter, aca.created_at`).
			Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
			Joins("LEFT JOIN sys_users su ON aca.username = su.username").
			Where("aca.assessment_config_id = ? AND aca.username IN ?", prevAssessmentId, departmentUsernames).
			Scan(&prevCoeffs).Error
	}

	return prevCoeffs, prevAssessmentId, err
}

// getOrgMembers 获取组织成员信息
func (s *AssessmentDataService) getOrgMembers(ctx context.Context, orgId int) ([]assessmentReq.OrgMember, error) {
	// 获取组织成员基本信息，包含组织名称和代理负责人状态
	var members []struct {
		UserId           int    `json:"userId"`
		UserName         string `json:"userName"`
		NickName         string `json:"nickName"`
		Email            string `json:"email"`
		Phone            string `json:"phone"`
		AuthorityId      int    `json:"authorityId"`
		IsAdmin          bool   `json:"isAdmin"`
		IsAgentManager   bool   `json:"isAgentManager"`
		OrganizationName string `json:"organizationName"`
	}

	err := global.GVA_DB.Table("org_organizational_user ou").
		Select("u.id as user_id, u.username as user_name, u.nick_name, u.email, u.phone, ou.authority_id, ou.is_admin, COALESCE(ou.is_agent_manager, false) as is_agent_manager, o.name as organization_name").
		Joins("LEFT JOIN sys_users u ON ou.user_id = u.id").
		Joins("LEFT JOIN org_org o ON ou.org_id = o.id").
		Where("ou.org_id = ? AND u.deleted_at IS NULL", orgId).
		Scan(&members).Error

	if err != nil {
		return nil, err
	}

	// 并发获取每个成员的详细信息
	orgMembers := make([]assessmentReq.OrgMember, len(members))
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	for i, member := range members {
		wg.Add(1)
		go func(index int, m struct {
			UserId           int    `json:"userId"`
			UserName         string `json:"userName"`
			NickName         string `json:"nickName"`
			Email            string `json:"email"`
			Phone            string `json:"phone"`
			AuthorityId      int    `json:"authorityId"`
			IsAdmin          bool   `json:"isAdmin"`
			IsAgentManager   bool   `json:"isAgentManager"`
			OrganizationName string `json:"organizationName"`
		}) {
			defer wg.Done()

			orgMember := assessmentReq.OrgMember{
				UserId:           m.UserId,
				UserName:         m.UserName,
				NickName:         m.NickName,
				Email:            m.Email,
				Phone:            m.Phone,
				AuthorityId:      m.AuthorityId,
				IsAdmin:          m.IsAdmin,
				IsAgentManager:   m.IsAgentManager,
				OrganizationName: m.OrganizationName,
			}

			// 获取计算方法信息 - 传递用户ID而不是用户名
			calcMethod, err := s.getUserCalculationMethod(ctx, fmt.Sprintf("%d", m.UserId))
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			orgMember.CalculationMethod = calcMethod

			// 获取项目信息
			projects, err := s.getUserProjects(ctx, m.UserName)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			orgMember.Projects = projects

			mu.Lock()
			orgMembers[index] = orgMember
			mu.Unlock()
		}(i, member)
	}

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return orgMembers, nil
}

// getUserCalculationMethod 获取用户的计算方法
func (s *AssessmentDataService) getUserCalculationMethod(ctx context.Context, userName string) (assessmentReq.CalculationMethod, error) {
	var calcMethod assessmentReq.CalculationMethod

	// 获取用户分配的计算方法
	var methodInfo struct {
		MethodId             int    `json:"methodId"`
		MethodName           string `json:"methodName"`
		Description          string `json:"description"`
		Formula              string `json:"formula"`
		AssessmentCategoryId int    `json:"assessmentCategoryId"`
	}

	err := global.GVA_DB.Table("method_user_assignments mua").
		Select("cm.id as method_id, cm.method_name, cm.description, cm.formula, cm.assessment_category_id").
		Joins("LEFT JOIN calculation_methods cm ON mua.method_id = cm.id").
		Where("mua.user_name = ?", userName).
		Scan(&methodInfo).Error

	if err != nil {
		return calcMethod, err
	}

	calcMethod.MethodId = methodInfo.MethodId
	calcMethod.MethodName = methodInfo.MethodName
	calcMethod.Description = methodInfo.Description
	calcMethod.Formula = methodInfo.Formula
	calcMethod.AssessmentCategoryId = methodInfo.AssessmentCategoryId

	// 获取方法关联的参数
	parameters, err := s.getMethodParameters(ctx, methodInfo.MethodId)
	if err != nil {
		return calcMethod, err
	}
	calcMethod.AssignedParameters = parameters

	return calcMethod, nil
}

// getMethodParameters 获取方法关联的参数
func (s *AssessmentDataService) getMethodParameters(ctx context.Context, methodId int) ([]assessmentReq.AssignedParameter, error) {
	var parameters []assessmentReq.AssignedParameter

	// 由于role_id现在是字符串类型（可能包含多个ID），我们不能直接JOIN
	// 先获取基本参数信息，角色名称在后续处理
	err := global.GVA_DB.Table("method_parameter_relations mpr").
		Select("cp.id, cp.parameter_name, cp.parameter_name_cn, cp.role_id, '' as role_name, cp.assessment_category_id").
		Joins("LEFT JOIN calculation_parameters cp ON mpr.parameter_id = cp.id").
		Where("mpr.method_id = ?", methodId).
		Scan(&parameters).Error

	return parameters, err
}

// getUserProjects 获取用户的项目信息
func (s *AssessmentDataService) getUserProjects(ctx context.Context, userName string) (assessmentReq.ProjectInfo, error) {
	var projects assessmentReq.ProjectInfo

	// 并发获取作为负责人和成员的项目
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	wg.Add(2)

	// 获取作为负责人的项目
	go func() {
		defer wg.Done()
		managerProjects, err := s.getProjectsAsManager(ctx, userName)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		projects.AsManager = managerProjects
		mu.Unlock()
	}()

	// 获取作为成员的项目
	go func() {
		defer wg.Done()
		memberProjects, err := s.getProjectsAsMember(ctx, userName)
		if err != nil {
			mu.Lock()
			errors = append(errors, err)
			mu.Unlock()
			return
		}
		mu.Lock()
		projects.AsMember = memberProjects
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		return projects, errors[0]
	}

	return projects, nil
}

// getProjectsAsManager 获取作为负责人的项目
func (s *AssessmentDataService) getProjectsAsManager(ctx context.Context, userName string) ([]assessmentReq.ProjectDetail, error) {
	var projectsDB []assessmentReq.ProjectDetailDB

	err := global.GVA_DB.Table("project_info pi").
		Select("pi.id as project_id, pi.name as project_name, pi.department_id, org.name as department_name, pi.type, pi.members as members_json").
		Joins("LEFT JOIN org_org org ON pi.department_id = org.id").
		Where("pi.manager_id = ?", userName).
		Scan(&projectsDB).Error

	if err != nil {
		return nil, err
	}

	// 转换为最终结构体
	projects := make([]assessmentReq.ProjectDetail, len(projectsDB))
	for i, projectDB := range projectsDB {
		projects[i] = assessmentReq.ProjectDetail{
			ProjectId:      projectDB.ProjectId,
			ProjectName:    projectDB.ProjectName,
			DepartmentId:   projectDB.DepartmentId,
			DepartmentName: projectDB.DepartmentName,
			Type:           projectDB.Type,
		}

		// 解析JSON成员列表
		members, err := s.parseJSONMembers(projectDB.MembersJSON)
		if err != nil {
			return nil, err
		}
		projects[i].Members = members

		// 获取成员名称
		memberNames, err := s.getMemberNames(ctx, members)
		if err != nil {
			return nil, err
		}
		projects[i].MemberNames = memberNames
	}

	return projects, nil
}

// parseJSONMembers 解析JSON格式的成员列表
func (s *AssessmentDataService) parseJSONMembers(membersJSON string) ([]string, error) {
	if membersJSON == "" || membersJSON == "null" {
		return []string{}, nil
	}

	var members []string
	err := json.Unmarshal([]byte(membersJSON), &members)
	if err != nil {
		return []string{}, err
	}

	return members, nil
}

// getProjectsAsMember 获取作为成员的项目
func (s *AssessmentDataService) getProjectsAsMember(ctx context.Context, userName string) ([]assessmentReq.ProjectDetail, error) {
	var projectsDB []assessmentReq.ProjectDetailDB

	// 查询包含该用户的项目
	err := global.GVA_DB.Table("project_info pi").
		Select("pi.id as project_id, pi.name as project_name, pi.department_id, org.name as department_name, pi.manager_id, pi.type, pi.members as members_json").
		Joins("LEFT JOIN org_org org ON pi.department_id = org.id").
		Where("JSON_CONTAINS(pi.members, ?)", fmt.Sprintf(`"%s"`, userName)).
		Scan(&projectsDB).Error

	if err != nil {
		return nil, err
	}

	// 转换为最终结构体
	projects := make([]assessmentReq.ProjectDetail, len(projectsDB))
	for i, projectDB := range projectsDB {
		projects[i] = assessmentReq.ProjectDetail{
			ProjectId:      projectDB.ProjectId,
			ProjectName:    projectDB.ProjectName,
			DepartmentId:   projectDB.DepartmentId,
			DepartmentName: projectDB.DepartmentName,
			ManagerId:      projectDB.ManagerId,
			Type:           projectDB.Type,
		}

		// 获取项目负责人姓名
		managerName, err := s.getUserNickName(ctx, projectDB.ManagerId)
		if err != nil {
			return nil, err
		}
		projects[i].ManagerName = managerName

		// 解析JSON成员列表
		members, err := s.parseJSONMembers(projectDB.MembersJSON)
		if err != nil {
			return nil, err
		}
		projects[i].Members = members

		// 获取成员名称
		memberNames, err := s.getMemberNames(ctx, members)
		if err != nil {
			return nil, err
		}
		projects[i].MemberNames = memberNames
	}

	return projects, nil
}

// getMemberNames 获取成员姓名列表
func (s *AssessmentDataService) getMemberNames(ctx context.Context, members []string) ([]string, error) {
	if len(members) == 0 {
		return []string{}, nil
	}

	// 查询用户信息，保持顺序
	var users []struct {
		Username string `json:"username"`
		NickName string `json:"nickName"`
	}

	// 使用ORDER BY FIELD确保返回结果按照members的顺序
	orderClause := "FIELD(username"
	for _, member := range members {
		orderClause += ", '" + member + "'"
	}
	orderClause += ")"

	err := global.GVA_DB.Table("sys_users").
		Select("username, nick_name").
		Where("username IN ?", members).
		Order(orderClause).
		Scan(&users).Error

	if err != nil {
		return nil, err
	}

	// 按顺序提取昵称
	names := make([]string, len(users))
	for i, user := range users {
		names[i] = user.NickName
	}

	return names, nil
}

// getUserNickName 获取用户昵称
func (s *AssessmentDataService) getUserNickName(ctx context.Context, userName string) (string, error) {
	var nickName string
	err := global.GVA_DB.Table("sys_users").
		Select("nick_name").
		Where("username = ?", userName).
		Scan(&nickName).Error

	return nickName, err
}

// getManagerProjects 获取用户作为项目负责人的项目
func (s *AssessmentDataService) getManagerProjects(ctx context.Context, userName string) ([]assessmentReq.ManagerProjectInfo, error) {
	var projectsDB []assessmentReq.ManagerProjectInfoDB

	// 获取项目基本信息
	err := global.GVA_DB.Table("project_info pi").
		Select("pi.id as project_id, pi.name as project_name, pi.department_id, org.name as department_name, pi.type, pi.members as members_json").
		Joins("LEFT JOIN org_org org ON pi.department_id = org.id").
		Where("pi.manager_id = ?", userName).
		Scan(&projectsDB).Error

	if err != nil {
		return nil, err
	}

	// 转换为最终结构体
	projects := make([]assessmentReq.ManagerProjectInfo, len(projectsDB))

	// 并发获取每个项目的系数信息和成员名称
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	for i, projectDB := range projectsDB {
		wg.Add(1)
		go func(index int, pDB assessmentReq.ManagerProjectInfoDB) {
			defer wg.Done()

			// 基本信息赋值
			projects[index] = assessmentReq.ManagerProjectInfo{
				ProjectId:      pDB.ProjectId,
				ProjectName:    pDB.ProjectName,
				DepartmentId:   pDB.DepartmentId,
				DepartmentName: pDB.DepartmentName,
				Type:           pDB.Type,
			}

			// 解析JSON成员列表
			members, err := s.parseJSONMembers(pDB.MembersJSON)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}

			// 获取成员名称
			memberNames, err := s.getMemberNames(ctx, members)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}

			// 获取项目的系数信息
			coefficients, err := s.getProjectCoefficients(ctx, pDB.ProjectId)
			if err != nil {
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}

			mu.Lock()
			projects[index].Members = members
			projects[index].MemberNames = memberNames
			projects[index].Coefficients = coefficients
			mu.Unlock()
		}(i, projectDB)
	}

	wg.Wait()

	if len(errors) > 0 {
		return nil, errors[0]
	}

	return projects, nil
}

// getProjectCoefficients 获取项目的系数信息
func (s *AssessmentDataService) getProjectCoefficients(ctx context.Context, projectId int) ([]assessmentReq.CoefficientInfo, error) {
	var coefficients []assessmentReq.CoefficientInfo

	// 从新的两个表联合查询项目的系数信息
	err := global.GVA_DB.Table("assessment_coefficient_allocation aca").
		Select(`aca.id, aca.assessment_config_id, aca.username, su.nick_name,
				aca.project_id, aca.assessment_coefficient, pms.manager_score,
				pms.scorer_username, aca.calculation_parameter, aca.created_at`).
		Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
		Joins("LEFT JOIN sys_users su ON aca.username = su.username").
		Where("aca.project_id = ?", projectId).
		Scan(&coefficients).Error

	return coefficients, err
}

// getDepartmentManagerScores 获取部门负责人评分数据
func (s *AssessmentDataService) getDepartmentManagerScores(ctx context.Context, configs []assessmentReq.AssessmentConfigInfo, orgId int) ([]assessmentReq.DepartmentManagerScoreInfo, error) {
	var departmentScores []assessmentReq.DepartmentManagerScoreInfo

	// 如果没有考核配置，返回空数据
	if len(configs) == 0 {
		return departmentScores, nil
	}

	// 提取考核配置ID
	configIds := make([]int, len(configs))
	configMap := make(map[int]assessmentReq.AssessmentConfigInfo)
	for i, config := range configs {
		configIds[i] = config.Id
		configMap[config.Id] = config
	}

	// 查询部门负责人评分数据
	type DepartmentManagerScoreDB struct {
		Id                   int       `json:"id"`
		AssessmentConfigId   int       `json:"assessmentConfigId"`
		Username             string    `json:"username"`
		DepartmentId         int       `json:"departmentId"`
		ManagerScore         *float64  `json:"managerScore"`
		BonusAmount          *float64  `json:"bonusAmount"`
		ScorerUsername       *string   `json:"scorerUsername"`
		CalculationParameter *string   `json:"calculationParameter"`
		CreatedAt            time.Time `json:"createdAt"`
		UpdatedAt            time.Time `json:"updatedAt"`
	}

	var scoresDB []DepartmentManagerScoreDB
	err := global.GVA_DB.Table("department_manager_score").
		Select("id, assessment_config_id, username, department_id, manager_score, bonus_amount, scorer_username, calculation_parameter, created_at, updated_at").
		Where("assessment_config_id IN ? AND department_id = ?", configIds, orgId).
		Order("assessment_config_id DESC, created_at DESC").
		Scan(&scoresDB).Error

	if err != nil {
		return nil, fmt.Errorf("查询部门负责人评分数据失败: %v", err)
	}

	// 转换为响应结构体
	for _, scoreDB := range scoresDB {
		scoreInfo := assessmentReq.DepartmentManagerScoreInfo{
			Id:                   scoreDB.Id,
			AssessmentConfigId:   scoreDB.AssessmentConfigId,
			AssessmentConfigName: "", // 稍后填充
			Username:             scoreDB.Username,
			UserNickName:         "", // 稍后填充
			DepartmentId:         scoreDB.DepartmentId,
			DepartmentName:       "", // 稍后填充
			ManagerScore:         scoreDB.ManagerScore,
			BonusAmount:          scoreDB.BonusAmount,
			ScorerUsername:       scoreDB.ScorerUsername,
			ScorerNickName:       nil, // 稍后填充
			CalculationParameter: scoreDB.CalculationParameter,
			CreatedAt:            scoreDB.CreatedAt,
			UpdatedAt:            scoreDB.UpdatedAt,
		}

		// 添加考核配置名称
		if config, exists := configMap[scoreDB.AssessmentConfigId]; exists {
			scoreInfo.AssessmentConfigName = config.AssessmentName
		}

		// 获取用户昵称
		userNickName, err := s.getUserNickName(ctx, scoreDB.Username)
		if err == nil {
			scoreInfo.UserNickName = userNickName
		}

		// 获取评分人昵称
		if scoreDB.ScorerUsername != nil && *scoreDB.ScorerUsername != "" {
			scorerNickName, err := s.getUserNickName(ctx, *scoreDB.ScorerUsername)
			if err == nil {
				scoreInfo.ScorerNickName = &scorerNickName
			}
		}

		// 获取部门名称
		departmentName, err := s.getDepartmentName(ctx, scoreDB.DepartmentId)
		if err == nil {
			scoreInfo.DepartmentName = departmentName
		}

		departmentScores = append(departmentScores, scoreInfo)
	}

	return departmentScores, nil
}

// getDepartmentName 根据部门ID获取部门名称
func (s *AssessmentDataService) getDepartmentName(ctx context.Context, departmentId int) (string, error) {
	var departmentName string
	err := global.GVA_DB.Table("org_org").
		Select("name").
		Where("id = ?", departmentId).
		Scan(&departmentName).Error

	if err != nil {
		return "", fmt.Errorf("获取部门名称失败: %v", err)
	}

	return departmentName, nil
}

// getCalculationParameters 获取计算参数映射信息
func (s *AssessmentDataService) getCalculationParameters(ctx context.Context) ([]assessmentReq.CalculationParameterInfo, error) {
	var parameters []assessmentReq.CalculationParameterInfo

	// 由于role_id现在是字符串类型（可能包含多个ID），我们不能直接JOIN
	// 先获取基本参数信息，角色名称在前端处理
	err := global.GVA_DB.Table("calculation_parameters cp").
		Select(`cp.id, cp.parameter_name, cp.parameter_name_cn, cp.role_id,
				'' as role_name,
				cp.assessment_category_id,
				COALESCE(ac.category_name, '') as assessment_category_name`).
		Joins("LEFT JOIN assessment_categories ac ON cp.assessment_category_id = ac.id").
		Order("cp.parameter_name").
		Scan(&parameters).Error

	if err != nil {
		return nil, fmt.Errorf("查询计算参数失败: %v", err)
	}

	return parameters, nil
}

// assembleUserParameterScores 组装用户参数评分数据
func (s *AssessmentDataService) assembleUserParameterScores(userName string, userInfo *assessmentReq.UserInfo, calcMethod *assessmentReq.CalculationMethod, parameterScores []assessmentReq.ParameterScoreDetail, requestedParams []string, validationDetails []assessmentReq.ValidationDetail) assessmentReq.UserParameterScores {
	userScores := assessmentReq.UserParameterScores{
		UserName:          userName,
		UserNickName:      userInfo.NickName,
		CalculationMethod: calcMethod,
		ParameterScores:   parameterScores,
		HasError:          false,
		ErrorMessage:      "",
		ValidationDetails: validationDetails,
	}

	// 设置校验状态和信息
	if len(validationDetails) > 0 {
		// 检查是否有错误
		hasError := false
		var errorMessages []string

		for _, detail := range validationDetails {
			if detail.Status == "error" {
				hasError = true
				errorMessages = append(errorMessages, detail.Message)
			}
		}

		if hasError {
			userScores.ValidationStatus = "error"
			userScores.ValidationMessage = strings.Join(errorMessages, "；")
			userScores.HasError = true
			userScores.ErrorMessage = userScores.ValidationMessage
		} else {
			userScores.ValidationStatus = "success"
			userScores.ValidationMessage = "校验通过"
		}
	} else {
		userScores.ValidationStatus = "success"
		userScores.ValidationMessage = "无需校验"
	}

	// 计算用户个人汇总信息
	userScores.UserSummary = s.calculateUserSummary(calcMethod, parameterScores, requestedParams)

	return userScores
}

// validateUserScoreCompleteness 校验用户评分完整性（返回校验结果而不是错误）
func (s *AssessmentDataService) validateUserScoreCompleteness(ctx context.Context, userName string, configId int, hasDepartmentManagerScore bool) []assessmentReq.ValidationDetail {
	var validationDetails []assessmentReq.ValidationDetail

	// 1. 检查该用户在此考核配置中是否有考核系数分配
	var coefficientCount int64
	err := global.GVA_DB.Model(&assessment.AssessmentCoefficientAllocation{}).
		Where("username = ? AND assessment_config_id = ?", userName, configId).
		Count(&coefficientCount).Error
	if err != nil {
		validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
			Type:    "coefficient_allocation",
			Status:  "error",
			Message: fmt.Sprintf("查询考核系数分配失败: %v", err),
		})
		return validationDetails
	}

	if coefficientCount == 0 {
		validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
			Type:    "coefficient_allocation",
			Status:  "error",
			Message: "该名用户需要考核系数分配，请先进行分配",
		})
		return validationDetails
	}

	// 2. 获取该用户的所有考核系数分配记录
	var coefficients []assessment.AssessmentCoefficientAllocation
	err = global.GVA_DB.Where("username = ? AND assessment_config_id = ?", userName, configId).
		Find(&coefficients).Error
	if err != nil {
		validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
			Type:    "coefficient_allocation",
			Status:  "error",
			Message: fmt.Sprintf("获取考核系数分配记录失败: %v", err),
		})
		return validationDetails
	}

	// 3. 检查每个项目的项目负责人评分情况
	for _, coeff := range coefficients {
		if coeff.ProjectId == nil {
			continue
		}

		// 检查该项目是否有对应的项目负责人评分
		var scoreCount int64
		err = global.GVA_DB.Model(&assessment.ProjectManagerScore{}).
			Where("coefficient_allocation_id = ?", coeff.Id).
			Count(&scoreCount).Error
		if err != nil {
			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:      "project_manager_score",
				Status:    "error",
				Message:   fmt.Sprintf("查询项目负责人评分失败: %v", err),
				ProjectId: coeff.ProjectId,
			})
			continue
		}

		if scoreCount == 0 {
			// 获取项目名称和项目负责人信息
			projectName, managerName, managerId, err := s.getProjectInfoAndManagerWithId(ctx, *coeff.ProjectId)
			if err != nil {
				validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
					Type:      "project_manager_score",
					Status:    "error",
					Message:   fmt.Sprintf("获取项目信息失败: %v", err),
					ProjectId: coeff.ProjectId,
				})
				continue
			}

			// 检查项目负责人是否与被分配系数的用户是同一人
			// 如果是同一人，则跳过校验（项目负责人不需要给自己评分）
			if managerId != "" && managerId == userName {
				// 项目负责人是自己，跳过校验
				continue
			}

			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:        "project_manager_score",
				Status:      "error",
				Message:     fmt.Sprintf("%s项目未评分-项目负责人%s", projectName, managerName),
				ProjectId:   coeff.ProjectId,
				ProjectName: projectName,
				ManagerName: managerName,
			})
		}
	}

	// 4. 检查机构负责人评分（如果需要）
	if hasDepartmentManagerScore {
		var deptScoreCount int64
		err = global.GVA_DB.Table("department_manager_score").
			Where("username = ? AND assessment_config_id = ?", userName, configId).
			Count(&deptScoreCount).Error
		if err != nil {
			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:    "department_manager_score",
				Status:  "error",
				Message: fmt.Sprintf("查询部门经理评分失败: %v", err),
			})
		} else if deptScoreCount == 0 {
			validationDetails = append(validationDetails, assessmentReq.ValidationDetail{
				Type:    "department_manager_score",
				Status:  "error",
				Message: "机构负责人暂无评分",
			})
		}
	}

	return validationDetails
}

// batchValidateUserScoreCompleteness 批量校验用户评分完整性
func (s *AssessmentDataService) batchValidateUserScoreCompleteness(ctx context.Context, userValidationMap map[string][]int) map[string][]assessmentReq.ValidationDetail {
	result := make(map[string][]assessmentReq.ValidationDetail)

	for userName, configIds := range userValidationMap {
		var allValidationDetails []assessmentReq.ValidationDetail

		for _, configId := range configIds {
			// 这里需要确定用户是否需要 department_manager_score 校验
			// 为了简化，我们先假设都需要，后续可以优化
			validationDetails := s.validateUserScoreCompleteness(ctx, userName, configId, true)
			allValidationDetails = append(allValidationDetails, validationDetails...)
		}

		result[userName] = allValidationDetails
	}

	return result
}

// getProjectInfoAndManager 获取项目信息和项目负责人
func (s *AssessmentDataService) getProjectInfoAndManager(ctx context.Context, projectId int) (string, string, error) {
	projectName, managerName, _, err := s.getProjectInfoAndManagerWithId(ctx, projectId)
	return projectName, managerName, err
}

// getProjectInfoAndManagerWithId 获取项目信息和项目负责人（包含负责人ID）
func (s *AssessmentDataService) getProjectInfoAndManagerWithId(ctx context.Context, projectId int) (string, string, string, error) {
	type ProjectInfo struct {
		Name      string  `json:"name"`
		ManagerId *string `json:"managerId"`
	}

	var project ProjectInfo
	err := global.GVA_DB.Table("project_info").
		Select("name, manager_id").
		Where("id = ?", projectId).
		First(&project).Error
	if err != nil {
		return "", "", "", fmt.Errorf("项目不存在: %v", err)
	}

	projectName := project.Name
	if projectName == "" {
		projectName = fmt.Sprintf("项目%d", projectId)
	}

	// 获取项目负责人姓名和ID
	var managerName string
	var managerId string
	if project.ManagerId != nil && *project.ManagerId != "" {
		managerId = *project.ManagerId
		// 从sys_users表获取用户昵称
		var user struct {
			NickName string `json:"nickName"`
		}
		err = global.GVA_DB.Table("sys_users").
			Select("nick_name").
			Where("username = ?", *project.ManagerId).
			First(&user).Error
		if err == nil && user.NickName != "" {
			managerName = user.NickName
		} else {
			// 如果找不到用户昵称，使用用户名
			managerName = *project.ManagerId
		}
	} else {
		managerName = "未知"
		managerId = ""
	}

	return projectName, managerName, managerId, nil
}

// calculateUserSummary 计算用户个人汇总信息
func (s *AssessmentDataService) calculateUserSummary(calcMethod *assessmentReq.CalculationMethod, parameterScores []assessmentReq.ParameterScoreDetail, requestedParams []string) assessmentReq.ParameterScoreSummary {
	summary := assessmentReq.ParameterScoreSummary{
		TotalParameters:    0,
		ScoredParameters:   0,
		UnscoredParameters: 0,
		TotalRecords:       len(parameterScores),
	}

	// 确定需要统计的参数范围
	var targetParams []string
	if len(requestedParams) > 0 {
		targetParams = requestedParams
	} else if calcMethod != nil {
		for _, param := range calcMethod.AssignedParameters {
			targetParams = append(targetParams, param.ParameterName)
		}
	}

	summary.TotalParameters = len(targetParams)

	// 统计已评分的参数
	scoredParams := make(map[string]bool)
	for _, score := range parameterScores {
		if score.ScoreValue != nil {
			scoredParams[score.ParameterName] = true
		}
	}

	summary.ScoredParameters = len(scoredParams)
	summary.UnscoredParameters = summary.TotalParameters - summary.ScoredParameters

	return summary
}

// calculateBatchSummary 计算批量查询汇总信息
func (s *AssessmentDataService) calculateBatchSummary(users []assessmentReq.UserParameterScores, totalRecords int) assessmentReq.BatchQuerySummary {
	summary := assessmentReq.BatchQuerySummary{
		TotalUsers:   len(users),
		SuccessUsers: 0,
		ErrorUsers:   0,
		TotalRecords: totalRecords,
	}

	for _, user := range users {
		if user.HasError {
			summary.ErrorUsers++
		} else {
			summary.SuccessUsers++
		}
	}

	return summary
}

// GetEvaluationDetailsByConfig 根据考核配置获取评价详情
func (s *AssessmentDataService) GetEvaluationDetailsByConfig(ctx context.Context, req assessmentReq.GetEvaluationDetailsByConfigRequest) (*assessmentReq.GetEvaluationDetailsByConfigResponse, error) {
	// 1. 获取考核配置信息
	var assessmentConfig assessment.AssessmentConfig
	err := global.GVA_DB.Where("id = ?", req.AssessmentConfigId).First(&assessmentConfig).Error
	if err != nil {
		return nil, fmt.Errorf("考核配置不存在: %v", err)
	}

	// 2. 基于计算方法分配获取相关用户列表
	userNames, err := s.getUsersByCalculationMethod(ctx, assessmentConfig.AlgorithmRelationId)
	if err != nil {
		return nil, fmt.Errorf("获取用户列表失败: %v", err)
	}

	if len(userNames) == 0 {
		// 返回空结果
		assessmentName := ""
		if assessmentConfig.AssessmentName != nil {
			assessmentName = *assessmentConfig.AssessmentName
		}
		return &assessmentReq.GetEvaluationDetailsByConfigResponse{
			AssessmentConfigId:   req.AssessmentConfigId,
			AssessmentConfigName: assessmentName,
			Users:                []assessmentReq.EvaluationUserDetail{},
			Summary: assessmentReq.EvaluationSummary{
				Total:            0,
				Normal:           0,
				Incomplete:       0,
				NotParticipating: 0,
			},
		}, nil
	}

	// 3. 获取用户详细评分数据
	userScoresReq := assessmentReq.GetUserParameterScoresRequest{
		UserNames:           userNames,
		AssessmentConfigIds: []int{req.AssessmentConfigId},
		ParameterNames:      []string{},
	}

	userScoresResp, err := s.GetUserParameterScores(ctx, userScoresReq)
	if err != nil {
		return nil, fmt.Errorf("获取用户评分数据失败: %v", err)
	}

	// 4. 转换为评价详情格式并分析状态
	evaluationUsers := make([]assessmentReq.EvaluationUserDetail, 0, len(userScoresResp.Users))
	summary := assessmentReq.EvaluationSummary{}

	for _, userScore := range userScoresResp.Users {
		evaluationUser := s.convertToEvaluationUserDetail(userScore)
		evaluationUsers = append(evaluationUsers, evaluationUser)

		// 统计各状态用户数量
		switch evaluationUser.Status {
		case "normal":
			summary.Normal++
		case "incomplete":
			summary.Incomplete++
		case "not_participating":
			summary.NotParticipating++
		}
	}

	summary.Total = len(evaluationUsers)

	assessmentName := ""
	if assessmentConfig.AssessmentName != nil {
		assessmentName = *assessmentConfig.AssessmentName
	}

	return &assessmentReq.GetEvaluationDetailsByConfigResponse{
		AssessmentConfigId:   req.AssessmentConfigId,
		AssessmentConfigName: assessmentName,
		Users:                evaluationUsers,
		Summary:              summary,
	}, nil
}

// getUsersByCalculationMethod 根据计算方法获取相关用户列表
func (s *AssessmentDataService) getUsersByCalculationMethod(ctx context.Context, algorithmRelationId *int) ([]string, error) {
	if algorithmRelationId == nil {
		return []string{}, nil
	}

	var userNames []string

	// 从method_user_assignments表获取分配了该计算方法的用户
	err := global.GVA_DB.Table("method_user_assignments").
		Select("DISTINCT user_name").
		Where("method_id = ?", *algorithmRelationId).
		Pluck("user_name", &userNames).Error

	if err != nil {
		return nil, fmt.Errorf("查询用户分配失败: %v", err)
	}

	// 将user_name转换为username（如果需要的话）
	// 这里假设user_name存储的是用户ID，需要转换为username
	var userNamesList []string
	if len(userNames) > 0 {
		err = global.GVA_DB.Table("sys_users").
			Select("username").
			Where("CAST(id AS CHAR) IN ?", userNames).
			Pluck("username", &userNamesList).Error

		if err != nil {
			return nil, fmt.Errorf("转换用户名失败: %v", err)
		}
	}

	return userNamesList, nil
}

// convertToEvaluationUserDetail 将UserParameterScores转换为EvaluationUserDetail
func (s *AssessmentDataService) convertToEvaluationUserDetail(userScore assessmentReq.UserParameterScores) assessmentReq.EvaluationUserDetail {
	// 分析用户状态
	status, issues := s.analyzeUserStatus(userScore)

	return assessmentReq.EvaluationUserDetail{
		UserName:          userScore.UserName,
		UserNickName:      userScore.UserNickName,
		Status:            status,
		CalculationMethod: userScore.CalculationMethod,
		ParameterScores:   userScore.ParameterScores,
		Issues:            issues,
		UserSummary:       userScore.UserSummary,
	}
}

// analyzeUserStatus 分析用户状态
func (s *AssessmentDataService) analyzeUserStatus(userData assessmentReq.UserParameterScores) (string, []string) {
	var issues []string

	// 1. 检查是否有计算方法
	if userData.CalculationMethod == nil || len(userData.CalculationMethod.AssignedParameters) == 0 {
		return "not_participating", []string{"未分配计算方法"}
	}

	// 2. 检查数据完整性
	parameters := make(map[string]bool)
	for _, param := range userData.CalculationMethod.AssignedParameters {
		parameters[param.ParameterName] = true
	}

	// 检查各种数据完整性问题
	if parameters["project_participation"] {
		// 检查考核系数分配
		hasCoefficient := false
		for _, score := range userData.ParameterScores {
			if score.ParameterName == "project_participation" && score.ScoreValue != nil {
				hasCoefficient = true
				break
			}
		}
		if !hasCoefficient {
			issues = append(issues, "缺少考核系数分配")
		}
	}

	if parameters["project_manager_score"] {
		// 检查项目负责人评分
		hasProjectScore := false
		for _, score := range userData.ParameterScores {
			if score.ParameterName == "project_manager_score" && score.ScoreValue != nil {
				hasProjectScore = true
				break
			}
		}
		if !hasProjectScore {
			issues = append(issues, "缺少项目负责人评分")
		}
	}

	if parameters["department_manager_score"] {
		// 检查部门负责人评分
		hasDeptScore := false
		for _, score := range userData.ParameterScores {
			if score.ParameterName == "department_manager_score" && score.ScoreValue != nil {
				hasDeptScore = true
				break
			}
		}
		if !hasDeptScore {
			issues = append(issues, "缺少部门负责人评分")
		}
	}

	// 3. 根据校验结果确定状态
	if userData.HasError || len(userData.ValidationDetails) > 0 {
		// 检查是否有错误级别的校验问题
		for _, detail := range userData.ValidationDetails {
			if detail.Status == "error" {
				issues = append(issues, detail.Message)
			}
		}
	}

	if len(issues) > 0 {
		return "incomplete", issues
	}

	return "normal", []string{}
}

// GetEvaluationDetailsByConfigId 根据考核配置ID获取评估详情
func (s *AssessmentDataService) GetEvaluationDetailsByConfigId(ctx context.Context, configId int) (result assessmentReq.EvaluationDetailResponse, err error) {
	// 1. 获取考核配置信息
	var config assessment.AssessmentConfig
	err = global.GVA_DB.WithContext(ctx).Where("id = ?", configId).First(&config).Error
	if err != nil {
		return result, fmt.Errorf("获取考核配置失败: %v", err)
	}

	if config.Id != nil {
		result.ConfigId = *config.Id
	}
	if config.AssessmentName != nil {
		result.ConfigName = *config.AssessmentName
	}

	// 2. 获取所有分配了计算方法的用户
	global.GVA_LOG.Info("开始获取用户计算方法")
	startTime := time.Now()
	users, err := s.getUsersWithCalculationMethods(ctx, configId)
	if err != nil {
		return result, fmt.Errorf("获取用户计算方法失败: %v", err)
	}
	global.GVA_LOG.Info(fmt.Sprintf("获取用户计算方法完成，耗时: %v", time.Since(startTime)))

	global.GVA_LOG.Info(fmt.Sprintf("查询到 %d 个用户，配置ID: %d", len(users), configId))

	// 提取所有用户名用于预加载
	usernames := make([]string, len(users))
	for i, user := range users {
		usernames[i] = user.Username
	}

	// 预加载所有评估数据
	global.GVA_LOG.Info(fmt.Sprintf("开始预加载评估数据，用户数量: %d", len(usernames)))
	startTime = time.Now()
	preloadedData, err := s.preloadEvaluationData(ctx, configId, usernames)
	if err != nil {
		return result, fmt.Errorf("预加载评估数据失败: %v", err)
	}
	global.GVA_LOG.Info(fmt.Sprintf("预加载评估数据完成，耗时: %v", time.Since(startTime)))

	// 3. 为每个用户获取详细的评估数据
	global.GVA_LOG.Info("开始处理用户评估详情")
	startTime = time.Now()
	userDetails := make([]assessmentReq.UserEvaluationDetail, 0, len(users))
	statistics := assessmentReq.EvaluationStatistics{}

	for i, userMethod := range users {
		// 每处理10个用户输出一次进度
		if i%10 == 0 {
			global.GVA_LOG.Info(fmt.Sprintf("处理用户进度: %d/%d", i, len(users)))
		}

		// 构建基础用户信息
		baseUser := assessmentReq.UserEvaluationDetail{
			Username:       userMethod.Username,
			UserNickName:   userMethod.UserNickName,
			EmployeeId:     userMethod.Username, // 暂时使用用户名作为员工编号
			DepartmentId:   userMethod.DepartmentId,
			DepartmentName: userMethod.DepartmentName,
		}

		// 设置计算方法信息（现在所有用户都有计算方法，因为查询已经过滤了）
		baseUser.CalculationMethod = &assessmentReq.CalculationMethodInfo{
			MethodId:             userMethod.MethodId,
			MethodName:           userMethod.MethodName,
			Description:          userMethod.Description,
			Formula:              userMethod.Formula,
			AssessmentCategoryId: userMethod.CategoryId,
		}

		// 所有用户都进行详细的参数验证，不再简单地根据ParticipatesInConfig跳过
		detail, err := s.buildUserEvaluationDetailOptimized(ctx, baseUser, configId, preloadedData)
		if err != nil {
			// 记录错误但继续处理其他用户
			global.GVA_LOG.Error(fmt.Sprintf("处理用户 %s 评估详情失败: %v", userMethod.Username, err))
			continue
		}
		userDetails = append(userDetails, detail)

		// 更新统计信息
		switch detail.Status {
		case "normal":
			statistics.NormalUsers++
		case "incomplete":
			statistics.IncompleteUsers++
		case "not_participating":
			statistics.NotParticipating++
		}
	}

	global.GVA_LOG.Info(fmt.Sprintf("用户评估详情处理完成，耗时: %v，处理用户数: %d", time.Since(startTime), len(userDetails)))

	statistics.TotalUsers = len(userDetails)
	result.Users = userDetails
	result.Statistics = statistics

	return result, nil
}

// UserMethodInfo 用户计算方法信息
type UserMethodInfo struct {
	Username             string `json:"username"`
	UserNickName         string `json:"userNickName"`
	DepartmentId         *int   `json:"departmentId"`
	DepartmentName       string `json:"departmentName"`
	MethodId             int    `json:"methodId"`
	MethodName           string `json:"methodName"`
	Description          string `json:"description"`
	Formula              string `json:"formula"`
	CategoryId           int    `json:"categoryId"`
	ParticipatesInConfig int    `json:"participatesInConfig"` // 1表示参与该考核配置，0表示不参与
}

// 并发查询用的数据结构
type UserInfo struct {
	ID             uint   `json:"id"`
	Username       string `json:"username"`
	NickName       string `json:"nick_name"`
	DepartmentId   *int   `gorm:"column:department_id" json:"department_id"`
	DepartmentName string `gorm:"column:department_name" json:"department_name"`
}

type MethodInfo struct {
	ID                   uint   `json:"id"`
	MethodName           string `json:"method_name"`
	Description          string `json:"description"`
	Formula              string `json:"formula"`
	AssessmentCategoryId uint   `json:"assessment_category_id"`
}

type UserMethodMapping struct {
	UserName string `json:"user_name"` // 这里存储的是用户ID字符串
	MethodId uint   `json:"method_id"`
}

// getUsersWithCalculationMethods 使用协程并发获取分配了计算方法的用户
func (s *AssessmentDataService) getUsersWithCalculationMethods(ctx context.Context, configId int) ([]UserMethodInfo, error) {
	// 第一步：获取考核配置信息
	var config struct {
		AssessmentType string `json:"assessment_type"`
	}
	err := global.GVA_DB.WithContext(ctx).Table("assessment_config").
		Select("assessment_type").
		Where("id = ?", configId).
		First(&config).Error
	if err != nil {
		return nil, fmt.Errorf("查询考核配置失败: %v", err)
	}

	// 第二步：获取考核类别ID
	var category struct {
		ID uint `json:"id"`
	}
	err = global.GVA_DB.WithContext(ctx).Table("assessment_categories").
		Select("id").
		Where("category_name = ?", config.AssessmentType).
		First(&category).Error
	if err != nil {
		return nil, fmt.Errorf("查询考核类别失败: %v", err)
	}
	categoryId := category.ID

	// 第三步：并发查询各种数据
	result := &QueryResult{}
	var wg sync.WaitGroup
	wg.Add(4)

	// 协程1：查询所有用户信息
	go func() {
		defer wg.Done()
		var users []UserInfo
		err := global.GVA_DB.WithContext(ctx).Raw(`
			SELECT DISTINCT u.id, u.username, u.nick_name,
				ou.org_id as department_id, COALESCE(o.name, '') as department_name
			FROM sys_users u
			JOIN method_user_assignments mua ON mua.user_name = CAST(u.id AS CHAR)
			JOIN calculation_methods cm ON mua.method_id = cm.id
			LEFT JOIN org_organizational_user ou ON u.id = ou.user_id
			LEFT JOIN org_org o ON ou.org_id = o.id
			WHERE cm.assessment_category_id = ?
		`, categoryId).Scan(&users).Error
		if err != nil {
			result.Error = fmt.Errorf("查询用户信息失败: %v", err)
			return
		}
		result.Users = users
		global.GVA_LOG.Info(fmt.Sprintf("协程1完成: 查询到 %d 个用户", len(users)))
	}()

	// 协程2：查询所有计算方法信息
	go func() {
		defer wg.Done()
		var methods []MethodInfo
		err := global.GVA_DB.WithContext(ctx).Table("calculation_methods").
			Select("id, method_name, description, formula, assessment_category_id").
			Where("assessment_category_id = ?", categoryId).
			Find(&methods).Error
		if err != nil {
			result.Error = fmt.Errorf("查询计算方法失败: %v", err)
			return
		}
		result.Methods = methods
		global.GVA_LOG.Info(fmt.Sprintf("协程2完成: 查询到 %d 个计算方法", len(methods)))
	}()

	// 协程3：查询用户-方法映射关系
	go func() {
		defer wg.Done()
		var mappings []UserMethodMapping
		err := global.GVA_DB.WithContext(ctx).Raw(`
			SELECT mua.user_name, mua.method_id
			FROM method_user_assignments mua
			JOIN calculation_methods cm ON mua.method_id = cm.id
			WHERE cm.assessment_category_id = ?
		`, categoryId).Scan(&mappings).Error
		if err != nil {
			result.Error = fmt.Errorf("查询用户方法映射失败: %v", err)
			return
		}
		result.UserMethodMappings = mappings
		global.GVA_LOG.Info(fmt.Sprintf("协程3完成: 查询到 %d 个用户方法映射", len(mappings)))
	}()

	// 协程4：查询参与考核配置的用户
	go func() {
		defer wg.Done()
		var usernames []string
		err := global.GVA_DB.WithContext(ctx).Table("assessment_coefficient_allocation").
			Select("DISTINCT username").
			Where("assessment_config_id = ?", configId).
			Pluck("username", &usernames).Error
		if err != nil {
			result.Error = fmt.Errorf("查询参与用户失败: %v", err)
			return
		}
		result.ParticipatingUsers = usernames
		global.GVA_LOG.Info(fmt.Sprintf("协程4完成: 查询到 %d 个参与用户", len(usernames)))
	}()

	// 等待所有协程完成
	wg.Wait()

	if result.Error != nil {
		return nil, result.Error
	}

	// 组装数据
	userMethods := s.assembleUserMethodData(result)

	global.GVA_LOG.Info(fmt.Sprintf("并发查询完成: 配置ID %d, 最终组装 %d 个用户方法记录", configId, len(userMethods)))

	return userMethods, nil
}

// preloadEvaluationData 预加载评估数据，用于性能优化
func (s *AssessmentDataService) preloadEvaluationData(ctx context.Context, configId int, usernames []string) (*PreloadedData, error) {
	data := &PreloadedData{
		AllocationsByUser:   make(map[string][]AllocationInfo),
		ProjectInfoMap:      make(map[uint]ProjectInfo),
		ProjectScoreMap:     make(map[uint]float64),
		DepartmentScoreMap:  make(map[string]map[string]float64),
		MethodParametersMap: make(map[int][]assessment.MethodParameterRelations),
		ParametersMap:       make(map[uint]assessment.CalculationParameters),
	}

	// 1. 预加载考核系数分配数据
	global.GVA_LOG.Info("开始预加载考核系数分配数据")
	startTime := time.Now()
	var allocations []struct {
		ID                    uint    `json:"id"`
		Username              string  `json:"username"`
		ProjectId             uint    `json:"project_id"`
		AssessmentCoefficient float64 `json:"assessment_coefficient"`
		CalculationParameter  string  `json:"calculation_parameter"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Table("assessment_coefficient_allocation").
		Select("id, username, project_id, assessment_coefficient, calculation_parameter").
		Where("assessment_config_id = ? AND username IN ?", configId, usernames).
		Find(&allocations).Error

	if err != nil {
		return nil, fmt.Errorf("预加载考核系数分配失败: %v", err)
	}
	global.GVA_LOG.Info(fmt.Sprintf("考核系数分配数据加载完成，耗时: %v，记录数: %d", time.Since(startTime), len(allocations)))

	// 建立用户分配映射和收集项目ID
	projectIds := make(map[uint]bool)
	allocationIds := make([]uint, 0)

	for _, allocation := range allocations {
		if data.AllocationsByUser[allocation.Username] == nil {
			data.AllocationsByUser[allocation.Username] = make([]AllocationInfo, 0)
		}
		data.AllocationsByUser[allocation.Username] = append(data.AllocationsByUser[allocation.Username], AllocationInfo{
			ID:                    allocation.ID,
			ProjectId:             allocation.ProjectId,
			AssessmentCoefficient: allocation.AssessmentCoefficient,
			CalculationParameter:  allocation.CalculationParameter,
		})
		projectIds[allocation.ProjectId] = true
		allocationIds = append(allocationIds, allocation.ID)
	}

	// 2. 预加载项目信息
	global.GVA_LOG.Info(fmt.Sprintf("开始预加载项目信息，项目数量: %d", len(projectIds)))
	startTime = time.Now()
	if len(projectIds) > 0 {
		projectIdList := make([]uint, 0, len(projectIds))
		for id := range projectIds {
			projectIdList = append(projectIdList, id)
		}

		var projects []ProjectInfo
		err = global.GVA_DB.WithContext(ctx).
			Table("project_info").
			Select("id, name, manager_id").
			Where("id IN ?", projectIdList).
			Find(&projects).Error

		if err != nil {
			return nil, fmt.Errorf("预加载项目信息失败: %v", err)
		}

		for _, project := range projects {
			data.ProjectInfoMap[project.ID] = project
		}
		global.GVA_LOG.Info(fmt.Sprintf("项目信息加载完成，耗时: %v，记录数: %d", time.Since(startTime), len(projects)))
	}

	// 3. 预加载项目负责人评分
	global.GVA_LOG.Info(fmt.Sprintf("开始预加载项目负责人评分，分配ID数量: %d", len(allocationIds)))
	startTime = time.Now()
	if len(allocationIds) > 0 {
		var projectScores []struct {
			CoefficientAllocationId uint    `json:"coefficient_allocation_id"`
			ManagerScore            float64 `json:"manager_score"`
		}

		err = global.GVA_DB.WithContext(ctx).
			Table("project_manager_score").
			Select("coefficient_allocation_id, manager_score").
			Where("coefficient_allocation_id IN ?", allocationIds).
			Find(&projectScores).Error

		if err != nil {
			return nil, fmt.Errorf("预加载项目负责人评分失败: %v", err)
		}

		for _, score := range projectScores {
			data.ProjectScoreMap[score.CoefficientAllocationId] = score.ManagerScore
		}
		global.GVA_LOG.Info(fmt.Sprintf("项目负责人评分加载完成，耗时: %v，记录数: %d", time.Since(startTime), len(projectScores)))
	}

	// 4. 预加载部门负责人评分
	global.GVA_LOG.Info("开始预加载部门负责人评分")
	startTime = time.Now()
	var departmentScores []struct {
		Username             string  `json:"username"`
		CalculationParameter string  `json:"calculation_parameter"`
		ManagerScore         float64 `json:"manager_score"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Table("department_manager_score").
		Select("username, calculation_parameter, manager_score").
		Where("assessment_config_id = ? AND username IN ?", configId, usernames).
		Find(&departmentScores).Error

	if err != nil {
		return nil, fmt.Errorf("预加载部门负责人评分失败: %v", err)
	}

	for _, score := range departmentScores {
		if data.DepartmentScoreMap[score.Username] == nil {
			data.DepartmentScoreMap[score.Username] = make(map[string]float64)
		}
		data.DepartmentScoreMap[score.Username][score.CalculationParameter] = score.ManagerScore
	}
	global.GVA_LOG.Info(fmt.Sprintf("部门负责人评分加载完成，耗时: %v，记录数: %d", time.Since(startTime), len(departmentScores)))

	// 5. 预加载计算方法参数关系
	global.GVA_LOG.Info("开始预加载计算方法参数关系")
	startTime = time.Now()

	var methodParams []assessment.MethodParameterRelations
	err = global.GVA_DB.WithContext(ctx).Find(&methodParams).Error
	if err != nil {
		return nil, fmt.Errorf("预加载计算方法参数关系失败: %v", err)
	}

	// 收集所有参数ID
	parameterIds := make(map[uint]bool)
	for _, mp := range methodParams {
		if mp.ParameterId != nil {
			parameterIds[uint(*mp.ParameterId)] = true
		}
		if mp.MethodId != nil {
			methodId := *mp.MethodId
			if data.MethodParametersMap[methodId] == nil {
				data.MethodParametersMap[methodId] = make([]assessment.MethodParameterRelations, 0)
			}
			data.MethodParametersMap[methodId] = append(data.MethodParametersMap[methodId], mp)
		}
	}
	global.GVA_LOG.Info(fmt.Sprintf("计算方法参数关系加载完成，耗时: %v，记录数: %d", time.Since(startTime), len(methodParams)))

	// 6. 预加载计算参数详情
	global.GVA_LOG.Info("开始预加载计算参数详情")
	startTime = time.Now()

	if len(parameterIds) > 0 {
		parameterIdList := make([]uint, 0, len(parameterIds))
		for id := range parameterIds {
			parameterIdList = append(parameterIdList, id)
		}

		var parameters []assessment.CalculationParameters
		err = global.GVA_DB.WithContext(ctx).Where("id IN ?", parameterIdList).Find(&parameters).Error
		if err != nil {
			return nil, fmt.Errorf("预加载计算参数详情失败: %v", err)
		}

		for _, param := range parameters {
			if param.Id != nil {
				data.ParametersMap[uint(*param.Id)] = param
			}
		}
		global.GVA_LOG.Info(fmt.Sprintf("计算参数详情加载完成，耗时: %v，记录数: %d", time.Since(startTime), len(parameters)))
	}

	return data, nil
}

// buildUserEvaluationDetailOptimized 构建用户评估详情（优化版本，使用预加载数据）
func (s *AssessmentDataService) buildUserEvaluationDetailOptimized(ctx context.Context, user assessmentReq.UserEvaluationDetail, configId int, preloadedData *PreloadedData) (assessmentReq.UserEvaluationDetail, error) {
	// 1. 从预加载数据获取用户的计算参数（基于计算方法）
	methodParams, exists := preloadedData.MethodParametersMap[user.CalculationMethod.MethodId]
	if !exists {
		methodParams = []assessment.MethodParameterRelations{}
	}

	// 2. 构建计算参数列表
	parameters := make([]assessmentReq.EvaluationParameterInfo, 0, len(methodParams))
	parameterMap := make(map[string]assessment.CalculationParameters)

	for _, mp := range methodParams {
		if mp.ParameterId != nil {
			// 从预加载数据获取参数详情
			param, exists := preloadedData.ParametersMap[uint(*mp.ParameterId)]
			if !exists {
				continue
			}

			paramInfo := assessmentReq.EvaluationParameterInfo{
				ParameterId: *param.Id,
			}
			if param.ParameterName != nil {
				paramInfo.ParameterName = *param.ParameterName
				parameterMap[*param.ParameterName] = param
			}
			if param.ParameterNameCn != nil {
				paramInfo.ParameterCode = *param.ParameterNameCn
			}

			parameters = append(parameters, paramInfo)
		}
	}
	user.CalculationParameters = parameters

	// 3. 初始化问题列表
	var issues []string
	user.Status = "normal"

	// 4. 检查参数并进行相应的校验（使用预加载数据）
	hasProjectParticipation := s.hasParameter(parameterMap, "project_participation")
	hasDepartmentManagerScore := s.hasParameter(parameterMap, "department_manager_score")

	if hasProjectParticipation {
		// 处理项目参与相关的校验（使用预加载数据）
		projectIssues, projectDetails := s.validateProjectParticipationOptimized(user.Username, preloadedData)
		issues = append(issues, projectIssues...)
		user.ProjectDetails = projectDetails
	}

	if hasDepartmentManagerScore {
		// 处理部门负责人评分相关的校验（使用预加载数据）
		deptIssues, deptScore := s.validateDepartmentManagerScoreOptimized(user.Username, preloadedData)
		issues = append(issues, deptIssues...)
		user.DepartmentManagerScore = deptScore
	}

	if !hasProjectParticipation && !hasDepartmentManagerScore {
		// 如果既没有项目参与也没有部门负责人评分参数
		issues = append(issues, "无机构/部门负责人评分")
	}

	// 5. 检查其他计算参数（使用预加载数据）
	otherParamIssues := s.validateOtherParametersOptimized(user.Username, configId, parameterMap, preloadedData)
	issues = append(issues, otherParamIssues...)

	// 6. 为每个参数获取值（使用预加载数据）
	for i := range user.CalculationParameters {
		value := s.getParameterValueOptimized(user.Username, configId, user.CalculationParameters[i].ParameterName, preloadedData)
		if value != nil {
			user.CalculationParameters[i].Value = value
		}
	}

	// 7. 计算并设置奖金
	bonus := s.calculateUserBonusOptimized(user.Username, configId, preloadedData)
	user.Bonus = bonus

	// 8. 设置最终状态
	user.Issues = issues
	if len(issues) > 0 {
		user.Status = "incomplete"
	}

	return user, nil
}

// validateProjectParticipationOptimized 验证项目参与相关的参数（优化版本）
func (s *AssessmentDataService) validateProjectParticipationOptimized(username string, preloadedData *PreloadedData) ([]string, []assessmentReq.ProjectDetailInfo) {
	var issues []string
	var projectDetails []assessmentReq.ProjectDetailInfo

	// 1. 检查考核系数分配
	allocations, exists := preloadedData.AllocationsByUser[username]
	if !exists || len(allocations) == 0 {
		issues = append(issues, "无考核系数分配")
		return issues, projectDetails
	}

	// 2. 检查项目评分情况
	var totalProjects = len(allocations)
	var scoredProjects int
	var allProjectDetails []assessmentReq.ProjectDetailInfo

	// 收集所有项目信息和评分状态
	for _, allocation := range allocations {
		// 获取项目信息
		projectInfo, exists := preloadedData.ProjectInfoMap[allocation.ProjectId]
		projectName := fmt.Sprintf("项目ID:%d", allocation.ProjectId)
		var projectManagerId string
		if exists {
			projectName = projectInfo.Name
			if projectInfo.ManagerId != nil {
				projectManagerId = *projectInfo.ManagerId
			}
		}

		// 检查项目负责人评分
		score, hasScore := preloadedData.ProjectScoreMap[allocation.ID]
		var finalScore *float64
		var isSubstituted bool

		if hasScore {
			finalScore = &score
			scoredProjects++
		} else if projectManagerId == username {
			// 如果项目负责人是自己，且没有项目负责人评分，使用部门负责人评分替代
			if userScores, exists := preloadedData.DepartmentScoreMap[username]; exists {
				if deptScore, hasDeptScore := userScores["department_manager_score"]; hasDeptScore {
					finalScore = &deptScore
					isSubstituted = true
					scoredProjects++
				}
			}
		}

		projectDetail := assessmentReq.ProjectDetailInfo{
			ProjectId:           int(allocation.ProjectId),
			ProjectName:         projectName,
			Coefficient:         allocation.AssessmentCoefficient,
			ProjectManagerScore: finalScore,
			IsSubstituted:       isSubstituted,
		}

		allProjectDetails = append(allProjectDetails, projectDetail)
	}

	// 只有在部分项目有评分的情况下，才报告具体缺失的项目
	// 如果所有项目都没有评分，则由validateOtherParametersOptimized统一报告"无项目负责人评分的值"
	if scoredProjects > 0 && scoredProjects < totalProjects {
		// 部分项目有评分，部分没有，报告具体缺失的项目
		for _, detail := range allProjectDetails {
			if detail.ProjectManagerScore == nil && !detail.IsSubstituted {
				// 只有在没有评分且不是替代评分的情况下才报告问题
				issues = append(issues, fmt.Sprintf("缺少%s项目的评分", detail.ProjectName))
			}
		}
	}

	projectDetails = allProjectDetails
	return issues, projectDetails
}

// validateDepartmentManagerScoreOptimized 验证部门负责人评分相关的参数（优化版本）
func (s *AssessmentDataService) validateDepartmentManagerScoreOptimized(username string, preloadedData *PreloadedData) ([]string, *float64) {
	var issues []string

	// 检查department_manager_score表中是否有该用户的评分
	userScores, exists := preloadedData.DepartmentScoreMap[username]
	if !exists {
		issues = append(issues, "无机构/部门负责人评分")
		return issues, nil
	}

	score, hasScore := userScores["department_manager_score"]
	if !hasScore {
		issues = append(issues, "无机构/部门负责人评分")
		return issues, nil
	}

	return issues, &score
}

// validateOtherParametersOptimized 验证其他计算参数（优化版本）
func (s *AssessmentDataService) validateOtherParametersOptimized(username string, configId int, parameterMap map[string]assessment.CalculationParameters, preloadedData *PreloadedData) []string {
	var issues []string

	// 遍历所有参数，检查除了project_participation和department_manager_score之外的参数
	for paramName, param := range parameterMap {
		if paramName == "project_participation" || paramName == "department_manager_score" {
			continue
		}

		var hasValue bool

		if paramName == "project_manager_score" {
			// 对于project_manager_score参数，检查项目评分情况（包括替代评分）
			allocations, exists := preloadedData.AllocationsByUser[username]
			if exists && len(allocations) > 0 {
				var totalProjects = len(allocations)
				var scoredProjects int

				for _, allocation := range allocations {
					// 检查是否有项目负责人评分
					if _, hasScore := preloadedData.ProjectScoreMap[allocation.ID]; hasScore {
						scoredProjects++
					} else {
						// 检查是否可以使用部门负责人评分替代
						projectInfo, projectExists := preloadedData.ProjectInfoMap[allocation.ProjectId]
						if projectExists && projectInfo.ManagerId != nil && *projectInfo.ManagerId == username {
							// 项目负责人是自己，检查是否有部门负责人评分可以替代
							if userScores, userExists := preloadedData.DepartmentScoreMap[username]; userExists {
								if _, hasDeptScore := userScores["department_manager_score"]; hasDeptScore {
									scoredProjects++ // 可以使用替代评分
								}
							}
						}
					}
				}

				if scoredProjects == 0 {
					// 所有项目都没有评分（包括替代评分）
					hasValue = false
				} else if scoredProjects == totalProjects {
					// 所有项目都有评分（包括替代评分）
					hasValue = true
				} else {
					// 部分项目有评分，部分没有，这种情况在validateProjectParticipationOptimized中已经处理
					// 这里设置为有效，避免重复报告问题
					hasValue = true
				}
			}
		} else {
			// 对于其他参数，在department_manager_score表中按calculation_parameter查找
			userScores, exists := preloadedData.DepartmentScoreMap[username]
			if exists {
				_, hasValue = userScores[paramName]
			}
		}

		if !hasValue {
			// 获取参数的中文名
			paramNameCn := paramName
			if param.ParameterNameCn != nil {
				paramNameCn = *param.ParameterNameCn
			}
			issues = append(issues, fmt.Sprintf("无%s的值", paramNameCn))
		}
	}

	return issues
}

// getParameterValueOptimized 获取参数值（优化版本）
func (s *AssessmentDataService) getParameterValueOptimized(username string, configId int, parameterName string, preloadedData *PreloadedData) *float64 {
	// 根据参数类型从预加载数据获取
	switch parameterName {
	case "project_participation":
		// 从考核系数分配数据获取
		allocations, exists := preloadedData.AllocationsByUser[username]
		if exists {
			value := float64(len(allocations))
			return &value
		}
	case "project_manager_score":
		// 计算项目负责人评分的平均值（包括替代评分）
		allocations, exists := preloadedData.AllocationsByUser[username]
		if exists {
			var total float64
			var count int
			for _, allocation := range allocations {
				if score, hasScore := preloadedData.ProjectScoreMap[allocation.ID]; hasScore {
					// 使用实际的项目负责人评分
					total += score
					count++
				} else {
					// 检查是否可以使用部门负责人评分替代
					projectInfo, projectExists := preloadedData.ProjectInfoMap[allocation.ProjectId]
					if projectExists && projectInfo.ManagerId != nil && *projectInfo.ManagerId == username {
						// 项目负责人是自己，使用部门负责人评分替代
						if userScores, userExists := preloadedData.DepartmentScoreMap[username]; userExists {
							if deptScore, hasDeptScore := userScores["department_manager_score"]; hasDeptScore {
								total += deptScore
								count++
							}
						}
					}
				}
			}
			if count > 0 {
				avg := total / float64(count)
				return &avg
			}
		}
	case "department_manager_score":
		// 从部门负责人评分数据获取
		userScores, exists := preloadedData.DepartmentScoreMap[username]
		if exists {
			if score, hasScore := userScores[parameterName]; hasScore {
				return &score
			}
		}
	default:
		// 其他参数从部门负责人评分数据获取
		userScores, exists := preloadedData.DepartmentScoreMap[username]
		if exists {
			if score, hasScore := userScores[parameterName]; hasScore {
				return &score
			}
		}
	}
	return nil
}

// calculateUserBonusOptimized 计算用户奖金（优化版本）
func (s *AssessmentDataService) calculateUserBonusOptimized(username string, configId int, preloadedData *PreloadedData) *float64 {
	// 查询用户在当前考核配置下的奖金分配
	var bonusAmount float64
	err := global.GVA_DB.Table("department_manager_score").
		Select("COALESCE(bonus_amount, 0)").
		Where("username = ? AND assessment_config_id = ?", username, configId).
		Scan(&bonusAmount).Error

	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("查询用户 %s 奖金失败: %v", username, err))
		return nil
	}

	// 如果奖金为0，返回nil表示没有奖金
	if bonusAmount == 0 {
		return nil
	}

	return &bonusAmount
}

// QueryResult 查询结果结构体
type QueryResult struct {
	Users              []UserInfo
	Methods            []MethodInfo
	UserMethodMappings []UserMethodMapping
	ParticipatingUsers []string
	Error              error
}

// PreloadedData 预加载的数据结构，用于性能优化
type PreloadedData struct {
	// 考核系数分配数据 - key: username
	AllocationsByUser map[string][]AllocationInfo
	// 项目信息 - key: project_id
	ProjectInfoMap map[uint]ProjectInfo
	// 项目负责人评分 - key: coefficient_allocation_id
	ProjectScoreMap map[uint]float64
	// 部门负责人评分 - key: username
	DepartmentScoreMap map[string]map[string]float64 // username -> parameter -> score
	// 计算方法参数关系 - key: method_id
	MethodParametersMap map[int][]assessment.MethodParameterRelations
	// 计算参数详情 - key: parameter_id
	ParametersMap map[uint]assessment.CalculationParameters
}

type AllocationInfo struct {
	ID                    uint    `json:"id"`
	ProjectId             uint    `json:"project_id"`
	AssessmentCoefficient float64 `json:"assessment_coefficient"`
	CalculationParameter  string  `json:"calculation_parameter"`
}

type ProjectInfo struct {
	ID        uint    `json:"id"`
	Name      string  `json:"name"`
	ManagerId *string `json:"managerId"`
}

// assembleUserMethodData 组装用户方法数据
func (s *AssessmentDataService) assembleUserMethodData(result *QueryResult) []UserMethodInfo {
	var userMethods []UserMethodInfo

	// 创建映射表以提高查找效率
	userMap := make(map[uint]UserInfo)
	for _, user := range result.Users {
		userMap[user.ID] = user
	}

	methodMap := make(map[uint]MethodInfo)
	for _, method := range result.Methods {
		methodMap[method.ID] = method
	}

	participatingUserSet := make(map[string]bool)
	for _, username := range result.ParticipatingUsers {
		participatingUserSet[username] = true
	}

	// 组装用户方法信息
	for _, mapping := range result.UserMethodMappings {
		// 将用户ID字符串转换为uint
		var userId uint
		if _, err := fmt.Sscanf(mapping.UserName, "%d", &userId); err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("转换用户ID失败: %s, 错误: %v", mapping.UserName, err))
			continue
		}

		user, userExists := userMap[userId]
		method, methodExists := methodMap[mapping.MethodId]

		if !userExists || !methodExists {
			global.GVA_LOG.Warn(fmt.Sprintf("用户或方法不存在: 用户ID %d, 方法ID %d", userId, mapping.MethodId))
			continue
		}

		participates := 0
		if participatingUserSet[user.Username] {
			participates = 1
		}

		userMethod := UserMethodInfo{
			Username:             user.Username,
			UserNickName:         user.NickName,
			DepartmentId:         user.DepartmentId,
			DepartmentName:       user.DepartmentName,
			MethodId:             int(method.ID),
			MethodName:           method.MethodName,
			Description:          method.Description,
			Formula:              method.Formula,
			CategoryId:           int(method.AssessmentCategoryId),
			ParticipatesInConfig: participates,
		}

		userMethods = append(userMethods, userMethod)
	}

	return userMethods
}

// buildUserEvaluationDetail 构建用户评估详情
func (s *AssessmentDataService) buildUserEvaluationDetail(ctx context.Context, user assessmentReq.UserEvaluationDetail, configId int) (assessmentReq.UserEvaluationDetail, error) {
	// 1. 获取用户的计算参数（基于计算方法）
	var methodParams []assessment.MethodParameterRelations
	err := global.GVA_DB.WithContext(ctx).
		Where("method_id = ?", user.CalculationMethod.MethodId).
		Find(&methodParams).Error
	if err != nil {
		return user, fmt.Errorf("获取计算方法参数失败: %v", err)
	}

	// 2. 构建计算参数列表
	parameters := make([]assessmentReq.EvaluationParameterInfo, 0, len(methodParams))
	parameterMap := make(map[string]assessment.CalculationParameters)

	for _, mp := range methodParams {
		if mp.ParameterId != nil {
			// 获取参数详情
			var param assessment.CalculationParameters
			err := global.GVA_DB.WithContext(ctx).Where("id = ?", *mp.ParameterId).First(&param).Error
			if err != nil {
				continue
			}

			paramInfo := assessmentReq.EvaluationParameterInfo{
				ParameterId: *param.Id,
			}
			if param.ParameterName != nil {
				paramInfo.ParameterName = *param.ParameterName
				parameterMap[*param.ParameterName] = param
			}
			if param.ParameterNameCn != nil {
				paramInfo.ParameterCode = *param.ParameterNameCn
			}

			parameters = append(parameters, paramInfo)
		}
	}
	user.CalculationParameters = parameters

	// 3. 初始化问题列表
	var issues []string
	user.Status = "normal"

	// 4. 检查参数并进行相应的校验
	hasProjectParticipation := s.hasParameter(parameterMap, "project_participation")
	hasDepartmentManagerScore := s.hasParameter(parameterMap, "department_manager_score")

	if hasProjectParticipation {
		// 处理项目参与相关的校验
		projectIssues, projectDetails := s.validateProjectParticipation(ctx, user.Username, configId)
		issues = append(issues, projectIssues...)
		user.ProjectDetails = projectDetails
	} else if hasDepartmentManagerScore {
		// 处理部门负责人评分相关的校验
		deptIssues, deptScore := s.validateDepartmentManagerScore(ctx, user.Username, configId)
		issues = append(issues, deptIssues...)
		user.DepartmentManagerScore = deptScore
	} else {
		// 如果既没有项目参与也没有部门负责人评分参数
		issues = append(issues, "无机构/部门负责人评分")
	}

	// 5. 检查其他计算参数
	otherParamIssues := s.validateOtherParameters(ctx, user.Username, configId, parameterMap)
	issues = append(issues, otherParamIssues...)

	// 6. 为每个参数获取值
	for i := range user.CalculationParameters {
		value, err := s.getParameterValue(ctx, user.Username, configId, user.CalculationParameters[i].ParameterName)
		if err == nil {
			user.CalculationParameters[i].Value = value
		}
	}

	// 7. 设置最终状态
	user.Issues = issues
	if len(issues) > 0 {
		user.Status = "incomplete"
	}

	return user, nil
}

// getParameterValue 获取参数值
func (s *AssessmentDataService) getParameterValue(ctx context.Context, username string, configId int, parameterName string) (*float64, error) {
	// 根据参数类型从不同表获取数据
	switch parameterName {
	case "project_participation":
		// 从考核系数分配表获取
		var count int64
		err := global.GVA_DB.WithContext(ctx).
			Table("assessment_coefficient_allocation").
			Where("username = ? AND assessment_config_id = ?", username, configId).
			Count(&count).Error
		if err != nil {
			return nil, err
		}
		value := float64(count)
		return &value, nil

	case "project_manager_score":
		// 从项目负责人评分表获取平均分，需要通过coefficient_allocation_id关联
		var avgScore sql.NullFloat64
		err := global.GVA_DB.WithContext(ctx).Raw(`
			SELECT AVG(pms.manager_score)
			FROM project_manager_score pms
			JOIN assessment_coefficient_allocation aca ON pms.coefficient_allocation_id = aca.id
			WHERE aca.username = ? AND aca.assessment_config_id = ?
		`, username, configId).Scan(&avgScore).Error
		if err != nil {
			return nil, err
		}
		if !avgScore.Valid {
			return nil, nil
		}
		return &avgScore.Float64, nil

	case "department_manager_score":
		// 从部门负责人评分表获取平均分
		var avgScore sql.NullFloat64
		err := global.GVA_DB.WithContext(ctx).
			Table("department_manager_score").
			Select("AVG(manager_score)").
			Where("username = ? AND assessment_config_id = ?", username, configId).
			Scan(&avgScore).Error
		if err != nil {
			return nil, err
		}
		if !avgScore.Valid {
			return nil, nil
		}
		return &avgScore.Float64, nil

	default:
		// 从用户参数评分表获取
		var score float64
		err := global.GVA_DB.WithContext(ctx).
			Table("user_parameter_scores").
			Select("score").
			Where("username = ? AND assessment_config_id = ? AND parameter_name = ?", username, configId, parameterName).
			Scan(&score).Error
		if err != nil {
			return nil, err
		}
		return &score, nil
	}
}

// getProjectParticipationData 获取项目参与数据
func (s *AssessmentDataService) getProjectParticipationData(ctx context.Context, username string, configId int) []assessmentReq.ProjectDetailInfo {
	type ProjectData struct {
		ProjectId            int      `json:"projectId"`
		ProjectName          string   `json:"projectName"`
		Coefficient          float64  `json:"coefficient"`
		CalculationParameter string   `json:"calculationParameter"`
		ProjectManagerScore  *float64 `json:"projectManagerScore"`
		ManagerUsername      *string  `json:"managerUsername"`
	}

	var projects []ProjectData
	err := global.GVA_DB.WithContext(ctx).Raw(`
		SELECT
			aca.project_id,
			pi.name as project_name,
			aca.assessment_coefficient as coefficient,
			aca.calculation_parameter,
			pms.manager_score as project_manager_score,
			pms.scorer_username as manager_username
		FROM assessment_coefficient_allocation aca
		LEFT JOIN project_info pi ON aca.project_id = pi.id
		LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id
		WHERE aca.username = ? AND aca.assessment_config_id = ?
	`, username, configId).Scan(&projects).Error

	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("获取用户 %s 项目参与数据失败: %v", username, err))
		return []assessmentReq.ProjectDetailInfo{}
	}

	result := make([]assessmentReq.ProjectDetailInfo, 0, len(projects))
	for _, p := range projects {
		detail := assessmentReq.ProjectDetailInfo{
			ProjectId:              p.ProjectId,
			ProjectName:            p.ProjectName,
			Coefficient:            p.Coefficient,
			CalculationParameter:   p.CalculationParameter,
			ProjectManagerScore:    p.ProjectManagerScore,
			ProjectManagerUsername: p.ManagerUsername,
		}
		result = append(result, detail)
	}

	return result
}

// getDepartmentManagerScoreData 获取部门负责人评分数据
func (s *AssessmentDataService) getDepartmentManagerScoreData(ctx context.Context, username string, configId int) *float64 {
	var score sql.NullFloat64
	err := global.GVA_DB.WithContext(ctx).
		Table("department_manager_score").
		Select("manager_score").
		Where("username = ? AND assessment_config_id = ?", username, configId).
		Scan(&score).Error

	if err != nil {
		global.GVA_LOG.Warn(fmt.Sprintf("获取用户 %s 部门负责人评分失败: %v", username, err))
		return nil
	}

	if !score.Valid {
		return nil
	}

	return &score.Float64
}

// hasParameter 检查参数映射中是否包含指定参数
func (s *AssessmentDataService) hasParameter(parameterMap map[string]assessment.CalculationParameters, parameterName string) bool {
	_, exists := parameterMap[parameterName]
	return exists
}

// validateProjectParticipation 验证项目参与相关的参数
func (s *AssessmentDataService) validateProjectParticipation(ctx context.Context, username string, configId int) ([]string, []assessmentReq.ProjectDetailInfo) {
	var issues []string
	var projectDetails []assessmentReq.ProjectDetailInfo

	// 1. 检查是否在assessment_coefficient_allocation中有数据
	var coefficientAllocations []struct {
		ID                    int     `json:"id"`
		ProjectId             int     `json:"project_id"`
		AssessmentCoefficient float64 `json:"assessment_coefficient"`
		CalculationParameter  string  `json:"calculation_parameter"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Table("assessment_coefficient_allocation").
		Select("id, project_id, assessment_coefficient, calculation_parameter").
		Where("username = ? AND assessment_config_id = ?", username, configId).
		Find(&coefficientAllocations).Error

	if err != nil {
		issues = append(issues, "查询考核系数分配失败")
		return issues, projectDetails
	}

	if len(coefficientAllocations) == 0 {
		issues = append(issues, "无考核系数分配")
		return issues, projectDetails
	}

	// 2. 检查项目评分情况
	// 首先统计总体评分情况
	var totalProjects = len(coefficientAllocations)
	var scoredProjects int
	var allProjectDetails []assessmentReq.ProjectDetailInfo

	// 收集所有项目信息和评分状态
	for _, allocation := range coefficientAllocations {
		// 获取项目信息
		var projectName string
		err := global.GVA_DB.WithContext(ctx).
			Table("project_info").
			Select("name").
			Where("id = ?", allocation.ProjectId).
			Scan(&projectName).Error

		if err != nil || projectName == "" {
			projectName = fmt.Sprintf("项目ID:%d", allocation.ProjectId)
		}

		// 检查项目负责人评分
		var managerScore sql.NullFloat64
		err = global.GVA_DB.WithContext(ctx).
			Table("project_manager_score").
			Select("manager_score").
			Where("coefficient_allocation_id = ?", allocation.ID).
			Scan(&managerScore).Error

		projectDetail := assessmentReq.ProjectDetailInfo{
			ProjectId:           allocation.ProjectId,
			ProjectName:         projectName,
			Coefficient:         allocation.AssessmentCoefficient,
			ProjectManagerScore: nil,
		}

		if err == nil && managerScore.Valid {
			projectDetail.ProjectManagerScore = &managerScore.Float64
			scoredProjects++
		}

		allProjectDetails = append(allProjectDetails, projectDetail)
	}

	// 只有在部分项目有评分的情况下，才报告具体缺失的项目
	// 如果所有项目都没有评分，则由validateOtherParameters统一报告"无项目负责人评分的值"
	if scoredProjects > 0 && scoredProjects < totalProjects {
		// 部分项目有评分，部分没有，报告具体缺失的项目
		for _, detail := range allProjectDetails {
			if detail.ProjectManagerScore == nil {
				issues = append(issues, fmt.Sprintf("缺少%s项目的评分", detail.ProjectName))
			}
		}
	}

	projectDetails = allProjectDetails

	return issues, projectDetails
}

// validateDepartmentManagerScore 验证部门负责人评分相关的参数
func (s *AssessmentDataService) validateDepartmentManagerScore(ctx context.Context, username string, configId int) ([]string, *float64) {
	var issues []string

	// 检查department_manager_score表中是否有该用户的评分
	var score sql.NullFloat64
	err := global.GVA_DB.WithContext(ctx).
		Table("department_manager_score").
		Select("manager_score").
		Where("username = ? AND assessment_config_id = ?", username, configId).
		Scan(&score).Error

	if err != nil || !score.Valid {
		issues = append(issues, "无机构/部门负责人评分")
		return issues, nil
	}

	return issues, &score.Float64
}

// validateOtherParameters 验证其他计算参数
func (s *AssessmentDataService) validateOtherParameters(ctx context.Context, username string, configId int, parameterMap map[string]assessment.CalculationParameters) []string {
	var issues []string

	// 遍历所有参数，检查除了project_participation和department_manager_score之外的参数
	for paramName, param := range parameterMap {
		if paramName == "project_participation" || paramName == "department_manager_score" {
			continue
		}

		var score sql.NullFloat64
		var err error

		if paramName == "project_manager_score" {
			// 对于project_manager_score参数，检查项目评分情况
			// 首先获取用户的所有项目分配
			var totalProjects int64
			var scoredProjects int64

			// 查询总项目数
			err = global.GVA_DB.WithContext(ctx).
				Table("assessment_coefficient_allocation").
				Where("username = ? AND assessment_config_id = ?", username, configId).
				Count(&totalProjects).Error

			if err == nil && totalProjects > 0 {
				// 查询已评分的项目数
				err = global.GVA_DB.WithContext(ctx).Raw(`
					SELECT COUNT(DISTINCT aca.id)
					FROM assessment_coefficient_allocation aca
					JOIN project_manager_score pms ON pms.coefficient_allocation_id = aca.id
					WHERE aca.username = ? AND aca.assessment_config_id = ?
				`, username, configId).Scan(&scoredProjects).Error

				if err == nil {
					if scoredProjects == 0 {
						// 所有项目都没有评分，设置score为无效
						score.Valid = false
					} else if scoredProjects == totalProjects {
						// 所有项目都有评分，计算平均分
						err = global.GVA_DB.WithContext(ctx).Raw(`
							SELECT AVG(pms.manager_score)
							FROM project_manager_score pms
							JOIN assessment_coefficient_allocation aca ON pms.coefficient_allocation_id = aca.id
							WHERE aca.username = ? AND aca.assessment_config_id = ?
						`, username, configId).Scan(&score).Error
					} else {
						// 部分项目有评分，部分没有，这种情况在validateProjectParticipation中已经处理
						// 这里设置为有效，避免重复报告问题
						score.Valid = true
						score.Float64 = 1.0 // 设置一个有效值
					}
				}
			}
		} else {
			// 对于其他参数，在department_manager_score表中按calculation_parameter查找
			err = global.GVA_DB.WithContext(ctx).
				Table("department_manager_score").
				Select("manager_score").
				Where("username = ? AND assessment_config_id = ? AND calculation_parameter = ?", username, configId, paramName).
				Scan(&score).Error
		}

		if err != nil || !score.Valid {
			// 获取参数的中文名
			paramNameCn := paramName
			if param.ParameterNameCn != nil {
				paramNameCn = *param.ParameterNameCn
			}
			issues = append(issues, fmt.Sprintf("无%s的值", paramNameCn))
		}
	}

	return issues
}
