
package assessment

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type BonusManagementService struct {}
// CreateBonusManagement 创建奖金管理记录
// Author [yourname](https://github.com/yourname)
func (bonusManagementService *BonusManagementService) CreateBonusManagement(ctx context.Context, bonusManagement *assessment.BonusManagement) (err error) {
	err = global.GVA_DB.Create(bonusManagement).Error
	return err
}

// DeleteBonusManagement 删除奖金管理记录
// Author [yourname](https://github.com/yourname)
func (bonusManagementService *BonusManagementService)DeleteBonusManagement(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.BonusManagement{},"id = ?",id).Error
	return err
}

// DeleteBonusManagementByIds 批量删除奖金管理记录
// Author [yourname](https://github.com/yourname)
func (bonusManagementService *BonusManagementService)DeleteBonusManagementByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.BonusManagement{},"id in ?",ids).Error
	return err
}

// UpdateBonusManagement 更新奖金管理记录
// Author [yourname](https://github.com/yourname)
func (bonusManagementService *BonusManagementService)UpdateBonusManagement(ctx context.Context, bonusManagement assessment.BonusManagement) (err error) {
	err = global.GVA_DB.Model(&assessment.BonusManagement{}).Where("id = ?",bonusManagement.Id).Updates(&bonusManagement).Error
	return err
}

// GetBonusManagement 根据id获取奖金管理记录
// Author [yourname](https://github.com/yourname)
func (bonusManagementService *BonusManagementService)GetBonusManagement(ctx context.Context, id string) (bonusManagement assessment.BonusManagement, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&bonusManagement).Error
	return
}
// GetBonusManagementInfoList 分页获取奖金管理记录
// Author [yourname](https://github.com/yourname)
func (bonusManagementService *BonusManagementService)GetBonusManagementInfoList(ctx context.Context, info assessmentReq.BonusManagementSearch) (list []assessment.BonusManagement, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&assessment.BonusManagement{})
    var bonusManagements []assessment.BonusManagement
    // 如果有条件搜索 下方会自动创建搜索语句
    
    if info.BonusName != nil && *info.BonusName != "" {
        db = db.Where("bonus_name LIKE ?", "%"+ *info.BonusName+"%")
    }
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&bonusManagements).Error
	return  bonusManagements, total, err
}
func (bonusManagementService *BonusManagementService)GetBonusManagementPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
