package assessment

import (
	"context"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type CalculationMethodsService struct{}

// CreateCalculationMethods 创建calculationMethods表记录
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) CreateCalculationMethods(ctx context.Context, calculationMethods *assessment.CalculationMethods) (err error) {
	err = global.GVA_DB.Create(calculationMethods).Error
	return err
}

// DeleteCalculationMethods 删除计算方法及其关联关系
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) DeleteCalculationMethods(ctx context.Context, id string) (err error) {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 删除参数关联关系
	if err := tx.Where("method_id = ?", id).Delete(&assessment.MethodParameterRelations{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. 删除用户分配关系
	if err := tx.Where("method_id = ?", id).Delete(&assessment.MethodUserAssignments{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. 删除计算方法
	if err := tx.Delete(&assessment.CalculationMethods{}, "id = ?", id).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// DeleteCalculationMethodsByIds 批量删除计算方法及其关联关系
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) DeleteCalculationMethodsByIds(ctx context.Context, ids []string) (err error) {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 删除参数关联关系
	if err := tx.Where("method_id in ?", ids).Delete(&assessment.MethodParameterRelations{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. 删除用户分配关系
	if err := tx.Where("method_id in ?", ids).Delete(&assessment.MethodUserAssignments{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. 删除计算方法
	if err := tx.Delete(&[]assessment.CalculationMethods{}, "id in ?", ids).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// UpdateCalculationMethods 更新calculationMethods表记录
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) UpdateCalculationMethods(ctx context.Context, calculationMethods assessment.CalculationMethods) (err error) {
	err = global.GVA_DB.Model(&assessment.CalculationMethods{}).Where("id = ?", calculationMethods.Id).Updates(&calculationMethods).Error
	return err
}

// GetCalculationMethods 根据id获取calculationMethods表记录
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) GetCalculationMethods(ctx context.Context, id string) (calculationMethods assessment.CalculationMethods, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&calculationMethods).Error
	return
}

// GetCalculationMethodsInfoList 分页获取calculationMethods表记录
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) GetCalculationMethodsInfoList(ctx context.Context, info assessmentReq.CalculationMethodsSearch) (list []assessment.CalculationMethods, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&assessment.CalculationMethods{})
	var calculationMethodss []assessment.CalculationMethods
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&calculationMethodss).Error
	return calculationMethodss, total, err
}
func (calculationMethodsService *CalculationMethodsService) GetCalculationMethodsPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// CreateCalculationMethodWithRelations 创建计算方法及其关联关系
func (calculationMethodsService *CalculationMethodsService) CreateCalculationMethodWithRelations(ctx context.Context, req assessmentReq.CreateCalculationMethodWithRelations) error {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 验证用户是否已被其他计算方法分配
	for _, userID := range req.AssignedUsers {
		userIDStr := fmt.Sprintf("%d", userID)
		var existingAssignment assessment.MethodUserAssignments
		err := tx.Where("user_name = ?", userIDStr).First(&existingAssignment).Error
		if err == nil {
			tx.Rollback()
			return fmt.Errorf("用户ID %d 已被其他计算方法分配", userID)
		}
	}

	// 2. 创建计算方法
	method := assessment.CalculationMethods{
		MethodName:           &req.MethodName,
		Description:          &req.Description,
		Formula:              &req.Formula,
		AssessmentCategoryId: req.AssessmentCategoryId,
	}

	if err := tx.Create(&method).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. 创建参数关联关系
	for _, paramID := range req.SelectedParameters {
		relation := assessment.MethodParameterRelations{
			MethodId:    method.Id,
			ParameterId: &paramID,
		}
		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 4. 创建用户分配关系
	for _, userID := range req.AssignedUsers {
		userIDStr := fmt.Sprintf("%d", userID) // 将int转换为string
		assignment := assessment.MethodUserAssignments{
			MethodId: method.Id,
			UserName: &userIDStr, // 使用UserName字段存储用户ID字符串
		}
		if err := tx.Create(&assignment).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// UpdateCalculationMethodWithRelations 更新计算方法及其关联关系
func (calculationMethodsService *CalculationMethodsService) UpdateCalculationMethodWithRelations(ctx context.Context, req assessmentReq.UpdateCalculationMethodWithRelations) error {
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 验证新分配的用户是否已被其他计算方法分配（排除当前方法）
	for _, userID := range req.AssignedUsers {
		userIDStr := fmt.Sprintf("%d", userID)
		var existingAssignment assessment.MethodUserAssignments
		err := tx.Where("user_name = ? AND method_id != ?", userIDStr, req.ID).First(&existingAssignment).Error
		if err == nil {
			tx.Rollback()
			return fmt.Errorf("用户ID %d 已被其他计算方法分配", userID)
		}
	}

	// 2. 更新计算方法基本信息
	method := assessment.CalculationMethods{
		Id:                   &req.ID,
		MethodName:           &req.MethodName,
		Description:          &req.Description,
		Formula:              &req.Formula,
		RuleType:             req.RuleType,
		AssessmentCategoryId: req.AssessmentCategoryId,
	}

	if err := tx.Save(&method).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. 删除旧的参数关联关系
	if err := tx.Where("method_id = ?", req.ID).Delete(&assessment.MethodParameterRelations{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 4. 创建新的参数关联关系
	for _, paramID := range req.SelectedParameters {
		relation := assessment.MethodParameterRelations{
			MethodId:    &req.ID,
			ParameterId: &paramID,
		}
		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 5. 删除旧的用户分配关系
	if err := tx.Where("method_id = ?", req.ID).Delete(&assessment.MethodUserAssignments{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 6. 创建新的用户分配关系
	for _, userID := range req.AssignedUsers {
		userIDStr := fmt.Sprintf("%d", userID)
		assignment := assessment.MethodUserAssignments{
			MethodId: &req.ID,
			UserName: &userIDStr,
		}
		if err := tx.Create(&assignment).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetCalculationMethodWithRelations 获取计算方法及其关联关系
func (calculationMethodsService *CalculationMethodsService) GetCalculationMethodWithRelations(ctx context.Context, id int) (assessmentReq.CalculationMethodWithRelationsResponse, error) {
	var result assessmentReq.CalculationMethodWithRelationsResponse

	// 1. 获取计算方法基本信息
	var method assessment.CalculationMethods
	err := global.GVA_DB.Where("id = ?", id).First(&method).Error
	if err != nil {
		return result, err
	}

	// 2. 填充基本信息
	result.ID = *method.Id
	result.MethodName = *method.MethodName
	if method.Description != nil {
		result.Description = *method.Description
	}
	if method.Formula != nil {
		result.Formula = *method.Formula
	}
	result.AssessmentCategoryId = method.AssessmentCategoryId
	if method.CreatedAt != nil {
		result.CreatedAt = method.CreatedAt.Format("2006-01-02 15:04:05")
	}
	if method.UpdatedAt != nil {
		result.UpdatedAt = method.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	// 3. 获取参数关联关系
	var paramRelations []assessment.MethodParameterRelations
	err = global.GVA_DB.Where("method_id = ?", id).Find(&paramRelations).Error
	if err != nil {
		return result, err
	}

	result.SelectedParameters = make([]int, 0)
	for _, relation := range paramRelations {
		if relation.ParameterId != nil {
			result.SelectedParameters = append(result.SelectedParameters, *relation.ParameterId)
		}
	}

	// 4. 获取用户分配关系
	var userAssignments []assessment.MethodUserAssignments
	err = global.GVA_DB.Where("method_id = ?", id).Find(&userAssignments).Error
	if err != nil {
		return result, err
	}

	result.AssignedUsers = make([]int, 0)
	for _, assignment := range userAssignments {
		if assignment.UserName != nil {
			// 将字符串转换为int
			var userID int
			if _, err := fmt.Sscanf(*assignment.UserName, "%d", &userID); err == nil {
				result.AssignedUsers = append(result.AssignedUsers, userID)
			}
		}
	}

	return result, nil
}

// GetCalculationMethodByID 根据ID获取计算方法（供规则引擎使用）
// Author [yourname](https://github.com/yourname)
func (calculationMethodsService *CalculationMethodsService) GetCalculationMethodByID(ctx context.Context, id int) (*assessment.CalculationMethods, error) {
	var calculationMethods assessment.CalculationMethods
	err := global.GVA_DB.Where("id = ?", id).First(&calculationMethods).Error
	if err != nil {
		return nil, err
	}
	return &calculationMethods, nil
}
