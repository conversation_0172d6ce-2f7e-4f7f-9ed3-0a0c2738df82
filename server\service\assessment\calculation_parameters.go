package assessment

import (
	"context"
	"fmt"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type CalculationParametersService struct{}

// CreateCalculationParameters 创建计算参数表记录
// Author [yourname](https://github.com/yourname)
func (calculationParametersService *CalculationParametersService) CreateCalculationParameters(ctx context.Context, calculationParameters *assessment.CalculationParameters) (err error) {
	err = global.GVA_DB.Create(calculationParameters).Error
	return err
}

// DeleteCalculationParameters 删除计算参数表记录
// Author [yourname](https://github.com/yourname)
func (calculationParametersService *CalculationParametersService) DeleteCalculationParameters(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.CalculationParameters{}, "id = ?", id).Error
	return err
}

// DeleteCalculationParametersByIds 批量删除计算参数表记录
// Author [yourname](https://github.com/yourname)
func (calculationParametersService *CalculationParametersService) DeleteCalculationParametersByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.CalculationParameters{}, "id in ?", ids).Error
	return err
}

// UpdateCalculationParameters 更新计算参数表记录
// Author [yourname](https://github.com/yourname)
func (calculationParametersService *CalculationParametersService) UpdateCalculationParameters(ctx context.Context, calculationParameters assessment.CalculationParameters) (err error) {
	// 使用 Select 明确指定要更新的字段，包括可能为 nil 的 roleId 字段
	err = global.GVA_DB.Model(&assessment.CalculationParameters{}).
		Where("id = ?", calculationParameters.Id).
		Select("parameter_name", "parameter_name_cn", "role_id", "assessment_category_id").
		Updates(&calculationParameters).Error
	return err
}

// GetCalculationParameters 根据id获取计算参数表记录
// Author [yourname](https://github.com/yourname)
func (calculationParametersService *CalculationParametersService) GetCalculationParameters(ctx context.Context, id string) (calculationParameters assessment.CalculationParameters, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&calculationParameters).Error
	return
}

// GetCalculationParametersInfoList 分页获取计算参数表记录
// Author [yourname](https://github.com/yourname)
func (calculationParametersService *CalculationParametersService) GetCalculationParametersInfoList(ctx context.Context, info assessmentReq.CalculationParametersSearch) (list []assessment.CalculationParameters, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&assessment.CalculationParameters{})
	var calculationParameterss []assessment.CalculationParameters
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&calculationParameterss).Error
	return calculationParameterss, total, err
}

// GetCalculationParametersWithRoleNameList 分页获取包含角色名称的计算参数表记录
func (calculationParametersService *CalculationParametersService) GetCalculationParametersWithRoleNameList(ctx context.Context, info assessmentReq.CalculationParametersSearch) (list []assessmentReq.CalculationParametersWithRoleName, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建基础查询 - 现在role_id可能包含多个ID，所以我们只返回基本信息，角色名称在前端处理
	db := global.GVA_DB.Table("calculation_parameters cp").
		Select("cp.id, cp.parameter_name, cp.parameter_name_cn, cp.role_id, cp.assessment_category_id, cp.created_at, cp.updated_at, '' as role_name")

	// 统计总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 分页查询
	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	var results []assessmentReq.CalculationParametersWithRoleName
	err = db.Scan(&results).Error
	return results, total, err
}

// CheckParameterUsage 检查参数是否被未归档的考核配置使用
func (calculationParametersService *CalculationParametersService) CheckParameterUsage(ctx context.Context, parameterId string) (map[string]interface{}, error) {
	// 查询使用该参数的未归档考核配置
	var results []struct {
		ConfigId       int    `json:"configId"`
		ConfigName     string `json:"configName"`
		AssessmentType string `json:"assessmentType"`
		IsArchived     bool   `json:"isArchived"`
	}

	// 复杂查询：参数 -> 方法参数关联 -> 计算方法 -> 考核配置
	err := global.GVA_DB.Table("calculation_parameters cp").
		Select("ac.id as config_id, ac.assessment_name as config_name, ac.assessment_type, ac.is_archived").
		Joins("INNER JOIN method_parameter_relations mpr ON cp.id = mpr.parameter_id").
		Joins("INNER JOIN calculation_methods cm ON mpr.method_id = cm.id").
		Joins("INNER JOIN assessment_config ac ON cm.id = ac.algorithm_relation_id").
		Where("cp.id = ? AND ac.is_archived = ?", parameterId, false).
		Scan(&results).Error

	if err != nil {
		return nil, fmt.Errorf("查询参数使用情况失败: %v", err)
	}

	// 构建返回结果
	response := map[string]interface{}{
		"canModify":         len(results) == 0,
		"hasUnarchived":     len(results) > 0,
		"unarchivedConfigs": results,
		"message":           "",
	}

	if len(results) > 0 {
		configNames := make([]string, len(results))
		for i, result := range results {
			configNames[i] = result.ConfigName
		}
		response["message"] = fmt.Sprintf("存在未归档的考核配置：%s", strings.Join(configNames, "、"))
	}

	return response, nil
}

func (calculationParametersService *CalculationParametersService) GetCalculationParametersPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
