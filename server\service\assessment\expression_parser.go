package assessment

import (
	"fmt"
	"math"
	"regexp"
	"strconv"
	"strings"
)

// ExpressionParser 表达式解析器
type ExpressionParser struct {
	variables map[string]interface{}
}

// NewExpressionParser 创建新的表达式解析器
func NewExpressionParser(variables map[string]interface{}) *ExpressionParser {
	return &ExpressionParser{
		variables: variables,
	}
}

// EvaluateCondition 评估条件表达式
func (p *ExpressionParser) EvaluateCondition(expression string) (bool, error) {
	// 预处理表达式
	expr := p.preprocessExpression(expression)

	// 先处理函数调用（此时变量名还没有被替换）
	expr, err := p.processFunctions(expr)
	if err != nil {
		return false, err
	}

	// 再替换剩余的变量
	expr, err = p.replaceVariables(expr)
	if err != nil {
		return false, err
	}

	// 评估布尔表达式
	return p.evaluateBooleanExpression(expr)
}

// EvaluateFormula 评估数学公式
func (p *ExpressionParser) EvaluateFormula(formula string) (float64, error) {
	// 预处理公式
	expr := p.preprocessExpression(formula)

	// 先处理函数调用（此时变量名还没有被替换）
	expr, err := p.processFunctions(expr)
	if err != nil {
		return 0, err
	}

	// 再替换剩余的变量
	expr, err = p.replaceVariables(expr)
	if err != nil {
		return 0, err
	}

	// 评估数学表达式
	return p.evaluateMathExpression(expr)
}

// preprocessExpression 预处理表达式
func (p *ExpressionParser) preprocessExpression(expr string) string {
	// 移除多余空格
	expr = strings.TrimSpace(expr)
	expr = regexp.MustCompile(`\s+`).ReplaceAllString(expr, " ")

	// 标准化操作符
	expr = strings.ReplaceAll(expr, "!=", " != ")
	expr = strings.ReplaceAll(expr, "==", " == ")
	expr = strings.ReplaceAll(expr, ">=", " >= ")
	expr = strings.ReplaceAll(expr, "<=", " <= ")
	expr = strings.ReplaceAll(expr, ">", " > ")
	expr = strings.ReplaceAll(expr, "<", " < ")
	expr = strings.ReplaceAll(expr, "&&", " && ")
	expr = strings.ReplaceAll(expr, "||", " || ")

	// 清理多余空格
	expr = regexp.MustCompile(`\s+`).ReplaceAllString(expr, " ")

	return expr
}

// replaceVariables 替换变量
func (p *ExpressionParser) replaceVariables(expr string) (string, error) {
	for varName, value := range p.variables {
		regex := regexp.MustCompile(`\b` + regexp.QuoteMeta(varName) + `\b`)

		if value == nil {
			expr = regex.ReplaceAllString(expr, "null")
		} else {
			switch v := value.(type) {
			case string:
				expr = regex.ReplaceAllString(expr, fmt.Sprintf(`"%s"`, v))
			case bool:
				expr = regex.ReplaceAllString(expr, fmt.Sprintf("%t", v))
			case int, int32, int64:
				expr = regex.ReplaceAllString(expr, fmt.Sprintf("%d", v))
			case float32, float64:
				expr = regex.ReplaceAllString(expr, fmt.Sprintf("%g", v))
			case []interface{}:
				// 数组类型，转换为字符串表示
				arrayStr := p.arrayToString(v)
				expr = regex.ReplaceAllString(expr, arrayStr)
			default:
				// 尝试转换为字符串
				expr = regex.ReplaceAllString(expr, fmt.Sprintf(`"%v"`, v))
			}
		}
	}

	return expr, nil
}

// processFunctions 处理函数调用
func (p *ExpressionParser) processFunctions(expr string) (string, error) {
	// 匹配函数调用模式
	funcPattern := regexp.MustCompile(`([A-Z_]+)\s*\(([^)]*)\)`)

	for {
		matches := funcPattern.FindStringSubmatch(expr)
		if matches == nil {
			break
		}

		funcName := matches[1]
		argsStr := matches[2]

		// 解析参数
		args := p.parseArguments(argsStr)

		// 执行函数
		result, err := p.executeFunction(funcName, args)
		if err != nil {
			return "", err
		}

		// 替换函数调用为结果
		expr = strings.Replace(expr, matches[0], fmt.Sprintf("%g", result), 1)
	}

	return expr, nil
}

// parseArguments 解析函数参数
func (p *ExpressionParser) parseArguments(argsStr string) []string {
	argsStr = strings.TrimSpace(argsStr)
	if argsStr == "" {
		return []string{}
	}

	args := []string{}
	current := ""
	parentheses := 0

	for _, char := range argsStr {
		if char == ',' && parentheses == 0 {
			trimmed := strings.TrimSpace(current)
			if trimmed != "" {
				args = append(args, trimmed)
			}
			current = ""
		} else {
			if char == '(' {
				parentheses++
			} else if char == ')' {
				parentheses--
			}
			current += string(char)
		}
	}

	trimmed := strings.TrimSpace(current)
	if trimmed != "" {
		args = append(args, trimmed)
	}

	return args
}

// executeFunction 执行函数
func (p *ExpressionParser) executeFunction(funcName string, args []string) (float64, error) {
	switch funcName {
	case "SUM":
		return p.executeSum(args)
	case "AVG":
		return p.executeAvg(args)
	case "MAX":
		return p.executeMax(args)
	case "MIN":
		return p.executeMin(args)
	case "COUNT":
		return p.executeCount(args)
	case "ROUND":
		return p.executeRound(args)
	case "ABS":
		return p.executeAbs(args)
	case "SQRT":
		return p.executeSqrt(args)
	case "POW":
		return p.executePow(args)
	case "CEIL":
		return p.executeCeil(args)
	case "FLOOR":
		return p.executeFloor(args)
	case "IF":
		return p.executeIf(args)
	case "CALCULATE_PROJECT_AVG":
		return p.executeCalculateProjectAvg(args)
	default:
		return 0, fmt.Errorf("未知函数: %s", funcName)
	}
}

// executeSum 执行SUM函数
func (p *ExpressionParser) executeSum(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("SUM函数需要1个参数")
	}

	array, err := p.parseArray(args[0])
	if err != nil {
		return 0, err
	}

	sum := 0.0
	for _, val := range array {
		sum += val
	}

	return sum, nil
}

// executeAvg 执行AVG函数
func (p *ExpressionParser) executeAvg(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("AVG函数需要1个参数")
	}

	array, err := p.parseArray(args[0])
	if err != nil {
		return 0, err
	}

	if len(array) == 0 {
		return 0, nil
	}

	sum := 0.0
	for _, val := range array {
		sum += val
	}

	return sum / float64(len(array)), nil
}

// executeMax 执行MAX函数
func (p *ExpressionParser) executeMax(args []string) (float64, error) {
	if len(args) == 0 {
		return 0, fmt.Errorf("MAX函数需要至少1个参数")
	}

	var values []float64

	if len(args) == 1 {
		// 单个数组参数
		array, err := p.parseArray(args[0])
		if err != nil {
			return 0, err
		}
		values = array
	} else {
		// 多个数值参数
		for _, arg := range args {
			val, err := p.parseNumber(arg)
			if err != nil {
				return 0, err
			}
			values = append(values, val)
		}
	}

	if len(values) == 0 {
		return 0, fmt.Errorf("MAX函数没有有效的数值")
	}

	max := values[0]
	for _, val := range values[1:] {
		if val > max {
			max = val
		}
	}

	return max, nil
}

// executeMin 执行MIN函数
func (p *ExpressionParser) executeMin(args []string) (float64, error) {
	if len(args) == 0 {
		return 0, fmt.Errorf("MIN函数需要至少1个参数")
	}

	var values []float64

	if len(args) == 1 {
		// 单个数组参数
		array, err := p.parseArray(args[0])
		if err != nil {
			return 0, err
		}
		values = array
	} else {
		// 多个数值参数
		for _, arg := range args {
			val, err := p.parseNumber(arg)
			if err != nil {
				return 0, err
			}
			values = append(values, val)
		}
	}

	if len(values) == 0 {
		return 0, fmt.Errorf("MIN函数没有有效的数值")
	}

	min := values[0]
	for _, val := range values[1:] {
		if val < min {
			min = val
		}
	}

	return min, nil
}

// executeCount 执行COUNT函数
func (p *ExpressionParser) executeCount(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("COUNT函数需要1个参数，但接收到%d个参数: %v", len(args), args)
	}

	paramName := strings.TrimSpace(args[0])

	// 从variables中获取数据
	if value, exists := p.variables[paramName]; exists {
		if value == nil {
			return 0, nil
		}

		// 处理数组类型
		if arr, ok := value.([]interface{}); ok {
			return float64(len(arr)), nil
		}

		// 处理对象类型（非空对象计数为1）
		if obj, ok := value.(map[string]interface{}); ok {
			if len(obj) > 0 {
				return 1, nil
			}
			return 0, nil
		}

		// 处理其他非空值（计数为1）
		return 1, nil
	}

	return 0, fmt.Errorf("未找到参数: %s", paramName)
}

// getVariableNames 获取所有变量名（用于调试）
func (p *ExpressionParser) getVariableNames() []string {
	names := make([]string, 0, len(p.variables))
	for name := range p.variables {
		names = append(names, name)
	}
	return names
}

// executeRound 执行ROUND函数
func (p *ExpressionParser) executeRound(args []string) (float64, error) {
	if len(args) < 1 || len(args) > 2 {
		return 0, fmt.Errorf("ROUND函数需要1-2个参数")
	}

	num, err := p.parseNumber(args[0])
	if err != nil {
		return 0, err
	}

	decimals := 0
	if len(args) == 2 {
		dec, err := p.parseNumber(args[1])
		if err != nil {
			return 0, err
		}
		decimals = int(dec)
	}

	multiplier := math.Pow(10, float64(decimals))
	return math.Round(num*multiplier) / multiplier, nil
}

// executeAbs 执行ABS函数
func (p *ExpressionParser) executeAbs(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("ABS函数需要1个参数")
	}

	num, err := p.parseNumber(args[0])
	if err != nil {
		return 0, err
	}

	return math.Abs(num), nil
}

// executeSqrt 执行SQRT函数
func (p *ExpressionParser) executeSqrt(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("SQRT函数需要1个参数")
	}

	num, err := p.parseNumber(args[0])
	if err != nil {
		return 0, err
	}

	if num < 0 {
		return 0, fmt.Errorf("SQRT函数不能计算负数的平方根")
	}

	return math.Sqrt(num), nil
}

// executePow 执行POW函数
func (p *ExpressionParser) executePow(args []string) (float64, error) {
	if len(args) != 2 {
		return 0, fmt.Errorf("POW函数需要2个参数")
	}

	base, err := p.parseNumber(args[0])
	if err != nil {
		return 0, err
	}

	exponent, err := p.parseNumber(args[1])
	if err != nil {
		return 0, err
	}

	return math.Pow(base, exponent), nil
}

// executeCeil 执行CEIL函数
func (p *ExpressionParser) executeCeil(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("CEIL函数需要1个参数")
	}

	num, err := p.parseNumber(args[0])
	if err != nil {
		return 0, err
	}

	return math.Ceil(num), nil
}

// executeFloor 执行FLOOR函数
func (p *ExpressionParser) executeFloor(args []string) (float64, error) {
	if len(args) != 1 {
		return 0, fmt.Errorf("FLOOR函数需要1个参数")
	}

	num, err := p.parseNumber(args[0])
	if err != nil {
		return 0, err
	}

	return math.Floor(num), nil
}

// executeIf 执行IF函数
func (p *ExpressionParser) executeIf(args []string) (float64, error) {
	if len(args) != 3 {
		return 0, fmt.Errorf("IF函数需要3个参数")
	}

	// 评估条件
	condition, err := p.evaluateBooleanExpression(args[0])
	if err != nil {
		return 0, err
	}

	if condition {
		return p.parseNumber(args[1])
	} else {
		return p.parseNumber(args[2])
	}
}

// executeCalculateProjectAvg 执行CALCULATE_PROJECT_AVG函数
// 处理项目负责人评分替代逻辑
func (p *ExpressionParser) executeCalculateProjectAvg(args []string) (float64, error) {
	if len(args) != 2 {
		return 0, fmt.Errorf("CALCULATE_PROJECT_AVG函数需要2个参数")
	}

	// 解析项目数据数组参数名和部门经理评分
	projectDataName := strings.TrimSpace(args[0])
	departmentScore, err := p.parseNumber(args[1])
	if err != nil {
		return 0, fmt.Errorf("解析部门经理评分失败: %v", err)
	}

	// 从variables中获取项目数据
	if value, exists := p.variables[projectDataName]; exists {
		if projectArray, ok := value.([]interface{}); ok {
			var weightedScores []float64

			for _, item := range projectArray {
				if projectMap, ok := item.(map[string]interface{}); ok {
					projectManagerScore := p.getFloatValue(projectMap, "project_manager_score")
					projectParticipation := p.getFloatValue(projectMap, "project_participation")
					isLeader := p.getBoolValue(projectMap, "is_leader_of_this_project")

					var score float64
					if isLeader {
						// 项目负责人使用部门经理评分替代
						score = departmentScore * projectParticipation
					} else {
						// 普通成员使用项目经理评分
						score = projectManagerScore * projectParticipation
					}

					weightedScores = append(weightedScores, score)
				}
			}

			if len(weightedScores) == 0 {
				return 0, nil
			}

			// 直接返回加权求和结果，不进行平均
			sum := 0.0
			for _, score := range weightedScores {
				sum += score
			}

			return sum, nil
		}
		return 0, fmt.Errorf("参数 %s 不是数组类型", projectDataName)
	}

	return 0, fmt.Errorf("未找到参数: %s", projectDataName)
}

// getFloatValue 从map中获取浮点数值
func (p *ExpressionParser) getFloatValue(data map[string]interface{}, key string) float64 {
	if value, exists := data[key]; exists {
		if floatVal, err := p.convertToFloat(value); err == nil {
			return floatVal
		}
	}
	return 0.0
}

// getBoolValue 从map中获取布尔值
func (p *ExpressionParser) getBoolValue(data map[string]interface{}, key string) bool {
	if value, exists := data[key]; exists {
		if boolVal, ok := value.(bool); ok {
			return boolVal
		}
		// 尝试从字符串转换
		if strVal, ok := value.(string); ok {
			return strVal == "true" || strVal == "1"
		}
		// 尝试从数字转换
		if numVal, err := p.convertToFloat(value); err == nil {
			return numVal != 0
		}
	}
	return false
}

// parseArray 解析数组参数
func (p *ExpressionParser) parseArray(arrayStr string) ([]float64, error) {
	arrayStr = strings.TrimSpace(arrayStr)

	// 如果是变量名，从variables中获取
	if value, exists := p.variables[arrayStr]; exists {
		if arr, ok := value.([]interface{}); ok {
			result := make([]float64, 0, len(arr))
			for _, item := range arr {
				if num, err := p.convertToFloat(item); err == nil {
					result = append(result, num)
				}
			}
			return result, nil
		}
		return nil, fmt.Errorf("变量 %s 不是数组类型", arrayStr)
	}

	// 如果是数组字面量 [1,2,3]
	if strings.HasPrefix(arrayStr, "[") && strings.HasSuffix(arrayStr, "]") {
		content := arrayStr[1 : len(arrayStr)-1]
		if content == "" {
			return []float64{}, nil
		}

		items := strings.Split(content, ",")
		result := make([]float64, 0, len(items))
		for _, item := range items {
			num, err := p.parseNumber(strings.TrimSpace(item))
			if err != nil {
				return nil, err
			}
			result = append(result, num)
		}
		return result, nil
	}

	return nil, fmt.Errorf("无法解析数组: %s", arrayStr)
}

// parseNumber 解析数字
func (p *ExpressionParser) parseNumber(numStr string) (float64, error) {
	numStr = strings.TrimSpace(numStr)

	// 如果是变量名，从variables中获取
	if value, exists := p.variables[numStr]; exists {
		return p.convertToFloat(value)
	}

	// 直接解析数字
	return strconv.ParseFloat(numStr, 64)
}

// convertToFloat 转换为浮点数
func (p *ExpressionParser) convertToFloat(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("无法转换为数字: %v", value)
	}
}

// arrayToString 数组转字符串
func (p *ExpressionParser) arrayToString(arr []interface{}) string {
	items := make([]string, len(arr))
	for i, item := range arr {
		items[i] = fmt.Sprintf("%v", item)
	}
	return "[" + strings.Join(items, ",") + "]"
}

// evaluateBooleanExpression 评估布尔表达式
func (p *ExpressionParser) evaluateBooleanExpression(expr string) (bool, error) {
	expr = strings.TrimSpace(expr)

	// 处理简单的布尔值
	if expr == "true" {
		return true, nil
	}
	if expr == "false" {
		return false, nil
	}

	// 处理逻辑操作符
	if strings.Contains(expr, " || ") {
		parts := strings.Split(expr, " || ")
		for _, part := range parts {
			result, err := p.evaluateBooleanExpression(strings.TrimSpace(part))
			if err != nil {
				return false, err
			}
			if result {
				return true, nil
			}
		}
		return false, nil
	}

	if strings.Contains(expr, " && ") {
		parts := strings.Split(expr, " && ")
		for _, part := range parts {
			result, err := p.evaluateBooleanExpression(strings.TrimSpace(part))
			if err != nil {
				return false, err
			}
			if !result {
				return false, nil
			}
		}
		return true, nil
	}

	// 处理比较操作符
	return p.evaluateComparison(expr)
}

// evaluateComparison 评估比较表达式
func (p *ExpressionParser) evaluateComparison(expr string) (bool, error) {
	operators := []string{" != ", " == ", " >= ", " <= ", " > ", " < "}

	for _, op := range operators {
		if strings.Contains(expr, op) {
			parts := strings.SplitN(expr, op, 2)
			if len(parts) != 2 {
				continue
			}

			left := strings.TrimSpace(parts[0])
			right := strings.TrimSpace(parts[1])

			return p.compareValues(left, right, op)
		}
	}

	return false, fmt.Errorf("无法解析比较表达式: %s", expr)
}

// compareValues 比较两个值
func (p *ExpressionParser) compareValues(left, right, operator string) (bool, error) {
	// 处理null值
	if left == "null" || right == "null" {
		switch operator {
		case " == ":
			return left == right, nil
		case " != ":
			return left != right, nil
		default:
			return false, nil
		}
	}

	// 尝试作为数字比较
	leftNum, leftErr := p.parseNumber(left)
	rightNum, rightErr := p.parseNumber(right)

	if leftErr == nil && rightErr == nil {
		switch operator {
		case " == ":
			return leftNum == rightNum, nil
		case " != ":
			return leftNum != rightNum, nil
		case " > ":
			return leftNum > rightNum, nil
		case " < ":
			return leftNum < rightNum, nil
		case " >= ":
			return leftNum >= rightNum, nil
		case " <= ":
			return leftNum <= rightNum, nil
		}
	}

	// 作为字符串比较
	leftStr := strings.Trim(left, `"`)
	rightStr := strings.Trim(right, `"`)

	switch operator {
	case " == ":
		return leftStr == rightStr, nil
	case " != ":
		return leftStr != rightStr, nil
	default:
		return false, fmt.Errorf("字符串不支持操作符: %s", operator)
	}
}

// evaluateMathExpression 评估数学表达式
func (p *ExpressionParser) evaluateMathExpression(expr string) (float64, error) {
	expr = strings.TrimSpace(expr)

	// 简单的数学表达式解析（可以使用更复杂的解析器）
	return p.parseSimpleMathExpression(expr)
}

// parseSimpleMathExpression 解析简单数学表达式
func (p *ExpressionParser) parseSimpleMathExpression(expr string) (float64, error) {
	// 处理加法
	if strings.Contains(expr, " + ") {
		parts := strings.Split(expr, " + ")
		if len(parts) == 2 {
			left, err1 := p.parseSimpleMathExpression(strings.TrimSpace(parts[0]))
			right, err2 := p.parseSimpleMathExpression(strings.TrimSpace(parts[1]))
			if err1 == nil && err2 == nil {
				return left + right, nil
			}
		}
	}

	// 处理减法
	if strings.Contains(expr, " - ") {
		parts := strings.Split(expr, " - ")
		if len(parts) == 2 {
			left, err1 := p.parseSimpleMathExpression(strings.TrimSpace(parts[0]))
			right, err2 := p.parseSimpleMathExpression(strings.TrimSpace(parts[1]))
			if err1 == nil && err2 == nil {
				return left - right, nil
			}
		}
	}

	// 处理乘法
	if strings.Contains(expr, " * ") {
		parts := strings.Split(expr, " * ")
		if len(parts) == 2 {
			left, err1 := p.parseSimpleMathExpression(strings.TrimSpace(parts[0]))
			right, err2 := p.parseSimpleMathExpression(strings.TrimSpace(parts[1]))
			if err1 == nil && err2 == nil {
				return left * right, nil
			}
		}
	}

	// 处理除法
	if strings.Contains(expr, " / ") {
		parts := strings.Split(expr, " / ")
		if len(parts) == 2 {
			left, err1 := p.parseSimpleMathExpression(strings.TrimSpace(parts[0]))
			right, err2 := p.parseSimpleMathExpression(strings.TrimSpace(parts[1]))
			if err1 == nil && err2 == nil {
				if right == 0 {
					return 0, fmt.Errorf("除零错误")
				}
				return left / right, nil
			}
		}
	}

	// 处理幂运算
	if strings.Contains(expr, " ^ ") {
		parts := strings.Split(expr, " ^ ")
		if len(parts) == 2 {
			left, err1 := p.parseSimpleMathExpression(strings.TrimSpace(parts[0]))
			right, err2 := p.parseSimpleMathExpression(strings.TrimSpace(parts[1]))
			if err1 == nil && err2 == nil {
				return math.Pow(left, right), nil
			}
		}
	}

	// 处理括号
	if strings.HasPrefix(expr, "(") && strings.HasSuffix(expr, ")") {
		return p.parseSimpleMathExpression(expr[1 : len(expr)-1])
	}

	// 尝试解析为数字
	return p.parseNumber(expr)
}
