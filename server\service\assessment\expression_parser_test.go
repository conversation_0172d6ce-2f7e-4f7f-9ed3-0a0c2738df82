package assessment

import (
	"testing"
)

func TestExpressionParser_EvaluateFormula(t *testing.T) {
	tests := []struct {
		name      string
		formula   string
		variables map[string]interface{}
		expected  float64
		wantErr   bool
	}{
		{
			name:    "简单加法",
			formula: "a + b",
			variables: map[string]interface{}{
				"a": 10.0,
				"b": 20.0,
			},
			expected: 30.0,
			wantErr:  false,
		},
		{
			name:    "SUM函数",
			formula: "SUM(scores)",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: 263.0,
			wantErr:  false,
		},
		{
			name:    "AVG函数",
			formula: "AVG(scores)",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: 87.66666666666667,
			wantErr:  false,
		},
		{
			name:    "MAX函数",
			formula: "MAX(scores)",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: 90.0,
			wantErr:  false,
		},
		{
			name:    "MIN函数",
			formula: "MIN(scores)",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: 85.0,
			wantErr:  false,
		},
		{
			name:    "COUNT函数",
			formula: "COUNT(scores)",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: 3.0,
			wantErr:  false,
		},
		{
			name:    "ROUND函数",
			formula: "ROUND(value, 2)",
			variables: map[string]interface{}{
				"value": 87.66666666666667,
			},
			expected: 87.67,
			wantErr:  false,
		},
		{
			name:    "复杂公式",
			formula: "AVG(projectScores) * 0.4 + AVG(departmentScores) * 0.6",
			variables: map[string]interface{}{
				"projectScores":    []interface{}{85.0, 90.0, 88.0},
				"departmentScores": []interface{}{92.0, 87.0},
			},
			expected: 88.6, // (87.67 * 0.4) + (89.5 * 0.6) = 35.07 + 53.7 = 88.77
			wantErr:  false,
		},
		{
			name:    "IF函数",
			formula: "IF(score >= 90, score * 1.2, score)",
			variables: map[string]interface{}{
				"score": 95.0,
			},
			expected: 114.0,
			wantErr:  false,
		},
		{
			name:    "数学函数组合",
			formula: "SQRT(POW(a, 2) + POW(b, 2))",
			variables: map[string]interface{}{
				"a": 3.0,
				"b": 4.0,
			},
			expected: 5.0,
			wantErr:  false,
		},
		{
			name:    "CALCULATE_PROJECT_AVG函数",
			formula: "CALCULATE_PROJECT_AVG(project_data, department_score)",
			variables: map[string]interface{}{
				"department_score": 90.0,
				"project_data": []interface{}{
					map[string]interface{}{
						"project_manager_score":     85.0,
						"project_participation":     0.8,
						"is_leader_of_this_project": false,
					},
					map[string]interface{}{
						"project_manager_score":     88.0,
						"project_participation":     0.6,
						"is_leader_of_this_project": true,
					},
					map[string]interface{}{
						"project_manager_score":     92.0,
						"project_participation":     0.9,
						"is_leader_of_this_project": false,
					},
				},
			},
			expected: 204.8, // (85*0.8 + 90*0.6 + 92*0.9) = (68 + 54 + 82.8) = 204.8
			wantErr:  false,
		},
		{
			name:    "完整的项目评分公式",
			formula: "department_manager_score * 0.6 + CALCULATE_PROJECT_AVG(project_data, department_manager_score) * 0.4",
			variables: map[string]interface{}{
				"department_manager_score": 90.0,
				"project_data": []interface{}{
					map[string]interface{}{
						"project_manager_score":     85.0,
						"project_participation":     0.8,
						"is_leader_of_this_project": false,
					},
					map[string]interface{}{
						"project_manager_score":     88.0,
						"project_participation":     0.6,
						"is_leader_of_this_project": true,
					},
				},
			},
			expected: 102.8, // 90 * 0.6 + (85*0.8 + 90*0.6) * 0.4 = 54 + 122 * 0.4 = 54 + 48.8 = 102.8
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parser := NewExpressionParser(tt.variables)
			result, err := parser.EvaluateFormula(tt.formula)

			if (err != nil) != tt.wantErr {
				t.Errorf("EvaluateFormula() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// 允许小的浮点数误差
				if abs(result-tt.expected) > 0.01 {
					t.Errorf("EvaluateFormula() = %v, want %v", result, tt.expected)
				}
			}
		})
	}
}

func TestExpressionParser_EvaluateCondition(t *testing.T) {
	tests := []struct {
		name      string
		condition string
		variables map[string]interface{}
		expected  bool
		wantErr   bool
	}{
		{
			name:      "简单比较",
			condition: "score > 80",
			variables: map[string]interface{}{
				"score": 85.0,
			},
			expected: true,
			wantErr:  false,
		},
		{
			name:      "逻辑AND",
			condition: "projectScore > 80 && departmentScore > 80",
			variables: map[string]interface{}{
				"projectScore":    85.0,
				"departmentScore": 90.0,
			},
			expected: true,
			wantErr:  false,
		},
		{
			name:      "逻辑OR",
			condition: "projectScore > 90 || departmentScore > 90",
			variables: map[string]interface{}{
				"projectScore":    85.0,
				"departmentScore": 95.0,
			},
			expected: true,
			wantErr:  false,
		},
		{
			name:      "NULL检查",
			condition: "projectScore != null && departmentScore != null",
			variables: map[string]interface{}{
				"projectScore":    85.0,
				"departmentScore": nil,
			},
			expected: false,
			wantErr:  false,
		},
		{
			name:      "函数条件",
			condition: "AVG(scores) > 85",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: true,
			wantErr:  false,
		},
		{
			name:      "COUNT函数条件",
			condition: "COUNT(scores) >= 3",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: true,
			wantErr:  false,
		},
		{
			name:      "MAX函数条件",
			condition: "MAX(scores) >= 95",
			variables: map[string]interface{}{
				"scores": []interface{}{85.0, 90.0, 88.0},
			},
			expected: false,
			wantErr:  false,
		},
		{
			name:      "COUNT函数条件",
			condition: "COUNT(project_data) > 0",
			variables: map[string]interface{}{
				"project_data": []interface{}{
					map[string]interface{}{
						"project_manager_score":     85.0,
						"project_participation":     0.8,
						"is_leader_of_this_project": false,
					},
				},
			},
			expected: true,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parser := NewExpressionParser(tt.variables)
			result, err := parser.EvaluateCondition(tt.condition)

			if (err != nil) != tt.wantErr {
				t.Errorf("EvaluateCondition() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && result != tt.expected {
				t.Errorf("EvaluateCondition() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// abs 计算绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
