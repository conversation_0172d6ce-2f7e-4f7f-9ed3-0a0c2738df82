
package assessment

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type MethodParameterRelationsService struct {}
// CreateMethodParameterRelations 创建关联关系记录
// Author [yourname](https://github.com/yourname)
func (methodParameterRelationsService *MethodParameterRelationsService) CreateMethodParameterRelations(ctx context.Context, methodParameterRelations *assessment.MethodParameterRelations) (err error) {
	err = global.GVA_DB.Create(methodParameterRelations).Error
	return err
}

// DeleteMethodParameterRelations 删除关联关系记录
// Author [yourname](https://github.com/yourname)
func (methodParameterRelationsService *MethodParameterRelationsService)DeleteMethodParameterRelations(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.MethodParameterRelations{},"id = ?",id).Error
	return err
}

// DeleteMethodParameterRelationsByIds 批量删除关联关系记录
// Author [yourname](https://github.com/yourname)
func (methodParameterRelationsService *MethodParameterRelationsService)DeleteMethodParameterRelationsByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.MethodParameterRelations{},"id in ?",ids).Error
	return err
}

// UpdateMethodParameterRelations 更新关联关系记录
// Author [yourname](https://github.com/yourname)
func (methodParameterRelationsService *MethodParameterRelationsService)UpdateMethodParameterRelations(ctx context.Context, methodParameterRelations assessment.MethodParameterRelations) (err error) {
	err = global.GVA_DB.Model(&assessment.MethodParameterRelations{}).Where("id = ?",methodParameterRelations.Id).Updates(&methodParameterRelations).Error
	return err
}

// GetMethodParameterRelations 根据id获取关联关系记录
// Author [yourname](https://github.com/yourname)
func (methodParameterRelationsService *MethodParameterRelationsService)GetMethodParameterRelations(ctx context.Context, id string) (methodParameterRelations assessment.MethodParameterRelations, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&methodParameterRelations).Error
	return
}
// GetMethodParameterRelationsInfoList 分页获取关联关系记录
// Author [yourname](https://github.com/yourname)
func (methodParameterRelationsService *MethodParameterRelationsService)GetMethodParameterRelationsInfoList(ctx context.Context, info assessmentReq.MethodParameterRelationsSearch) (list []assessment.MethodParameterRelations, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&assessment.MethodParameterRelations{})
    var methodParameterRelationss []assessment.MethodParameterRelations
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&methodParameterRelationss).Error
	return  methodParameterRelationss, total, err
}
func (methodParameterRelationsService *MethodParameterRelationsService)GetMethodParameterRelationsPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
