
package assessment

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type MethodUserAssignmentsService struct {}
// CreateMethodUserAssignments 创建方法用户关联记录
// Author [yourname](https://github.com/yourname)
func (methodUserAssignmentsService *MethodUserAssignmentsService) CreateMethodUserAssignments(ctx context.Context, methodUserAssignments *assessment.MethodUserAssignments) (err error) {
	err = global.GVA_DB.Create(methodUserAssignments).Error
	return err
}

// DeleteMethodUserAssignments 删除方法用户关联记录
// Author [yourname](https://github.com/yourname)
func (methodUserAssignmentsService *MethodUserAssignmentsService)DeleteMethodUserAssignments(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.MethodUserAssignments{},"id = ?",id).Error
	return err
}

// DeleteMethodUserAssignmentsByIds 批量删除方法用户关联记录
// Author [yourname](https://github.com/yourname)
func (methodUserAssignmentsService *MethodUserAssignmentsService)DeleteMethodUserAssignmentsByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.MethodUserAssignments{},"id in ?",ids).Error
	return err
}

// UpdateMethodUserAssignments 更新方法用户关联记录
// Author [yourname](https://github.com/yourname)
func (methodUserAssignmentsService *MethodUserAssignmentsService)UpdateMethodUserAssignments(ctx context.Context, methodUserAssignments assessment.MethodUserAssignments) (err error) {
	err = global.GVA_DB.Model(&assessment.MethodUserAssignments{}).Where("id = ?",methodUserAssignments.Id).Updates(&methodUserAssignments).Error
	return err
}

// GetMethodUserAssignments 根据id获取方法用户关联记录
// Author [yourname](https://github.com/yourname)
func (methodUserAssignmentsService *MethodUserAssignmentsService)GetMethodUserAssignments(ctx context.Context, id string) (methodUserAssignments assessment.MethodUserAssignments, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&methodUserAssignments).Error
	return
}
// GetMethodUserAssignmentsInfoList 分页获取方法用户关联记录
// Author [yourname](https://github.com/yourname)
func (methodUserAssignmentsService *MethodUserAssignmentsService)GetMethodUserAssignmentsInfoList(ctx context.Context, info assessmentReq.MethodUserAssignmentsSearch) (list []assessment.MethodUserAssignments, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&assessment.MethodUserAssignments{})
    var methodUserAssignmentss []assessment.MethodUserAssignments
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&methodUserAssignmentss).Error
	return  methodUserAssignmentss, total, err
}
func (methodUserAssignmentsService *MethodUserAssignmentsService)GetMethodUserAssignmentsPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
