package assessment

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type ProjectManagerScoreService struct{}

// CreateProjectManagerScore 创建项目负责人评分记录
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) CreateProjectManagerScore(ctx context.Context, projectManagerScore *assessment.ProjectManagerScore) (err error) {
	err = global.GVA_DB.Create(projectManagerScore).Error
	return err
}

// DeleteProjectManagerScore 删除项目负责人评分记录
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) DeleteProjectManagerScore(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.ProjectManagerScore{}, "id = ?", id).Error
	return err
}

// DeleteProjectManagerScoreByIds 批量删除项目负责人评分记录
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) DeleteProjectManagerScoreByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.ProjectManagerScore{}, "id in ?", ids).Error
	return err
}

// UpdateProjectManagerScore 更新项目负责人评分记录
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) UpdateProjectManagerScore(ctx context.Context, projectManagerScore assessment.ProjectManagerScore) (err error) {
	err = global.GVA_DB.Model(&assessment.ProjectManagerScore{}).Where("id = ?", projectManagerScore.Id).Updates(&projectManagerScore).Error
	return err
}

// GetProjectManagerScore 根据id获取项目负责人评分记录
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) GetProjectManagerScore(ctx context.Context, id string) (projectManagerScore assessment.ProjectManagerScore, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&projectManagerScore).Error
	return
}

// GetProjectManagerScoreInfoList 分页获取项目负责人评分记录
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) GetProjectManagerScoreInfoList(ctx context.Context, info assessmentReq.ProjectManagerScoreSearch) (list []assessment.ProjectManagerScore, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&assessment.ProjectManagerScore{})
	var projectManagerScores []assessment.ProjectManagerScore
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&projectManagerScores).Error
	return projectManagerScores, total, err
}
func (projectManagerScoreService *ProjectManagerScoreService) GetProjectManagerScorePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// BatchSubmitProjectMemberScores 批量提交项目成员评分
// Author [yourname](https://github.com/yourname)
func (projectManagerScoreService *ProjectManagerScoreService) BatchSubmitProjectMemberScores(ctx context.Context, scoreRequests []assessmentReq.BatchSubmitProjectMemberScoresRequest) error {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, scoreReq := range scoreRequests {
		// 检查是否已存在该考核系数分配ID的评分记录
		var existingScore assessment.ProjectManagerScore
		err := tx.Where("coefficient_allocation_id = ?", scoreReq.CoefficientAllocationId).First(&existingScore).Error

		if err == nil {
			// 记录已存在，更新评分
			existingScore.ManagerScore = &scoreReq.ManagerScore
			existingScore.ScorerUsername = &scoreReq.ScorerUsername
			if scoreReq.CalculationParameter != "" {
				existingScore.CalculationParameter = &scoreReq.CalculationParameter
			}

			err = tx.Save(&existingScore).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		} else {
			// 记录不存在，创建新记录
			newScore := assessment.ProjectManagerScore{
				CoefficientAllocationId: &scoreReq.CoefficientAllocationId,
				ManagerScore:            &scoreReq.ManagerScore,
				ScorerUsername:          &scoreReq.ScorerUsername,
			}

			if scoreReq.CalculationParameter != "" {
				newScore.CalculationParameter = &scoreReq.CalculationParameter
			}

			err = tx.Create(&newScore).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}
