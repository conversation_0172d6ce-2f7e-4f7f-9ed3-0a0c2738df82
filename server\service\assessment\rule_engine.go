package assessment

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// RuleEngineService 规则引擎服务
type RuleEngineService struct{}

// RuleConfig 规则配置结构
type RuleConfig struct {
	RuleType        string            `json:"ruleType"`
	Version         string            `json:"version"`
	Metadata        RuleMetadata      `json:"metadata"`
	InputParameters []InputParameter  `json:"inputParameters"`
	Conditions      []RuleCondition   `json:"conditions"`
	DefaultValue    float64           `json:"defaultValue"`
}

// RuleMetadata 规则元数据
type RuleMetadata struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// InputParameter 输入参数
type InputParameter struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"`
	Required     bool        `json:"required"`
	DefaultValue interface{} `json:"defaultValue"`
	Description  string      `json:"description"`
}

// RuleCondition 规则条件
type RuleCondition struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	When        string      `json:"when"`
	Then        RuleAction  `json:"then"`
	Priority    int         `json:"priority"`
	Description string      `json:"description"`
}

// RuleAction 规则动作
type RuleAction struct {
	Formula string `json:"formula"`
}

// CalculateByRuleRequest 规则计算请求
type CalculateByRuleRequest struct {
	MethodID   int                    `json:"methodId"`
	Parameters map[string]interface{} `json:"parameters"`
}

// CalculateByRuleResponse 规则计算响应
type CalculateByRuleResponse struct {
	Success          bool                   `json:"success"`
	Result           float64                `json:"result"`
	AppliedCondition string                 `json:"appliedCondition"`
	ExecutionTime    int64                  `json:"executionTime"`
	Trace            []ExecutionStep        `json:"trace"`
	Error            string                 `json:"error,omitempty"`
}

// ExecutionStep 执行步骤
type ExecutionStep struct {
	Step    string      `json:"step"`
	Detail  string      `json:"detail"`
	Result  interface{} `json:"result"`
	Success bool        `json:"success"`
}

// TestRuleRequest 规则测试请求
type TestRuleRequest struct {
	RuleConfig RuleConfig             `json:"ruleConfig"`
	TestData   map[string]interface{} `json:"testData"`
}

// CalculateByRule 根据规则计算
func (s *RuleEngineService) CalculateByRule(ctx context.Context, req CalculateByRuleRequest) (*CalculateByRuleResponse, error) {
	startTime := time.Now()
	trace := []ExecutionStep{}

	// 1. 获取计算方法配置
	methodService := CalculationMethodsService{}
	method, err := methodService.GetCalculationMethodByID(ctx, req.MethodID)
	if err != nil {
		return &CalculateByRuleResponse{
			Success: false,
			Error:   fmt.Sprintf("获取计算方法失败: %v", err),
		}, nil
	}

	trace = append(trace, ExecutionStep{
		Step:    "获取计算方法",
		Detail:  fmt.Sprintf("方法名称: %s", *method.MethodName),
		Success: true,
	})

	// 2. 解析规则配置
	var ruleConfig RuleConfig
	if method.RuleType != nil && *method.RuleType == "rule" {
		err = json.Unmarshal([]byte(*method.Formula), &ruleConfig)
		if err != nil {
			return &CalculateByRuleResponse{
				Success: false,
				Error:   fmt.Sprintf("解析规则配置失败: %v", err),
			}, nil
		}
	} else {
		// 兼容传统公式
		return s.calculateByFormula(ctx, *method.Formula, req.Parameters, trace, startTime)
	}

	trace = append(trace, ExecutionStep{
		Step:    "解析规则配置",
		Detail:  fmt.Sprintf("规则类型: %s, 条件数量: %d", ruleConfig.RuleType, len(ruleConfig.Conditions)),
		Success: true,
	})

	// 3. 执行规则计算
	result, appliedCondition, newTrace, err := s.executeRuleEngine(ctx, &ruleConfig, req.Parameters)
	trace = append(trace, newTrace...)

	if err != nil {
		return &CalculateByRuleResponse{
			Success: false,
			Error:   err.Error(),
			Trace:   trace,
		}, nil
	}

	executionTime := time.Since(startTime).Milliseconds()

	return &CalculateByRuleResponse{
		Success:          true,
		Result:           result,
		AppliedCondition: appliedCondition,
		ExecutionTime:    executionTime,
		Trace:            trace,
	}, nil
}

// TestRule 测试规则
func (s *RuleEngineService) TestRule(ctx context.Context, req TestRuleRequest) (*CalculateByRuleResponse, error) {
	startTime := time.Now()
	trace := []ExecutionStep{}

	trace = append(trace, ExecutionStep{
		Step:    "开始规则测试",
		Detail:  fmt.Sprintf("条件数量: %d", len(req.RuleConfig.Conditions)),
		Success: true,
	})

	// 执行规则计算
	result, appliedCondition, newTrace, err := s.executeRuleEngine(ctx, &req.RuleConfig, req.TestData)
	trace = append(trace, newTrace...)

	if err != nil {
		return &CalculateByRuleResponse{
			Success: false,
			Error:   err.Error(),
			Trace:   trace,
		}, nil
	}

	executionTime := time.Since(startTime).Milliseconds()

	return &CalculateByRuleResponse{
		Success:          true,
		Result:           result,
		AppliedCondition: appliedCondition,
		ExecutionTime:    executionTime,
		Trace:            trace,
	}, nil
}

// executeRuleEngine 执行规则引擎
func (s *RuleEngineService) executeRuleEngine(ctx context.Context, ruleConfig *RuleConfig, parameters map[string]interface{}) (float64, string, []ExecutionStep, error) {
	trace := []ExecutionStep{}

	// 1. 参数验证
	trace = append(trace, ExecutionStep{
		Step:    "参数验证",
		Detail:  fmt.Sprintf("接收到 %d 个参数", len(parameters)),
		Success: true,
	})

	// 2. 按优先级排序条件
	conditions := make([]RuleCondition, len(ruleConfig.Conditions))
	copy(conditions, ruleConfig.Conditions)

	// 简单排序（按优先级降序）
	for i := 0; i < len(conditions)-1; i++ {
		for j := i + 1; j < len(conditions); j++ {
			if conditions[i].Priority < conditions[j].Priority {
				conditions[i], conditions[j] = conditions[j], conditions[i]
			}
		}
	}

	// 3. 条件匹配
	for _, condition := range conditions {
		trace = append(trace, ExecutionStep{
			Step:   fmt.Sprintf("条件评估: %s", condition.Name),
			Detail: fmt.Sprintf("条件表达式: %s", condition.When),
		})

		matched, err := s.evaluateCondition(condition.When, parameters)
		if err != nil {
			trace[len(trace)-1].Success = false
			trace[len(trace)-1].Result = fmt.Sprintf("评估失败: %v", err)
			continue
		}

		trace[len(trace)-1].Success = true
		trace[len(trace)-1].Result = matched

		if matched {
			// 计算公式
			result, err := s.evaluateFormula(condition.Then.Formula, parameters)
			if err != nil {
				return 0, "", trace, fmt.Errorf("公式计算失败: %v", err)
			}

			trace = append(trace, ExecutionStep{
				Step:    "公式计算",
				Detail:  fmt.Sprintf("公式: %s", condition.Then.Formula),
				Result:  result,
				Success: true,
			})

			return result, condition.Name, trace, nil
		}
	}

	// 4. 使用默认值
	trace = append(trace, ExecutionStep{
		Step:    "使用默认值",
		Detail:  "没有匹配的条件，使用默认值",
		Result:  ruleConfig.DefaultValue,
		Success: true,
	})

	return ruleConfig.DefaultValue, "默认值", trace, nil
}

// calculateByFormula 传统公式计算（兼容性）
func (s *RuleEngineService) calculateByFormula(ctx context.Context, formula string, parameters map[string]interface{}, trace []ExecutionStep, startTime time.Time) (*CalculateByRuleResponse, error) {
	result, err := s.evaluateFormula(formula, parameters)
	if err != nil {
		return &CalculateByRuleResponse{
			Success: false,
			Error:   fmt.Sprintf("公式计算失败: %v", err),
		}, nil
	}

	trace = append(trace, ExecutionStep{
		Step:    "传统公式计算",
		Detail:  fmt.Sprintf("公式: %s", formula),
		Result:  result,
		Success: true,
	})

	executionTime := time.Since(startTime).Milliseconds()

	return &CalculateByRuleResponse{
		Success:          true,
		Result:           result,
		AppliedCondition: "传统公式",
		ExecutionTime:    executionTime,
		Trace:            trace,
	}, nil
}

// evaluateCondition 评估条件表达式
func (s *RuleEngineService) evaluateCondition(condition string, parameters map[string]interface{}) (bool, error) {
	parser := NewExpressionParser(parameters)
	return parser.EvaluateCondition(condition)
}

// evaluateFormula 评估计算公式
func (s *RuleEngineService) evaluateFormula(formula string, parameters map[string]interface{}) (float64, error) {
	parser := NewExpressionParser(parameters)
	return parser.EvaluateFormula(formula)
}


