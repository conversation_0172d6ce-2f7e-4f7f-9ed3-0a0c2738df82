
package assessment

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
    assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
)

type ScoreQuotaManagementService struct {}
// CreateScoreQuotaManagement 创建高分配额管理记录
// Author [yourname](https://github.com/yourname)
func (scoreQuotaManagementService *ScoreQuotaManagementService) CreateScoreQuotaManagement(ctx context.Context, scoreQuotaManagement *assessment.ScoreQuotaManagement) (err error) {
	err = global.GVA_DB.Create(scoreQuotaManagement).Error
	return err
}

// DeleteScoreQuotaManagement 删除高分配额管理记录
// Author [yourname](https://github.com/yourname)
func (scoreQuotaManagementService *ScoreQuotaManagementService)DeleteScoreQuotaManagement(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&assessment.ScoreQuotaManagement{},"id = ?",id).Error
	return err
}

// DeleteScoreQuotaManagementByIds 批量删除高分配额管理记录
// Author [yourname](https://github.com/yourname)
func (scoreQuotaManagementService *ScoreQuotaManagementService)DeleteScoreQuotaManagementByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]assessment.ScoreQuotaManagement{},"id in ?",ids).Error
	return err
}

// UpdateScoreQuotaManagement 更新高分配额管理记录
// Author [yourname](https://github.com/yourname)
func (scoreQuotaManagementService *ScoreQuotaManagementService)UpdateScoreQuotaManagement(ctx context.Context, scoreQuotaManagement assessment.ScoreQuotaManagement) (err error) {
	err = global.GVA_DB.Model(&assessment.ScoreQuotaManagement{}).Where("id = ?",scoreQuotaManagement.Id).Updates(&scoreQuotaManagement).Error
	return err
}

// GetScoreQuotaManagement 根据id获取高分配额管理记录
// Author [yourname](https://github.com/yourname)
func (scoreQuotaManagementService *ScoreQuotaManagementService)GetScoreQuotaManagement(ctx context.Context, id string) (scoreQuotaManagement assessment.ScoreQuotaManagement, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&scoreQuotaManagement).Error
	return
}
// GetScoreQuotaManagementInfoList 分页获取高分配额管理记录
// Author [yourname](https://github.com/yourname)
func (scoreQuotaManagementService *ScoreQuotaManagementService)GetScoreQuotaManagementInfoList(ctx context.Context, info assessmentReq.ScoreQuotaManagementSearch) (list []assessment.ScoreQuotaManagement, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&assessment.ScoreQuotaManagement{})
    var scoreQuotaManagements []assessment.ScoreQuotaManagement
    // 如果有条件搜索 下方会自动创建搜索语句
    
    if info.QuotaName != nil && *info.QuotaName != "" {
        db = db.Where("quota_name LIKE ?", "%"+ *info.QuotaName+"%")
    }
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&scoreQuotaManagements).Error
	return  scoreQuotaManagements, total, err
}
func (scoreQuotaManagementService *ScoreQuotaManagementService)GetScoreQuotaManagementPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
