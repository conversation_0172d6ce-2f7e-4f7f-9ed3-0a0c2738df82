package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/assessment"
	"github.com/flipped-aurora/gin-vue-admin/server/service/example"
	"github.com/flipped-aurora/gin-vue-admin/server/service/project"
	"github.com/flipped-aurora/gin-vue-admin/server/service/score"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
)

var ServiceGroupApp = new(ServiceGroup)

type ServiceGroup struct {
	SystemServiceGroup     system.ServiceGroup
	ExampleServiceGroup    example.ServiceGroup
	ProjectServiceGroup    project.ServiceGroup
	AssessmentServiceGroup assessment.ServiceGroup
	ScoreServiceGroup      score.ServiceGroup
}
