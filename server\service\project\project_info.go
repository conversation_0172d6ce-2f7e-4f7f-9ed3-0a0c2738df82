package project

import (
	"context"
	"encoding/json"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/project"
	projectReq "github.com/flipped-aurora/gin-vue-admin/server/model/project/request"
)

type ProjectInfoService struct{}

// CreateProjectInfo 创建项目信息表记录
// Author [yourname](https://github.com/yourname)
func (projectInfoService *ProjectInfoService) CreateProjectInfo(ctx context.Context, projectInfo *project.ProjectInfo) (err error) {
	err = global.GVA_DB.Create(projectInfo).Error
	return err
}

// DeleteProjectInfo 删除项目信息表记录
// Author [yourname](https://github.com/yourname)
func (projectInfoService *ProjectInfoService) DeleteProjectInfo(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&project.ProjectInfo{}, "id = ?", id).Error
	return err
}

// DeleteProjectInfoByIds 批量删除项目信息表记录
// Author [yourname](https://github.com/yourname)
func (projectInfoService *ProjectInfoService) DeleteProjectInfoByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]project.ProjectInfo{}, "id in ?", ids).Error
	return err
}

// UpdateProjectInfo 更新项目信息表记录
// Author [yourname](https://github.com/yourname)
func (projectInfoService *ProjectInfoService) UpdateProjectInfo(ctx context.Context, projectInfo project.ProjectInfo) (err error) {
	err = global.GVA_DB.Model(&project.ProjectInfo{}).Where("id = ?", projectInfo.Id).Updates(&projectInfo).Error
	return err
}

// GetProjectInfo 根据id获取项目信息表记录
// Author [yourname](https://github.com/yourname)
func (projectInfoService *ProjectInfoService) GetProjectInfo(ctx context.Context, id string) (projectInfoResponse project.ProjectInfoResponse, err error) {
	var projectInfo project.ProjectInfo
	err = global.GVA_DB.Where("id = ?", id).First(&projectInfo).Error
	if err != nil {
		return
	}

	// 映射数据，添加用户昵称和部门名称
	projectInfoResponse = projectInfoService.mapProjectInfoToResponse(ctx, projectInfo)
	return
}

// GetProjectInfoInfoList 分页获取项目信息表记录
// Author [yourname](https://github.com/yourname)
func (projectInfoService *ProjectInfoService) GetProjectInfoInfoList(ctx context.Context, info projectReq.ProjectInfoSearch) (list []project.ProjectInfoResponse, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&project.ProjectInfo{})
	var projectInfos []project.ProjectInfo
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}
	if info.DepartmentId != nil {
		db = db.Where("department_id = ?", *info.DepartmentId)
	}
	if info.ManagerId != nil && *info.ManagerId != "" {
		db = db.Where("manager_id = ?", *info.ManagerId)
	}
	if info.Type != nil && *info.Type != "" {
		db = db.Where("type = ?", *info.Type)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&projectInfos).Error
	if err != nil {
		return
	}

	// 映射数据，添加用户昵称和部门名称
	list = make([]project.ProjectInfoResponse, len(projectInfos))
	for i, projectInfo := range projectInfos {
		list[i] = projectInfoService.mapProjectInfoToResponse(ctx, projectInfo)
	}

	return list, total, err
}
func (projectInfoService *ProjectInfoService) GetProjectInfoPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// mapProjectInfoToResponse 将ProjectInfo映射为ProjectInfoResponse，包含用户昵称和部门名称
func (projectInfoService *ProjectInfoService) mapProjectInfoToResponse(ctx context.Context, projectInfo project.ProjectInfo) project.ProjectInfoResponse {
	response := project.ProjectInfoResponse{
		ProjectInfo: projectInfo,
	}

	// 映射部门名称
	if projectInfo.DepartmentId != nil {
		var org struct {
			Name *string `json:"name"`
		}
		err := global.GVA_DB.Table("org_org").Where("id = ?", *projectInfo.DepartmentId).First(&org).Error
		if err == nil && org.Name != nil {
			response.DepartmentName = *org.Name
		} else {
			response.DepartmentName = "未知部门"
		}
	} else {
		response.DepartmentName = "未分配部门"
	}

	// 映射项目负责人姓名
	if projectInfo.ManagerId != nil {
		var user struct {
			NickName string `json:"nickName"`
		}
		err := global.GVA_DB.Table("sys_users").Where("username = ?", *projectInfo.ManagerId).First(&user).Error
		if err == nil {
			response.ManagerName = user.NickName
		} else {
			response.ManagerName = *projectInfo.ManagerId // 如果找不到用户，显示用户名
		}
	}

	// 映射项目成员姓名
	if projectInfo.Members != nil {
		var memberUsernames []string
		// 使用encoding/json包解析JSON
		if err := json.Unmarshal(projectInfo.Members, &memberUsernames); err == nil {
			var users []struct {
				Username string `json:"username"`
				NickName string `json:"nickName"`
			}
			// 使用ORDER BY FIELD确保返回结果按照memberUsernames的顺序
			orderClause := "FIELD(username"
			for _, username := range memberUsernames {
				orderClause += ", '" + username + "'"
			}
			orderClause += ")"

			err = global.GVA_DB.Table("sys_users").
				Where("username IN ?", memberUsernames).
				Order(orderClause).
				Find(&users).Error
			if err == nil {
				// 创建用户名到昵称的映射
				userMap := make(map[string]string)
				for _, user := range users {
					userMap[user.Username] = user.NickName
				}

				// 按原始顺序映射成员姓名
				response.MemberNames = make([]string, len(memberUsernames))
				for i, username := range memberUsernames {
					if nickName, exists := userMap[username]; exists {
						response.MemberNames[i] = nickName
					} else {
						response.MemberNames[i] = username // 如果找不到用户，显示用户名
					}
				}
			}
		}
	}

	return response
}
