
package score

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/score"
    scoreReq "github.com/flipped-aurora/gin-vue-admin/server/model/score/request"
)

type ProjectAssessmentScoreService struct {}
// CreateProjectAssessmentScore 创建精力分配评分表记录
// Author [yourname](https://github.com/yourname)
func (projectAssessmentScoreService *ProjectAssessmentScoreService) CreateProjectAssessmentScore(ctx context.Context, projectAssessmentScore *score.ProjectAssessmentScore) (err error) {
	err = global.GVA_DB.Create(projectAssessmentScore).Error
	return err
}

// DeleteProjectAssessmentScore 删除精力分配评分表记录
// Author [yourname](https://github.com/yourname)
func (projectAssessmentScoreService *ProjectAssessmentScoreService)DeleteProjectAssessmentScore(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&score.ProjectAssessmentScore{},"id = ?",id).Error
	return err
}

// DeleteProjectAssessmentScoreByIds 批量删除精力分配评分表记录
// Author [yourname](https://github.com/yourname)
func (projectAssessmentScoreService *ProjectAssessmentScoreService)DeleteProjectAssessmentScoreByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]score.ProjectAssessmentScore{},"id in ?",ids).Error
	return err
}

// UpdateProjectAssessmentScore 更新精力分配评分表记录
// Author [yourname](https://github.com/yourname)
func (projectAssessmentScoreService *ProjectAssessmentScoreService)UpdateProjectAssessmentScore(ctx context.Context, projectAssessmentScore score.ProjectAssessmentScore) (err error) {
	err = global.GVA_DB.Model(&score.ProjectAssessmentScore{}).Where("id = ?",projectAssessmentScore.Id).Updates(&projectAssessmentScore).Error
	return err
}

// GetProjectAssessmentScore 根据id获取精力分配评分表记录
// Author [yourname](https://github.com/yourname)
func (projectAssessmentScoreService *ProjectAssessmentScoreService)GetProjectAssessmentScore(ctx context.Context, id string) (projectAssessmentScore score.ProjectAssessmentScore, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&projectAssessmentScore).Error
	return
}
// GetProjectAssessmentScoreInfoList 分页获取精力分配评分表记录
// Author [yourname](https://github.com/yourname)
func (projectAssessmentScoreService *ProjectAssessmentScoreService)GetProjectAssessmentScoreInfoList(ctx context.Context, info scoreReq.ProjectAssessmentScoreSearch) (list []score.ProjectAssessmentScore, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&score.ProjectAssessmentScore{})
    var projectAssessmentScores []score.ProjectAssessmentScore
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&projectAssessmentScores).Error
	return  projectAssessmentScores, total, err
}
func (projectAssessmentScoreService *ProjectAssessmentScoreService)GetProjectAssessmentScorePublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
