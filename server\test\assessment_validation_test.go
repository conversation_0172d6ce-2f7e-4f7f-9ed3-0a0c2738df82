package test

import (
	"context"
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/stretchr/testify/assert"
)

// TestUserParameterScoresValidation 测试用户参数评分校验功能
func TestUserParameterScoresValidation(t *testing.T) {
	assessmentService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService

	// 测试用例1: 用户缺少考核系数分配
	t.Run("用户缺少考核系数分配", func(t *testing.T) {
		req := request.GetUserParameterScoresRequest{
			UserNames:           []string{"test_user_no_coefficient"},
			AssessmentConfigIds: []int{1},
			ParameterNames:      []string{},
		}

		_, err := assessmentService.GetUserParameterScores(context.Background(), req)
		
		// 应该返回错误
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "该名用户需要考核系数分配，请先进行分配")
	})

	// 测试用例2: 项目负责人未评分
	t.Run("项目负责人未评分", func(t *testing.T) {
		req := request.GetUserParameterScoresRequest{
			UserNames:           []string{"test_user_no_manager_score"},
			AssessmentConfigIds: []int{1},
			ParameterNames:      []string{},
		}

		_, err := assessmentService.GetUserParameterScores(context.Background(), req)
		
		// 应该返回错误，包含项目名称和负责人信息
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "项目未评分-项目负责人")
	})

	// 测试用例3: 部门经理未评分
	t.Run("部门经理未评分", func(t *testing.T) {
		req := request.GetUserParameterScoresRequest{
			UserNames:           []string{"test_user_no_dept_score"},
			AssessmentConfigIds: []int{1},
			ParameterNames:      []string{},
		}

		_, err := assessmentService.GetUserParameterScores(context.Background(), req)
		
		// 应该返回错误
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "机构负责人暂无评分")
	})

	// 测试用例4: 校验通过
	t.Run("校验通过", func(t *testing.T) {
		req := request.GetUserParameterScoresRequest{
			UserNames:           []string{"test_user_complete"},
			AssessmentConfigIds: []int{1},
			ParameterNames:      []string{},
		}

		result, err := assessmentService.GetUserParameterScores(context.Background(), req)
		
		// 应该成功返回数据
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result.Users, 1)
	})

	// 测试用例5: 不触发校验（用户计算参数不包含必要参数）
	t.Run("不触发校验", func(t *testing.T) {
		req := request.GetUserParameterScoresRequest{
			UserNames:           []string{"test_user_no_validation"},
			AssessmentConfigIds: []int{1},
			ParameterNames:      []string{},
		}

		result, err := assessmentService.GetUserParameterScores(context.Background(), req)
		
		// 应该成功返回数据，不进行校验
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})
}

// TestProjectInfoAndManager 测试项目信息和负责人获取功能
func TestProjectInfoAndManager(t *testing.T) {
	assessmentService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService

	t.Run("获取项目信息和负责人", func(t *testing.T) {
		// 测试获取项目信息
		projectName, managerName, err := assessmentService.getProjectInfoAndManager(context.Background(), 1)
		
		// 应该成功获取项目信息
		assert.NoError(t, err)
		assert.NotEmpty(t, projectName)
		assert.NotEmpty(t, managerName)
	})

	t.Run("项目不存在", func(t *testing.T) {
		// 测试不存在的项目
		_, _, err := assessmentService.getProjectInfoAndManager(context.Background(), 99999)
		
		// 应该返回错误
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "项目不存在")
	})
}

// TestValidateUserScoreCompleteness 测试用户评分完整性校验
func TestValidateUserScoreCompleteness(t *testing.T) {
	assessmentService := service.ServiceGroupApp.AssessmentServiceGroup.AssessmentDataService

	t.Run("校验用户评分完整性", func(t *testing.T) {
		// 测试有完整评分的用户
		err := assessmentService.validateUserScoreCompleteness(context.Background(), "test_user", 1, true)
		
		// 根据测试数据的情况，这里可能成功或失败
		// 具体的断言需要根据实际的测试数据来调整
		if err != nil {
			t.Logf("校验失败: %v", err)
		} else {
			t.Log("校验通过")
		}
	})
}
