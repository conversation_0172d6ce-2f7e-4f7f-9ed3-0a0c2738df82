# 基于标签页的考核配置ID修复

## 问题描述

之前的提交逻辑存在问题：
- 无论用户在哪个标签页操作，都使用最新的考核配置ID进行提交
- 这导致用户在历史考核配置标签页中的操作，数据却被提交到最新的考核配置中

## 修复方案

### 1. 标签页结构分析

每个标签页都包含对应的考核配置信息：
```javascript
{
  title: "2025月度考核",           // 显示名称
  name: "assessment_11",          // 标签页唯一标识
  type: "assessment",             // 标签页类型
  configId: 11,                   // 对应的考核配置ID ⭐
  config: { /* 完整的考核配置对象 */ }
}
```

### 2. 修改前的逻辑

```javascript
// 错误的实现 - 总是返回最新的考核配置ID
const getCurrentAssessmentConfigId = () => {
  return assessmentData.value?.assessmentConfigs?.[0]?.id || 1
}
```

**问题**：
- 用户在"2025月度考核"(ID=11)标签页操作
- 但提交时使用的是"7月考核任务"(ID=12)的配置ID
- 导致数据提交到错误的考核配置中

### 3. 修改后的逻辑

```javascript
// 正确的实现 - 返回当前活动标签页对应的考核配置ID
const getCurrentAssessmentConfigId = () => {
  // 获取当前活动标签页对应的考核配置ID
  const currentTab = editableTabs.value.find(tab => tab.name === editableTabsValue.value)
  if (currentTab && currentTab.configId) {
    return currentTab.configId
  }
  
  // 如果找不到当前标签页，则返回第一个考核配置的ID作为默认值
  return assessmentData.value?.assessmentConfigs?.[0]?.id || 1
}
```

**改进**：
- 根据当前活动的标签页(`editableTabsValue.value`)查找对应的标签页对象
- 从标签页对象中获取 `configId` 属性
- 确保提交的数据使用正确的考核配置ID

### 4. 调试信息

添加了调试日志来验证修复效果：
```javascript
console.log('当前标签页:', editableTabsValue.value)
console.log('当前考核配置ID:', assessmentConfigId)
```

## 测试场景

### 场景1：在历史考核配置中提交数据

1. **操作**：切换到"2025月度考核"标签页
2. **预期**：
   - `editableTabsValue.value` = `"assessment_11"`
   - `getCurrentAssessmentConfigId()` 返回 `11`
   - 提交的数据插入到考核配置ID=11中

### 场景2：在当前考核配置中提交数据

1. **操作**：切换到"7月考核任务"标签页
2. **预期**：
   - `editableTabsValue.value` = `"assessment_12"`
   - `getCurrentAssessmentConfigId()` 返回 `12`
   - 提交的数据插入到考核配置ID=12中

## 数据流程验证

### 提交前
```javascript
// 用户在"2025月度考核"标签页填写数据
当前标签页: "assessment_11"
当前考核配置ID: 11

// 准备提交的数据
{
  assessmentConfigId: 11,  // ✅ 正确使用标签页对应的配置ID
  records: [
    {
      assessmentConfigId: 11,
      username: "10159180",
      projectId: 23,
      assessmentCoefficient: 50,
      calculationParameter: "project_participation"
    }
  ]
}
```

### 数据库验证
```sql
-- 验证数据是否插入到正确的考核配置中
SELECT * FROM assessment_coefficient_allocation 
WHERE assessment_config_id = 11 
  AND username = '10159180';
```

## 影响范围

### 修改的函数
1. `getCurrentAssessmentConfigId()` - 核心修复
2. `prepareSubmitData()` - 添加调试信息

### 影响的功能
1. **考核系数提交** - 主要修复目标
2. **Excel导出** - 也使用了 `getCurrentAssessmentConfigId()`
3. **数据查询** - 确保查询正确的考核配置数据

## 测试步骤

1. **刷新页面**，确保获取最新代码
2. **切换到历史标签页**（如"2025月度考核"）
3. **填写考核系数数据**
4. **点击提交按钮**
5. **查看控制台日志**，确认：
   - 当前标签页名称正确
   - 当前考核配置ID正确
6. **验证数据库**，确认数据插入到正确的考核配置中

## 预期结果

修复后，用户在不同标签页的操作将会：
- ✅ 数据提交到对应的考核配置中
- ✅ 不会出现数据错位的问题
- ✅ 每个考核配置的数据独立管理
- ✅ 支持历史数据的查看和修改

这个修复确保了多考核配置环境下的数据一致性和操作准确性。
