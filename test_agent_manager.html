<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理负责人功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>代理负责人功能测试</h1>
    
    <div class="test-section">
        <h2>1. 测试 getUserInfo API</h2>
        <button onclick="testGetUserInfo()">测试获取用户信息</button>
        <div id="userInfoResult"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试设置代理负责人 API</h2>
        <button onclick="testSetAgentManager(true)">设置为代理负责人</button>
        <button onclick="testSetAgentManager(false)">取消代理负责人</button>
        <div id="setAgentResult"></div>
    </div>

    <div class="test-section">
        <h2>3. 测试考核数据 API</h2>
        <button onclick="testAssessmentData()">获取考核数据</button>
        <div id="assessmentResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // 模拟的认证token（实际使用时需要真实的token）
        const AUTH_TOKEN = 'your-auth-token-here';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                return { success: response.ok, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testGetUserInfo() {
            const result = await makeRequest(`${API_BASE}/user/getUserInfo`);
            const resultDiv = document.getElementById('userInfoResult');
            
            if (result.success) {
                resultDiv.className = 'test-section success';
                resultDiv.innerHTML = `
                    <h3>✅ 成功获取用户信息</h3>
                    <p><strong>管理员状态:</strong> ${result.data.org?.is_admin ? '是' : '否'}</p>
                    <p><strong>代理负责人状态:</strong> ${result.data.org?.is_agent_manager ? '是' : '否'}</p>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h3>❌ 获取用户信息失败</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            }
        }

        async function testSetAgentManager(isAgentManager) {
            const requestData = {
                user_id: 2, // 测试用户ID
                org_id: 1,  // 测试组织ID
                is_agent_manager: isAgentManager
            };

            const result = await makeRequest(`${API_BASE}/org/setAgentManager`, {
                method: 'PUT',
                body: JSON.stringify(requestData)
            });

            const resultDiv = document.getElementById('setAgentResult');
            
            if (result.success) {
                resultDiv.className = 'test-section success';
                resultDiv.innerHTML = `
                    <h3>✅ ${isAgentManager ? '设置' : '取消'}代理负责人成功</h3>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h3>❌ ${isAgentManager ? '设置' : '取消'}代理负责人失败</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            }
        }

        async function testAssessmentData() {
            const requestData = {
                orgId: 1,
                isAdmin: true,
                userName: 'test_user'
            };

            const result = await makeRequest(`${API_BASE}/assessmentData/getAssessmentData`, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            const resultDiv = document.getElementById('assessmentResult');
            
            if (result.success) {
                const orgMembers = result.data.adminData?.orgMembers || [];
                const agentManagers = orgMembers.filter(member => member.isAgentManager);
                
                resultDiv.className = 'test-section success';
                resultDiv.innerHTML = `
                    <h3>✅ 成功获取考核数据</h3>
                    <p><strong>组织成员总数:</strong> ${orgMembers.length}</p>
                    <p><strong>代理负责人数量:</strong> ${agentManagers.length}</p>
                    <p><strong>代理负责人列表:</strong> ${agentManagers.map(m => m.nickName).join(', ') || '无'}</p>
                    <details>
                        <summary>查看完整数据</summary>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    </details>
                `;
            } else {
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = `
                    <h3>❌ 获取考核数据失败</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            }
        }

        // 页面加载时自动测试getUserInfo
        window.onload = function() {
            console.log('页面加载完成，开始测试...');
        };
    </script>
</body>
</html>
