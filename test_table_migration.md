# 数据表拆分测试说明

## 修改概述

已将 `project_assessment_score` 表拆分为两个新表：

1. **`assessment_coefficient_allocation`** - 考核系数分配表
2. **`project_manager_score`** - 项目负责人评分表

## 修改的文件

### 1. 数据服务层 (`server/service/assessment/assessment_data.go`)

修改了以下方法：
- `getCoefficientData()` - 使用联合查询从两个新表获取数据
- `getPreviousCoefficients()` - 使用联合查询获取历史数据
- `getProjectCoefficients()` - 使用联合查询获取项目系数信息

### 2. 系数管理服务 (`server/service/assessment/assessment_coefficient.go`)

修改了以下方法：
- `ReplaceAssessmentCoefficients()` - 重写为操作两个新表
- `GetAssessmentCoefficients()` - 使用联合查询但保持返回格式兼容
- `ValidateCoefficientsSum()` - 使用新表结构进行验证
- 添加了 `validateCoefficientsSumNew()` 新方法

### 3. 数据库迁移 (`server/initialize/gorm_biz.go`)

添加了 `AssessmentCoefficientAllocation` 到自动迁移列表

## 数据结构保持兼容

虽然底层表结构发生了变化，但返回给前端的数据结构保持不变：

```json
{
  "coefficientData": {
    "hasCurrentData": true,
    "currentAssessmentId": 1,
    "coefficients": [
      {
        "id": 1,
        "assessmentConfigId": 1,
        "username": "10112531",
        "projectId": 1,
        "assessmentCoefficient": 0.8,
        "managerScore": null,
        "scorerUsername": null,
        "calculationParameter": "A",
        "createdAt": "2025-01-15T10:00:00Z"
      }
    ]
  }
}
```

## 联合查询逻辑

新的查询使用 LEFT JOIN 来合并两个表的数据：

```sql
SELECT aca.id, aca.assessment_config_id, aca.username, aca.project_id, 
       aca.assessment_coefficient, pms.manager_score, pms.scorer_username, 
       aca.calculation_parameter, aca.created_at
FROM assessment_coefficient_allocation aca
LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id
WHERE aca.assessment_config_id = ? AND aca.username = ?
```

## 测试建议

1. **数据迁移测试**：
   - 备份现有 `project_assessment_score` 表数据
   - 创建新的两个表
   - 将数据迁移到新表中
   - 验证数据完整性

2. **API 测试**：
   - 测试 `/assessmentData/getAssessmentData` 接口
   - 验证返回的数据结构与之前一致
   - 测试系数分配和评分功能

3. **前端兼容性测试**：
   - 确认前端页面正常显示
   - 测试数据更新功能
   - 验证表格和表单操作

## 注意事项

- 原有的 `project_assessment_score` 表暂时保留，可在确认新逻辑正常工作后删除
- 新的表结构支持更灵活的评分管理
- 保持了向后兼容性，前端无需修改
