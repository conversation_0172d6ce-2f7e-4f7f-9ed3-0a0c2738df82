-- 更新 calculation_parameter 字段长度
-- 解决 'project_participation' 字符串长度超出限制的问题

-- 1. 更新 assessment_coefficient_allocation 表的 calculation_parameter 字段长度
ALTER TABLE `assessment_coefficient_allocation` 
MODIFY COLUMN `calculation_parameter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算参数';

-- 2. 更新 project_manager_score 表的 calculation_parameter 字段长度
ALTER TABLE `project_manager_score` 
MODIFY COLUMN `calculation_parameter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算参数';

-- 3. 如果原有的 project_assessment_score 表还存在，也更新它的字段长度（可选）
-- ALTER TABLE `project_assessment_score` 
-- MODIFY COLUMN `calculation_parameter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计算参数（英文字母，如A、B、C等）';

-- 验证字段长度修改是否成功
DESCRIBE `assessment_coefficient_allocation`;
DESCRIBE `project_manager_score`;

-- 测试插入 'project_participation' 值
-- INSERT INTO `assessment_coefficient_allocation` 
-- (`assessment_config_id`, `username`, `project_id`, `assessment_coefficient`, `calculation_parameter`, `created_at`, `updated_at`)
-- VALUES (1, 'test_user', 1, 50.0, 'project_participation', NOW(), NOW());

-- 查询测试数据
-- SELECT * FROM `assessment_coefficient_allocation` WHERE `calculation_parameter` = 'project_participation';
