import service from '@/utils/request'

// @Tags AssessmentCategories
// @Summary 创建考核类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AssessmentCategories true "创建考核类型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /assessmentCategories/createAssessmentCategories [post]
export const createAssessmentCategories = (data) => {
  return service({
    url: '/assessmentCategories/createAssessmentCategories',
    method: 'post',
    data
  })
}

// @Tags AssessmentCategories
// @Summary 删除考核类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AssessmentCategories true "删除考核类型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /assessmentCategories/deleteAssessmentCategories [delete]
export const deleteAssessmentCategories = (params) => {
  return service({
    url: '/assessmentCategories/deleteAssessmentCategories',
    method: 'delete',
    params
  })
}

// @Tags AssessmentCategories
// @Summary 批量删除考核类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除考核类型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /assessmentCategories/deleteAssessmentCategoriesByIds [delete]
export const deleteAssessmentCategoriesByIds = (params) => {
  return service({
    url: '/assessmentCategories/deleteAssessmentCategoriesByIds',
    method: 'delete',
    params
  })
}

// @Tags AssessmentCategories
// @Summary 更新考核类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AssessmentCategories true "更新考核类型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /assessmentCategories/updateAssessmentCategories [put]
export const updateAssessmentCategories = (data) => {
  return service({
    url: '/assessmentCategories/updateAssessmentCategories',
    method: 'put',
    data
  })
}

// @Tags AssessmentCategories
// @Summary 用id查询考核类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AssessmentCategories true "用id查询考核类型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /assessmentCategories/findAssessmentCategories [get]
export const findAssessmentCategories = (params) => {
  return service({
    url: '/assessmentCategories/findAssessmentCategories',
    method: 'get',
    params
  })
}

// @Tags AssessmentCategories
// @Summary 分页获取考核类型列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取考核类型列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /assessmentCategories/getAssessmentCategoriesList [get]
export const getAssessmentCategoriesList = (params) => {
  return service({
    url: '/assessmentCategories/getAssessmentCategoriesList',
    method: 'get',
    params
  })
}
