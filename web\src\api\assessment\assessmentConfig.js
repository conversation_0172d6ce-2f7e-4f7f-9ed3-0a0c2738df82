import service from '@/utils/request'
// @Tags AssessmentConfig
// @Summary 创建assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AssessmentConfig true "创建assessmentConfig表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /assessmentConfig/createAssessmentConfig [post]
export const createAssessmentConfig = (data) => {
  return service({
    url: '/assessmentConfig/createAssessmentConfig',
    method: 'post',
    data
  })
}

// @Tags AssessmentConfig
// @Summary 删除assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AssessmentConfig true "删除assessmentConfig表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /assessmentConfig/deleteAssessmentConfig [delete]
export const deleteAssessmentConfig = (params) => {
  return service({
    url: '/assessmentConfig/deleteAssessmentConfig',
    method: 'delete',
    params
  })
}

// @Tags AssessmentConfig
// @Summary 批量删除assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除assessmentConfig表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /assessmentConfig/deleteAssessmentConfig [delete]
export const deleteAssessmentConfigByIds = (params) => {
  return service({
    url: '/assessmentConfig/deleteAssessmentConfigByIds',
    method: 'delete',
    params
  })
}

// @Tags AssessmentConfig
// @Summary 更新assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AssessmentConfig true "更新assessmentConfig表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /assessmentConfig/updateAssessmentConfig [put]
export const updateAssessmentConfig = (data) => {
  return service({
    url: '/assessmentConfig/updateAssessmentConfig',
    method: 'put',
    data
  })
}

// @Tags AssessmentConfig
// @Summary 用id查询assessmentConfig表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AssessmentConfig true "用id查询assessmentConfig表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /assessmentConfig/findAssessmentConfig [get]
export const findAssessmentConfig = (params) => {
  return service({
    url: '/assessmentConfig/findAssessmentConfig',
    method: 'get',
    params
  })
}

// @Tags AssessmentConfig
// @Summary 用id查询包含关联名称的考核配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询考核配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /assessmentConfig/findAssessmentConfigWithNames [get]
export const findAssessmentConfigWithNames = (params) => {
  return service({
    url: '/assessmentConfig/findAssessmentConfigWithNames',
    method: 'get',
    params
  })
}

// @Tags AssessmentConfig
// @Summary 分页获取assessmentConfig表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取assessmentConfig表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /assessmentConfig/getAssessmentConfigList [get]
export const getAssessmentConfigList = (params) => {
  return service({
    url: '/assessmentConfig/getAssessmentConfigList',
    method: 'get',
    params
  })
}

// @Tags AssessmentConfig
// @Summary 分页获取包含关联名称的考核配置列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.AssessmentConfigSearch true "分页获取考核配置列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /assessmentConfig/getAssessmentConfigWithNamesList [get]
export const getAssessmentConfigWithNamesList = (params) => {
  return service({
    url: '/assessmentConfig/getAssessmentConfigWithNamesList',
    method: 'get',
    params
  })
}

// @Tags AssessmentConfig
// @Summary 不需要鉴权的assessmentConfig表接口
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.AssessmentConfigSearch true "分页获取assessmentConfig表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentConfig/getAssessmentConfigPublic [get]
export const getAssessmentConfigPublic = () => {
  return service({
    url: '/assessmentConfig/getAssessmentConfigPublic',
    method: 'get',
  })
}
