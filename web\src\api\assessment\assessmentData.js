import service from '@/utils/request'

// @Tags AssessmentData
// @Summary 获取考核数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body {orgId:number,userName:string} true "获取考核数据请求"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentData/getAssessmentData [post]
export const getAssessmentData = (data) => {
  return service({
    url: '/assessmentData/getAssessmentData',
    method: 'post',
    data
  })
}

// @Tags AssessmentData
// @Summary 获取用户参数评分数据（支持批量查询）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body {userNames:string[],assessmentConfigIds:number[],parameterNames:string[]} true "获取用户参数评分请求"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentData/getUserParameterScores [post]
export const getUserParameterScores = (data) => {
  return service({
    url: '/assessmentData/getUserParameterScores',
    method: 'post',
    data
  })
}

/**
 * 获取单个用户的参数评分数据
 * @param {string} userName - 用户名
 * @param {number[]} assessmentConfigIds - 考核配置ID列表（可选）
 * @param {string[]} parameterNames - 参数名称列表（可选）
 * @returns {Promise} API响应
 */
export const getSingleUserParameterScores = (userName, assessmentConfigIds = [], parameterNames = []) => {
  return getUserParameterScores({
    userNames: [userName],
    assessmentConfigIds,
    parameterNames
  })
}

/**
 * 获取多个用户的参数评分数据
 * @param {string[]} userNames - 用户名列表
 * @param {number[]} assessmentConfigIds - 考核配置ID列表（可选）
 * @param {string[]} parameterNames - 参数名称列表（可选）
 * @returns {Promise} API响应
 */
export const getBatchUserParameterScores = (userNames, assessmentConfigIds = [], parameterNames = []) => {
  return getUserParameterScores({
    userNames,
    assessmentConfigIds,
    parameterNames
  })
}

/**
 * 获取用户在指定考核配置下的评分数据
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise} API响应
 */
export const getUserScoresByConfig = (userName, assessmentConfigId) => {
  return getSingleUserParameterScores(userName, [assessmentConfigId])
}

/**
 * 获取用户指定参数的评分数据
 * @param {string} userName - 用户名
 * @param {string[]} parameterNames - 参数名称列表
 * @returns {Promise} API响应
 */
export const getUserScoresByParameters = (userName, parameterNames) => {
  return getSingleUserParameterScores(userName, [], parameterNames)
}

// @Tags AssessmentData
// @Summary 根据考核配置获取评价详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body {assessmentConfigId:number} true "获取评价详情请求"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentData/getEvaluationDetailsByConfig [post]
export const getEvaluationDetailsByConfig = (assessmentConfigId) => {
  return service({
    url: '/assessmentData/getEvaluationDetailsByConfig',
    method: 'post',
    data: {
      assessmentConfigId
    }
  })
}

// @Tags AssessmentData
// @Summary 根据考核配置ID获取评估详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body {assessmentConfigId:number} true "获取评估详情请求"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /assessmentData/getEvaluationDetailsByConfigId [post]
export const getEvaluationDetailsByConfigId = (assessmentConfigId) => {
  return service({
    url: '/assessmentData/getEvaluationDetailsByConfigId',
    method: 'post',
    data: {
      assessmentConfigId
    }
  })
}
