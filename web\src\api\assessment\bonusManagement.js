import service from '@/utils/request'
// @Tags BonusManagement
// @Summary 创建奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.BonusManagement true "创建奖金管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /bonusManagement/createBonusManagement [post]
export const createBonusManagement = (data) => {
  return service({
    url: '/bonusManagement/createBonusManagement',
    method: 'post',
    data
  })
}

// @Tags BonusManagement
// @Summary 删除奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.BonusManagement true "删除奖金管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bonusManagement/deleteBonusManagement [delete]
export const deleteBonusManagement = (params) => {
  return service({
    url: '/bonusManagement/deleteBonusManagement',
    method: 'delete',
    params
  })
}

// @Tags BonusManagement
// @Summary 批量删除奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除奖金管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bonusManagement/deleteBonusManagement [delete]
export const deleteBonusManagementByIds = (params) => {
  return service({
    url: '/bonusManagement/deleteBonusManagementByIds',
    method: 'delete',
    params
  })
}

// @Tags BonusManagement
// @Summary 更新奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.BonusManagement true "更新奖金管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bonusManagement/updateBonusManagement [put]
export const updateBonusManagement = (data) => {
  return service({
    url: '/bonusManagement/updateBonusManagement',
    method: 'put',
    data
  })
}

// @Tags BonusManagement
// @Summary 用id查询奖金管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.BonusManagement true "用id查询奖金管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bonusManagement/findBonusManagement [get]
export const findBonusManagement = (params) => {
  return service({
    url: '/bonusManagement/findBonusManagement',
    method: 'get',
    params
  })
}

// @Tags BonusManagement
// @Summary 分页获取奖金管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取奖金管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bonusManagement/getBonusManagementList [get]
export const getBonusManagementList = (params) => {
  return service({
    url: '/bonusManagement/getBonusManagementList',
    method: 'get',
    params
  })
}

// @Tags BonusManagement
// @Summary 不需要鉴权的奖金管理接口
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.BonusManagementSearch true "分页获取奖金管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /bonusManagement/getBonusManagementPublic [get]
export const getBonusManagementPublic = () => {
  return service({
    url: '/bonusManagement/getBonusManagementPublic',
    method: 'get',
  })
}
