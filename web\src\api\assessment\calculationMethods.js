import service from '@/utils/request'

// @Tags CalculationMethods
// @Summary 创建计算方法及其关联关系
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CreateCalculationMethodWithRelations true "创建计算方法及其关联关系"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /calculationMethods/createCalculationMethods [post]
export const createCalculationMethods = (data) => {
  return service({
    url: '/calculationMethods/createCalculationMethods',
    method: 'post',
    data
  })
}

// @Tags CalculationMethods
// @Summary 删除计算方法及其关联关系
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "删除计算方法及其关联关系"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /calculationMethods/deleteCalculationMethods [delete]
export const deleteCalculationMethods = (params) => {
  return service({
    url: '/calculationMethods/deleteCalculationMethods',
    method: 'delete',
    params
  })
}

// @Tags CalculationMethods
// @Summary 批量删除计算方法及其关联关系
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除计算方法及其关联关系"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /calculationMethods/deleteCalculationMethodsByIds [delete]
export const deleteCalculationMethodsByIds = (params) => {
  return service({
    url: '/calculationMethods/deleteCalculationMethodsByIds',
    method: 'delete',
    params
  })
}

// @Tags CalculationMethods
// @Summary 更新计算方法及其关联关系
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateCalculationMethodWithRelations true "更新计算方法及其关联关系"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /calculationMethods/updateCalculationMethods [put]
export const updateCalculationMethods = (data) => {
  return service({
    url: '/calculationMethods/updateCalculationMethods',
    method: 'put',
    data
  })
}

// @Tags CalculationMethods
// @Summary 用id查询计算方法及其关联关系
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "用id查询计算方法及其关联关系"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /calculationMethods/findCalculationMethods [get]
export const findCalculationMethods = (params) => {
  return service({
    url: '/calculationMethods/findCalculationMethods',
    method: 'get',
    params
  })
}

// @Tags CalculationMethods
// @Summary 分页获取计算方法列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取计算方法列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /calculationMethods/getCalculationMethodsList [get]
export const getCalculationMethodsList = (params) => {
  return service({
    url: '/calculationMethods/getCalculationMethodsList',
    method: 'get',
    params
  })
}

// @Tags CalculationMethods
// @Summary 根据规则计算结果
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body object true "规则计算请求"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"计算成功"}"
// @Router /calculationMethods/calculateByRule [post]
export const calculateByRule = (data) => {
  return service({
    url: '/calculationMethods/calculateByRule',
    method: 'post',
    data
  })
}

// @Tags CalculationMethods
// @Summary 测试规则配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body object true "规则测试请求"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"测试成功"}"
// @Router /calculationMethods/testRule [post]
export const testRule = (data) => {
  return service({
    url: '/calculationMethods/testRule',
    method: 'post',
    data
  })
}
