import service from '@/utils/request'
// @Tags ScoreQuotaManagement
// @Summary 创建高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ScoreQuotaManagement true "创建高分配额管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /scoreQuotaManagement/createScoreQuotaManagement [post]
export const createScoreQuotaManagement = (data) => {
  return service({
    url: '/scoreQuotaManagement/createScoreQuotaManagement',
    method: 'post',
    data
  })
}

// @Tags ScoreQuotaManagement
// @Summary 删除高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ScoreQuotaManagement true "删除高分配额管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /scoreQuotaManagement/deleteScoreQuotaManagement [delete]
export const deleteScoreQuotaManagement = (params) => {
  return service({
    url: '/scoreQuotaManagement/deleteScoreQuotaManagement',
    method: 'delete',
    params
  })
}

// @Tags ScoreQuotaManagement
// @Summary 批量删除高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除高分配额管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /scoreQuotaManagement/deleteScoreQuotaManagement [delete]
export const deleteScoreQuotaManagementByIds = (params) => {
  return service({
    url: '/scoreQuotaManagement/deleteScoreQuotaManagementByIds',
    method: 'delete',
    params
  })
}

// @Tags ScoreQuotaManagement
// @Summary 更新高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ScoreQuotaManagement true "更新高分配额管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /scoreQuotaManagement/updateScoreQuotaManagement [put]
export const updateScoreQuotaManagement = (data) => {
  return service({
    url: '/scoreQuotaManagement/updateScoreQuotaManagement',
    method: 'put',
    data
  })
}

// @Tags ScoreQuotaManagement
// @Summary 用id查询高分配额管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.ScoreQuotaManagement true "用id查询高分配额管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /scoreQuotaManagement/findScoreQuotaManagement [get]
export const findScoreQuotaManagement = (params) => {
  return service({
    url: '/scoreQuotaManagement/findScoreQuotaManagement',
    method: 'get',
    params
  })
}

// @Tags ScoreQuotaManagement
// @Summary 分页获取高分配额管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取高分配额管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /scoreQuotaManagement/getScoreQuotaManagementList [get]
export const getScoreQuotaManagementList = (params) => {
  return service({
    url: '/scoreQuotaManagement/getScoreQuotaManagementList',
    method: 'get',
    params
  })
}

// @Tags ScoreQuotaManagement
// @Summary 不需要鉴权的高分配额管理接口
// @Accept application/json
// @Produce application/json
// @Param data query assessmentReq.ScoreQuotaManagementSearch true "分页获取高分配额管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /scoreQuotaManagement/getScoreQuotaManagementPublic [get]
export const getScoreQuotaManagementPublic = () => {
  return service({
    url: '/scoreQuotaManagement/getScoreQuotaManagementPublic',
    method: 'get',
  })
}
