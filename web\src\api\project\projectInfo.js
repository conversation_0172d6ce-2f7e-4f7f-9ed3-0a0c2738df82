import service from '@/utils/request'
// @Tags ProjectInfo
// @Summary 创建项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ProjectInfo true "创建项目信息表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /projectInfo/createProjectInfo [post]
export const createProjectInfo = (data) => {
  return service({
    url: '/projectInfo/createProjectInfo',
    method: 'post',
    data
  })
}

// @Tags ProjectInfo
// @Summary 删除项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ProjectInfo true "删除项目信息表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /projectInfo/deleteProjectInfo [delete]
export const deleteProjectInfo = (params) => {
  return service({
    url: '/projectInfo/deleteProjectInfo',
    method: 'delete',
    params
  })
}

// @Tags ProjectInfo
// @Summary 批量删除项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除项目信息表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /projectInfo/deleteProjectInfo [delete]
export const deleteProjectInfoByIds = (params) => {
  return service({
    url: '/projectInfo/deleteProjectInfoByIds',
    method: 'delete',
    params
  })
}

// @Tags ProjectInfo
// @Summary 更新项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ProjectInfo true "更新项目信息表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /projectInfo/updateProjectInfo [put]
export const updateProjectInfo = (data) => {
  return service({
    url: '/projectInfo/updateProjectInfo',
    method: 'put',
    data
  })
}

// @Tags ProjectInfo
// @Summary 用id查询项目信息表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.ProjectInfo true "用id查询项目信息表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /projectInfo/findProjectInfo [get]
export const findProjectInfo = (params) => {
  return service({
    url: '/projectInfo/findProjectInfo',
    method: 'get',
    params
  })
}

// @Tags ProjectInfo
// @Summary 分页获取项目信息表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取项目信息表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /projectInfo/getProjectInfoList [get]
export const getProjectInfoList = (params) => {
  return service({
    url: '/projectInfo/getProjectInfoList',
    method: 'get',
    params
  })
}

// @Tags ProjectInfo
// @Summary 不需要鉴权的项目信息表接口
// @Accept application/json
// @Produce application/json
// @Param data query projectReq.ProjectInfoSearch true "分页获取项目信息表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectInfo/getProjectInfoPublic [get]
export const getProjectInfoPublic = () => {
  return service({
    url: '/projectInfo/getProjectInfoPublic',
    method: 'get',
  })
}
