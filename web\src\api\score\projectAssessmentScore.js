import service from '@/utils/request'
// @Tags ProjectAssessmentScore
// @Summary 创建精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ProjectAssessmentScore true "创建精力分配评分表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /projectAssessmentScore/createProjectAssessmentScore [post]
export const createProjectAssessmentScore = (data) => {
  return service({
    url: '/projectAssessmentScore/createProjectAssessmentScore',
    method: 'post',
    data
  })
}

// @Tags ProjectAssessmentScore
// @Summary 删除精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ProjectAssessmentScore true "删除精力分配评分表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /projectAssessmentScore/deleteProjectAssessmentScore [delete]
export const deleteProjectAssessmentScore = (params) => {
  return service({
    url: '/projectAssessmentScore/deleteProjectAssessmentScore',
    method: 'delete',
    params
  })
}

// @Tags ProjectAssessmentScore
// @Summary 批量删除精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除精力分配评分表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /projectAssessmentScore/deleteProjectAssessmentScore [delete]
export const deleteProjectAssessmentScoreByIds = (params) => {
  return service({
    url: '/projectAssessmentScore/deleteProjectAssessmentScoreByIds',
    method: 'delete',
    params
  })
}

// @Tags ProjectAssessmentScore
// @Summary 更新精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ProjectAssessmentScore true "更新精力分配评分表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /projectAssessmentScore/updateProjectAssessmentScore [put]
export const updateProjectAssessmentScore = (data) => {
  return service({
    url: '/projectAssessmentScore/updateProjectAssessmentScore',
    method: 'put',
    data
  })
}

// @Tags ProjectAssessmentScore
// @Summary 用id查询精力分配评分表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.ProjectAssessmentScore true "用id查询精力分配评分表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /projectAssessmentScore/findProjectAssessmentScore [get]
export const findProjectAssessmentScore = (params) => {
  return service({
    url: '/projectAssessmentScore/findProjectAssessmentScore',
    method: 'get',
    params
  })
}

// @Tags ProjectAssessmentScore
// @Summary 分页获取精力分配评分表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取精力分配评分表列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /projectAssessmentScore/getProjectAssessmentScoreList [get]
export const getProjectAssessmentScoreList = (params) => {
  return service({
    url: '/projectAssessmentScore/getProjectAssessmentScoreList',
    method: 'get',
    params
  })
}

// @Tags ProjectAssessmentScore
// @Summary 不需要鉴权的精力分配评分表接口
// @Accept application/json
// @Produce application/json
// @Param data query scoreReq.ProjectAssessmentScoreSearch true "分页获取精力分配评分表列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectAssessmentScore/getProjectAssessmentScorePublic [get]
export const getProjectAssessmentScorePublic = () => {
  return service({
    url: '/projectAssessmentScore/getProjectAssessmentScorePublic',
    method: 'get',
  })
}

// ==================== 考核系数分配相关接口 ====================

// @Tags Assessment
// @Summary 替换考核系数分配
// @Description 先删除指定考核配置的所有系数记录，然后插入新的系数记录（事务操作）
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body object true "考核系数替换数据"
// @Success 200 {object} response.Response{msg=string} "替换成功"
// @Router /assessment/coefficients/replace [post]
export const replaceAssessmentCoefficients = (data) => {
  return service({
    url: '/assessment/coefficients/replace',
    method: 'post',
    data
  })
}

// @Tags Assessment
// @Summary 获取考核系数分配
// @Description 根据考核配置ID获取所有考核系数记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param configId path int true "考核配置ID"
// @Success 200 {object} response.Response{data=[]model.ProjectAssessmentScore} "获取成功"
// @Router /assessment/coefficients/{configId} [get]
export const getAssessmentCoefficients = (assessmentConfigId) => {
  return service({
    url: `/assessment/coefficients/${assessmentConfigId}`,
    method: 'get'
  })
}

// @Tags Assessment
// @Summary 获取考核数据（包含项目、成员、系数等完整信息）
// @Description 获取指定组织的完整考核数据，用于前端表格展示
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body object true "获取考核数据请求参数"
// @Success 200 {object} response.Response{data=object} "获取成功"
// @Router /assessment/data [post]
export const getAssessmentData = (data) => {
  return service({
    url: '/assessment/data',
    method: 'post',
    data
  })
}

// @Tags Assessment
// @Summary 校验考核系数总和
// @Description 校验指定考核配置中每个用户的系数总和是否为100%
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param configId path int true "考核配置ID"
// @Success 200 {object} response.Response{data=object} "校验结果"
// @Router /assessment/coefficients/{configId}/validate [get]
export const validateAssessmentCoefficients = (assessmentConfigId) => {
  return service({
    url: `/assessment/coefficients/${assessmentConfigId}/validate`,
    method: 'get'
  })
}

// @Tags Assessment
// @Summary 导出考核系数分配Excel
// @Description 导出指定考核配置的系数分配表格为Excel文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/octet-stream
// @Param configId path int true "考核配置ID"
// @Success 200 {file} file "Excel文件"
// @Router /assessment/coefficients/{configId}/export [get]
export const exportAssessmentCoefficients = (assessmentConfigId) => {
  return service({
    url: `/assessment/coefficients/${assessmentConfigId}/export`,
    method: 'get',
    responseType: 'blob' // 用于文件下载
  })
}

// @Tags Assessment
// @Summary 批量导入考核系数分配
// @Description 通过Excel文件批量导入考核系数分配
// @Security ApiKeyAuth
// @Accept multipart/form-data
// @Produce application/json
// @Param file formData file true "Excel文件"
// @Param assessmentConfigId formData int true "考核配置ID"
// @Success 200 {object} response.Response{data=object} "导入结果"
// @Router /assessment/coefficients/import [post]
export const importAssessmentCoefficients = (formData) => {
  return service({
    url: '/assessment/coefficients/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 项目成员评分相关接口 ====================

// @Tags ProjectManagerScore
// @Summary 批量提交项目成员评分
// @Description 批量提交项目负责人对成员的评分数据到project_manager_score表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body array true "项目成员评分数据"
// @Success 200 {object} response.Response{msg=string} "提交成功"
// @Router /projectManagerScore/batchSubmitProjectMemberScores [post]
export const batchSubmitProjectMemberScores = (data) => {
  return service({
    url: '/projectManagerScore/batchSubmitProjectMemberScores',
    method: 'post',
    data
  })
}

// ==================== 部门负责人评分相关接口 ====================

// @Tags DepartmentManagerScore
// @Summary 批量提交部门负责人评分
// @Description 批量提交部门负责人对员工的评分数据和奖金分配到department_manager_score表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body array true "部门负责人评分数据"
// @Success 200 {object} response.Response{msg=string} "提交成功"
// @Router /departmentManagerScore/batchSubmitDepartmentManagerScores [post]
export const batchSubmitDepartmentManagerScores = (data) => {
  return service({
    url: '/departmentManagerScore/batchSubmitDepartmentManagerScores',
    method: 'post',
    data
  })
}
