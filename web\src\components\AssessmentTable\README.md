# AssessmentTable 通用评分表格组件

## 概述

`AssessmentTable` 是一个通用的评分表格组件，支持固定表头、左侧列和底部合计行，具有十字高亮效果和完全的响应式设计。

## 特性

- ✅ **固定定位**: 表头、左侧项目列、底部合计行全部支持 sticky 定位
- ✅ **十字高亮**: 鼠标悬停时显示十字高亮效果
- ✅ **响应式设计**: 支持移动端和桌面端
- ✅ **自定义内容**: 支持 slot 自定义单元格和合计行内容
- ✅ **事件支持**: 支持单元格点击和内容变更事件
- ✅ **高性能**: 优化的 z-index 层级管理

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `projects` | Array | `[]` | 项目数据数组 |
| `members` | Array | `[]` | 成员数据数组 |
| `scores` | Array | `[]` | 评分数据数组 |
| `headerLabel` | String | `'项目 \\ 人员'` | 左上角表头标签 |
| `totalLabel` | String | `'合计'` | 合计行标签 |
| `showTotal` | Boolean | `true` | 是否显示合计行 |
| `enableHighlight` | Boolean | `true` | 是否启用高亮效果 |

## 数据格式

### projects 数据格式
```javascript
[
  {
    id: 1,
    projectName: '项目名称',
    projectType: '项目类型' // 可选
  }
]
```

### members 数据格式
```javascript
[
  {
    id: 1,
    username: '用户名',
    nickName: '显示名称',
    authorityName: '角色名称'
  }
]
```

### scores 数据格式
```javascript
[
  {
    projectId: 1,
    memberId: 1,
    score: 85
  }
]
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `cell-click` | `{ project, member, projectIndex, memberIndex, score }` | 单元格点击事件 |
| `cell-change` | `{ project, member, projectIndex, memberIndex, score }` | 单元格内容变更事件 |

## Slots

### cell
自定义单元格内容

```vue
<template #cell="{ project, member, score, projectIndex, memberIndex }">
  <input 
    v-model="score" 
    @change="handleScoreChange(project.id, member.id, $event)"
  />
</template>
```

### total
自定义合计行内容

```vue
<template #total="{ member, total }">
  <span class="total-score">{{ total }}</span>
</template>
```

## 使用示例

### 基础用法

```vue
<template>
  <AssessmentTable
    :projects="projects"
    :members="members"
    :scores="scores"
    @cell-click="handleCellClick"
    @cell-change="handleCellChange"
  />
</template>

<script setup>
import AssessmentTable from '@/components/AssessmentTable'

const projects = ref([
  { id: 1, projectName: '项目A', projectType: '自研项目' },
  { id: 2, projectName: '项目B', projectType: '承揽项目' }
])

const members = ref([
  { id: 1, username: 'zhangsan', nickName: '张三', authorityName: '开发工程师' },
  { id: 2, username: 'lisi', nickName: '李四', authorityName: '测试工程师' }
])

const scores = ref([
  { projectId: 1, memberId: 1, score: 85 },
  { projectId: 1, memberId: 2, score: 90 },
  { projectId: 2, memberId: 1, score: 78 },
  { projectId: 2, memberId: 2, score: 82 }
])

const handleCellClick = (data) => {
  console.log('Cell clicked:', data)
}

const handleCellChange = (data) => {
  console.log('Cell changed:', data)
}
</script>
```

### 自定义可编辑单元格

```vue
<template>
  <AssessmentTable
    :projects="projects"
    :members="members"
    :scores="scores"
  >
    <template #cell="{ project, member, score }">
      <div
        class="editable-cell"
        contenteditable="true"
        @blur="updateScore(project.id, member.id, $event)"
        @keydown="handleKeydown($event)"
      >
        {{ score }}
      </div>
    </template>
  </AssessmentTable>
</template>
```

### 不同业务场景的配置

#### 月度评分
```vue
<AssessmentTable
  :projects="monthlyProjects"
  :members="monthlyMembers"
  :scores="monthlyScores"
  header-label="项目 \ 成员"
  total-label="月度合计"
/>
```

#### 季度评分
```vue
<AssessmentTable
  :projects="quarterlyProjects"
  :members="quarterlyMembers"
  :scores="quarterlyScores"
  header-label="季度项目 \ 评估人员"
  total-label="季度合计"
/>
```

#### 年度评分
```vue
<AssessmentTable
  :projects="yearlyProjects"
  :members="yearlyMembers"
  :scores="yearlyScores"
  header-label="年度项目 \ 评估专家"
  total-label="年度总计"
/>
```

## 样式自定义

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<style>
/* 自定义表格容器 */
.assessment-table-container {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 自定义单元格样式 */
.score-cell {
  font-size: 16px;
}
</style>
```

## 注意事项

1. **数据响应性**: 确保传入的 `projects`、`members`、`scores` 都是响应式数据
2. **性能优化**: 大量数据时建议使用虚拟滚动或分页
3. **浏览器兼容**: sticky 定位需要现代浏览器支持
4. **移动端**: 在移动端会自动调整列宽和字体大小

## 方法

通过 `ref` 可以访问组件的方法：

```vue
<template>
  <AssessmentTable ref="tableRef" />
</template>

<script setup>
const tableRef = ref()

// 重新应用 sticky 定位
tableRef.value.applySticky()

// 获取指定项目和成员的分数
const score = tableRef.value.getScore(projectId, memberId)

// 计算指定成员的总分
const total = tableRef.value.calculateMemberTotal(memberId)
</script>
```
