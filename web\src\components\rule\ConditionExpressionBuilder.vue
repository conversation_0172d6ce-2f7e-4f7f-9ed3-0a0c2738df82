<template>
  <div class="condition-expression-builder">
    <div class="expression-input">
      <el-input
        v-model="expression"
        type="textarea"
        :rows="3"
        placeholder="请输入条件表达式，如：projectScore > 80 && departmentScore != null"
        @input="updateExpression"
      />
    </div>
    
    <div class="helper-tools">
      <div class="available-parameters">
        <span class="label">可用参数：</span>
        <el-tag 
          v-for="param in parameters" 
          :key="param.name"
          size="small"
          class="param-tag"
          @click="insertParameter(param.name)"
        >
          {{ param.name }}
        </el-tag>
      </div>
      
      <div class="operators">
        <span class="label">常用操作符：</span>
        <el-button-group>
          <el-button size="small" @click="insertOperator('==')">==(等于)</el-button>
          <el-button size="small" @click="insertOperator('!=')">==(不等于)</el-button>
          <el-button size="small" @click="insertOperator('>')">&gt;(大于)</el-button>
          <el-button size="small" @click="insertOperator('<')">&lt;(小于)</el-button>
          <el-button size="small" @click="insertOperator('>=')">&gt;=(大于等于)</el-button>
          <el-button size="small" @click="insertOperator('<=')">&lt;=(小于等于)</el-button>
        </el-button-group>
      </div>
      
      <div class="logic-operators">
        <span class="label">逻辑操作符：</span>
        <el-button-group>
          <el-button size="small" @click="insertOperator(' && ')">&&(且)</el-button>
          <el-button size="small" @click="insertOperator(' || ')">||(或)</el-button>
          <el-button size="small" @click="insertOperator('!')">!(非)</el-button>
        </el-button-group>
      </div>
      
      <div class="functions">
        <span class="label">常用函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('isNull', 'param')">isNull()</el-button>
          <el-button size="small" @click="insertFunction('isNotNull', 'param')">isNotNull()</el-button>
          <el-button size="small" @click="insertFunction('contains', 'array, value')">contains()</el-button>
          <el-button size="small" @click="insertFunction('length', 'param')">length()</el-button>
        </el-button-group>
      </div>

      <div class="aggregate-functions">
        <span class="label">聚合函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('SUM', 'array')">SUM()</el-button>
          <el-button size="small" @click="insertFunction('AVG', 'array')">AVG()</el-button>
          <el-button size="small" @click="insertFunction('MAX', 'array')">MAX()</el-button>
          <el-button size="small" @click="insertFunction('MIN', 'array')">MIN()</el-button>
          <el-button size="small" @click="insertFunction('COUNT', 'array')">COUNT()</el-button>
        </el-button-group>
      </div>

      <div class="math-functions">
        <span class="label">数学函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('ROUND', 'number, decimals')">ROUND()</el-button>
          <el-button size="small" @click="insertFunction('ABS', 'number')">ABS()</el-button>
          <el-button size="small" @click="insertFunction('SQRT', 'number')">SQRT()</el-button>
          <el-button size="small" @click="insertFunction('POW', 'base, exponent')">POW()</el-button>
        </el-button-group>
      </div>

      <div class="conditional-functions">
        <span class="label">条件函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('IF', 'condition, trueValue, falseValue')">IF()</el-button>
          <el-button size="small" @click="insertFunction('CASE', 'value, case1, result1, case2, result2, default')">CASE()</el-button>
        </el-button-group>
      </div>

      <div class="business-functions">
        <span class="label">业务函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('CALCULATE_PROJECT_AVG', 'project_data, department_manager_score')">CALCULATE_PROJECT_AVG()</el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="expression-validation" v-if="validationResult && !validationResult.isValid">
      <el-alert
        type="error"
        title="表达式语法错误"
        :description="validationResult.message"
        show-icon
        :closable="false"
      />
    </div>
    
    <div class="examples">
      <el-divider content-position="left">示例</el-divider>
      <div class="example-list">
        <div class="example-item" @click="useExample('projectScore > 80')">
          <code>projectScore > 80</code>
          <span class="desc">- 项目评分大于80</span>
        </div>
        <div class="example-item" @click="useExample('projectScore != null && departmentScore != null')">
          <code>projectScore != null && departmentScore != null</code>
          <span class="desc">- 同时存在项目评分和部门评分</span>
        </div>
        <div class="example-item" @click="useExample('userLevel == &quot;A&quot; || score >= 95')">
          <code>userLevel == "A" || score >= 95</code>
          <span class="desc">- 用户等级为A或评分大于等于95</span>
        </div>
        <div class="example-item" @click="useExample('AVG(projectScores) > 85')">
          <code>AVG(projectScores) > 85</code>
          <span class="desc">- 项目评分平均值大于85</span>
        </div>
        <div class="example-item" @click="useExample('COUNT(completedProjects) >= 3')">
          <code>COUNT(completedProjects) >= 3</code>
          <span class="desc">- 完成项目数量大于等于3个</span>
        </div>
        <div class="example-item" @click="useExample('MAX(scores) >= 95 && MIN(scores) >= 80')">
          <code>MAX(scores) >= 95 && MIN(scores) >= 80</code>
          <span class="desc">- 最高分不低于95且最低分不低于80</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  parameters: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const expression = ref(props.modelValue)
const validationResult = ref(null)

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  expression.value = newVal
})

// 更新表达式
const updateExpression = () => {
  emit('update:modelValue', expression.value)
  validateExpression()
}

// 插入参数
const insertParameter = (paramName) => {
  const textarea = document.querySelector('.condition-expression-builder textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const before = expression.value.substring(0, start)
    const after = expression.value.substring(end)
    expression.value = before + paramName + after
    
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + paramName.length, start + paramName.length)
    })
    
    updateExpression()
  }
}

// 插入操作符
const insertOperator = (operator) => {
  const textarea = document.querySelector('.condition-expression-builder textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const before = expression.value.substring(0, start)
    const after = expression.value.substring(end)
    expression.value = before + operator + after
    
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + operator.length, start + operator.length)
    })
    
    updateExpression()
  }
}

// 插入函数
const insertFunction = (funcName, params) => {
  const functionText = `${funcName}(${params})`
  insertOperator(functionText)
}

// 使用示例
const useExample = (exampleExpression) => {
  expression.value = exampleExpression
  updateExpression()
}

// 验证表达式
const validateExpression = () => {
  if (!expression.value.trim()) {
    validationResult.value = null
    return
  }
  
  try {
    // 简单的语法检查
    const expr = expression.value
    
    // 检查括号匹配
    let parenthesesCount = 0
    for (let char of expr) {
      if (char === '(') parenthesesCount++
      if (char === ')') parenthesesCount--
      if (parenthesesCount < 0) {
        throw new Error('括号不匹配')
      }
    }
    if (parenthesesCount !== 0) {
      throw new Error('括号不匹配')
    }
    
    // 检查引号匹配
    let quoteCount = 0
    for (let char of expr) {
      if (char === '"') quoteCount++
    }
    if (quoteCount % 2 !== 0) {
      throw new Error('引号不匹配')
    }
    
    validationResult.value = {
      isValid: true,
      message: '表达式语法检查通过'
    }
  } catch (error) {
    validationResult.value = {
      isValid: false,
      message: error.message
    }
  }
}
</script>

<style lang="scss" scoped>
.condition-expression-builder {
  .expression-input {
    margin-bottom: 16px;
  }
  
  .helper-tools {
    margin-bottom: 16px;
    
    > div {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      
      .label {
        font-weight: 600;
        font-size: 12px;
        color: #606266;
        min-width: 80px;
      }
      
      .param-tag {
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
      }
    }
  }
  
  .expression-validation {
    margin-bottom: 16px;
  }
  
  .examples {
    .example-list {
      .example-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: #e9ecef;
        }
        
        code {
          color: #e83e8c;
          background: transparent;
          padding: 0;
          font-size: 12px;
        }
        
        .desc {
          color: #6c757d;
          font-size: 12px;
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
