<template>
  <div class="formula-editor">
    <div class="editor-input">
      <el-input
        v-model="formula"
        type="textarea"
        :rows="4"
        placeholder="请输入计算公式，如：SUM(projectScores) * 0.4 + AVG(departmentScores) * 0.6"
        @input="updateFormula"
      />
    </div>
    
    <div class="helper-tools">
      <div class="available-parameters">
        <span class="label">可用参数：</span>
        <el-tag 
          v-for="param in parameters" 
          :key="param.name"
          size="small"
          class="param-tag"
          @click="insertParameter(param.name)"
        >
          {{ param.name }} ({{ param.type }})
        </el-tag>
      </div>
      
      <div class="operators">
        <span class="label">运算符：</span>
        <el-button-group>
          <el-button size="small" @click="insertOperator(' + ')">+</el-button>
          <el-button size="small" @click="insertOperator(' - ')">-</el-button>
          <el-button size="small" @click="insertOperator(' * ')">×</el-button>
          <el-button size="small" @click="insertOperator(' / ')">÷</el-button>
          <el-button size="small" @click="insertOperator(' ^ ')">^</el-button>
          <el-button size="small" @click="insertOperator('(')">(</el-button>
          <el-button size="small" @click="insertOperator(')')">)</el-button>
        </el-button-group>
      </div>
      
      <div class="aggregate-functions">
        <span class="label">聚合函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('SUM', 'array')">SUM()</el-button>
          <el-button size="small" @click="insertFunction('AVG', 'array')">AVG()</el-button>
          <el-button size="small" @click="insertFunction('MAX', 'array')">MAX()</el-button>
          <el-button size="small" @click="insertFunction('MIN', 'array')">MIN()</el-button>
          <el-button size="small" @click="insertFunction('COUNT', 'array')">COUNT()</el-button>
        </el-button-group>
      </div>

      <div class="business-functions">
        <span class="label">业务函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('CALCULATE_PROJECT_AVG', 'project_data, department_manager_score')">CALCULATE_PROJECT_AVG()</el-button>
        </el-button-group>
      </div>
      
      <div class="math-functions">
        <span class="label">数学函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('ROUND', 'number, decimals')">ROUND()</el-button>
          <el-button size="small" @click="insertFunction('ABS', 'number')">ABS()</el-button>
          <el-button size="small" @click="insertFunction('SQRT', 'number')">SQRT()</el-button>
          <el-button size="small" @click="insertFunction('POW', 'base, exponent')">POW()</el-button>
          <el-button size="small" @click="insertFunction('CEIL', 'number')">CEIL()</el-button>
          <el-button size="small" @click="insertFunction('FLOOR', 'number')">FLOOR()</el-button>
        </el-button-group>
      </div>
      
      <div class="conditional-functions">
        <span class="label">条件函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('IF', 'condition, trueValue, falseValue')">IF()</el-button>
          <el-button size="small" @click="insertFunction('CASE', 'value, case1, result1, default')">CASE()</el-button>
        </el-button-group>
      </div>
      
      <div class="string-functions">
        <span class="label">字符串函数：</span>
        <el-button-group>
          <el-button size="small" @click="insertFunction('CONCAT', 'str1, str2')">CONCAT()</el-button>
          <el-button size="small" @click="insertFunction('LENGTH', 'string')">LENGTH()</el-button>
          <el-button size="small" @click="insertFunction('UPPER', 'string')">UPPER()</el-button>
          <el-button size="small" @click="insertFunction('LOWER', 'string')">LOWER()</el-button>
        </el-button-group>
      </div>
    </div>
    

    
    <div class="examples">
      <el-divider content-position="left">公式示例</el-divider>
      <div class="example-list">
        <div class="example-item" @click="useExample('projectScore * 0.4 + departmentScore * 0.6')">
          <code>projectScore * 0.4 + departmentScore * 0.6</code>
          <span class="desc">- 加权平均计算</span>
        </div>
        <div class="example-item" @click="useExample('SUM(projectScores) / COUNT(projectScores)')">
          <code>SUM(projectScores) / COUNT(projectScores)</code>
          <span class="desc">- 手动计算平均值</span>
        </div>
        <div class="example-item" @click="useExample('IF(score >= 90, score * 1.2, score)')">
          <code>IF(score >= 90, score * 1.2, score)</code>
          <span class="desc">- 条件加分</span>
        </div>
        <div class="example-item" @click="useExample('ROUND(AVG(scores) * coefficient, 2)')">
          <code>ROUND(AVG(scores) * coefficient, 2)</code>
          <span class="desc">- 平均分乘以系数并保留2位小数</span>
        </div>
        <div class="example-item" @click="useExample('MAX(projectScore, departmentScore) * 0.8 + MIN(projectScore, departmentScore) * 0.2')">
          <code>MAX(projectScore, departmentScore) * 0.8 + MIN(projectScore, departmentScore) * 0.2</code>
          <span class="desc">- 高分权重大，低分权重小</span>
        </div>
        <div class="example-item" @click="useExample('department_manager_score * 0.6 + CALCULATE_PROJECT_AVG(project_data, department_manager_score) * 0.4')">
          <code>department_manager_score * 0.6 + CALCULATE_PROJECT_AVG(project_data, department_manager_score) * 0.4</code>
          <span class="desc">- 项目负责人评分替代计算</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  parameters: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const formula = ref(props.modelValue)
const validationResult = ref(null)

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  formula.value = newVal
})

// 更新公式
const updateFormula = () => {
  emit('update:modelValue', formula.value)
}

// 插入参数
const insertParameter = (paramName) => {
  const textarea = document.querySelector('.formula-editor textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const before = formula.value.substring(0, start)
    const after = formula.value.substring(end)
    formula.value = before + paramName + after
    
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + paramName.length, start + paramName.length)
    })
    
    updateFormula()
  }
}

// 插入操作符
const insertOperator = (operator) => {
  const textarea = document.querySelector('.formula-editor textarea')
  if (textarea) {
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const before = formula.value.substring(0, start)
    const after = formula.value.substring(end)
    formula.value = before + operator + after
    
    nextTick(() => {
      textarea.focus()
      textarea.setSelectionRange(start + operator.length, start + operator.length)
    })
    
    updateFormula()
  }
}

// 插入函数
const insertFunction = (funcName, params) => {
  const functionText = `${funcName}(${params})`
  insertOperator(functionText)
}

// 使用示例
const useExample = (exampleFormula) => {
  formula.value = exampleFormula
  updateFormula()
}

// 验证公式
const validateFormula = () => {
  if (!formula.value.trim()) {
    validationResult.value = null
    return
  }
  
  try {
    // 基础语法检查
    const expr = formula.value
    
    // 检查括号匹配
    let parenthesesCount = 0
    for (let char of expr) {
      if (char === '(') parenthesesCount++
      if (char === ')') parenthesesCount--
      if (parenthesesCount < 0) {
        throw new Error('括号不匹配')
      }
    }
    if (parenthesesCount !== 0) {
      throw new Error('括号不匹配')
    }
    
    // 检查函数语法
    const functionPattern = /([A-Z_]+)\s*\(/g
    const functions = ['SUM', 'AVG', 'MAX', 'MIN', 'COUNT', 'ROUND', 'ABS', 'SQRT', 'POW', 'CEIL', 'FLOOR', 'IF', 'CASE', 'CONCAT', 'LENGTH', 'UPPER', 'LOWER', 'CALCULATE_PROJECT_AVG']
    let match
    while ((match = functionPattern.exec(expr)) !== null) {
      const funcName = match[1]
      if (!functions.includes(funcName)) {
        throw new Error(`未知函数: ${funcName}`)
      }
    }
    
    validationResult.value = {
      isValid: true,
      message: '公式语法检查通过'
    }
  } catch (error) {
    validationResult.value = {
      isValid: false,
      message: error.message
    }
  }
}
</script>

<style lang="scss" scoped>
.formula-editor {
  .editor-input {
    margin-bottom: 16px;
  }
  
  .helper-tools {
    margin-bottom: 16px;
    
    > div {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      
      .label {
        font-weight: 600;
        font-size: 12px;
        color: #606266;
        min-width: 80px;
      }
      
      .param-tag {
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
      }
    }
  }
  
  .formula-validation {
    margin-bottom: 16px;
  }
  
  .examples {
    .example-list {
      .example-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: #e9ecef;
        }
        
        code {
          color: #e83e8c;
          background: transparent;
          padding: 0;
          font-size: 12px;
          display: block;
          margin-bottom: 4px;
        }
        
        .desc {
          color: #6c757d;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
