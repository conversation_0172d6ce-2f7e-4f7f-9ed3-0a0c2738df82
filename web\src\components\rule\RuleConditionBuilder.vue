<template>
  <div class="rule-condition-builder">
    <div class="condition-header">
      <span class="title">规则条件配置</span>
      <el-button type="primary" size="small" @click="addCondition">
        <el-icon><Plus /></el-icon>
        添加条件
      </el-button>
    </div>
    
    <div v-if="conditions.length === 0" class="empty-state">
      <el-empty description="暂无条件，请添加规则条件" />
    </div>
    
    <div v-else class="condition-list">
      <el-card 
        v-for="(condition, index) in conditions" 
        :key="condition.id"
        class="condition-card"
        shadow="hover"
      >
        <template #header>
          <div class="condition-header">
            <div class="header-left">
              <el-tag type="primary" size="small">条件 {{ index + 1 }}</el-tag>
              <span class="condition-name">{{ condition.name || '未命名条件' }}</span>
            </div>
            <div class="header-right">
              <el-button 
                type="danger" 
                size="small" 
                @click="removeCondition(index)"
              >
                删除
              </el-button>
            </div>
          </div>
        </template>
        
        <el-form label-width="100px" size="small">
          <el-form-item label="条件名称">
            <el-input 
              v-model="condition.name" 
              placeholder="请输入条件名称"
              @input="updateConditions"
            />
          </el-form-item>
          
          <el-form-item label="触发条件">
            <ConditionExpressionBuilder 
              v-model="condition.when"
              :parameters="parameters"
              @update:modelValue="updateConditions"
            />
          </el-form-item>
          
          <el-form-item label="计算公式">
            <FormulaEditor
              v-model="condition.then.formula"
              :parameters="parameters"
              @update:modelValue="updateConditions"
            />
          </el-form-item>
          
          <el-form-item label="优先级">
            <el-input-number 
              v-model="condition.priority" 
              :min="1" 
              :max="100"
              placeholder="数字越大优先级越高"
              @change="updateConditions"
            />
            <div class="priority-help">
              <el-text size="small" type="info">
                优先级高的条件会优先匹配执行
              </el-text>
            </div>
          </el-form-item>
          
          <el-form-item label="条件描述">
            <el-input 
              v-model="condition.description"
              type="textarea"
              :rows="2"
              placeholder="请输入条件描述（可选）"
              @input="updateConditions"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <div class="default-value-config" v-if="conditions.length > 0">
      <el-divider content-position="left">默认值配置</el-divider>
      <el-form-item label="默认返回值">
        <el-input-number 
          v-model="defaultValue" 
          placeholder="当所有条件都不匹配时的默认返回值"
          @change="updateDefaultValue"
        />
        <div class="default-help">
          <el-text size="small" type="info">
            当所有条件都不匹配时，将返回此默认值
          </el-text>
        </div>
      </el-form-item>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import ConditionExpressionBuilder from './ConditionExpressionBuilder.vue'
import FormulaEditor from './FormulaEditor.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  parameters: {
    type: Array,
    default: () => []
  },
  defaultValue: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue', 'update:defaultValue'])

const conditions = ref([...props.modelValue])
const defaultValue = ref(props.defaultValue)

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  conditions.value = [...newVal]
}, { deep: true })

watch(() => props.defaultValue, (newVal) => {
  defaultValue.value = newVal
})

// 监听参数变化，确保条件构建器能获取到最新的参数列表
watch(() => props.parameters, (newVal) => {
  // 参数变化时不需要特殊处理，组件会自动响应
}, { deep: true })

// 生成唯一ID
const generateId = () => {
  return 'condition_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 添加条件
const addCondition = () => {
  const newCondition = {
    id: generateId(),
    name: '',
    when: '',
    then: {
      formula: ''
    },
    priority: conditions.value.length + 1,
    description: ''
  }
  conditions.value.push(newCondition)
  updateConditions()
}

// 删除条件
const removeCondition = (index) => {
  conditions.value.splice(index, 1)
  // 重新调整优先级
  conditions.value.forEach((condition, idx) => {
    condition.priority = idx + 1
  })
  updateConditions()
}

// 更新条件数据
const updateConditions = () => {
  emit('update:modelValue', conditions.value)
}

// 更新默认值
const updateDefaultValue = () => {
  emit('update:defaultValue', defaultValue.value)
}
</script>

<style lang="scss" scoped>
.rule-condition-builder {
  .condition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .title {
      font-weight: 600;
      font-size: 14px;
    }
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .condition-name {
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .empty-state {
    padding: 20px 0;
  }
  
  .condition-list {
    .condition-card {
      margin-bottom: 16px;
      
      .formula-help,
      .priority-help,
      .default-help {
        margin-top: 4px;
      }
    }
  }
  
  .default-value-config {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
  }
}
</style>
