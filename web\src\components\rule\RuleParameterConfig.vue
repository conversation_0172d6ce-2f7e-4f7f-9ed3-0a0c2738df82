<template>
  <div class="rule-parameter-config">
    <div class="parameter-header">
      <span class="title">输入参数配置</span>
      <el-button type="primary" size="small" @click="addParameter">
        <el-icon><Plus /></el-icon>
        添加参数
      </el-button>
    </div>
    
    <div v-if="parameters.length === 0" class="empty-state">
      <el-empty description="暂无参数，请添加输入参数" />
    </div>
    
    <div v-else class="parameter-list">
      <el-table :data="parameters" style="width: 100%">
        <el-table-column label="参数名" width="150">
          <template #default="scope">
            <el-input 
              v-model="scope.row.name" 
              placeholder="参数名"
              @input="updateParameters"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="类型" width="120">
          <template #default="scope">
            <el-select 
              v-model="scope.row.type" 
              placeholder="类型"
              @change="updateParameters"
            >
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔" value="boolean" />
              <el-option label="数组" value="array" />
              <el-option label="对象" value="object" />
            </el-select>
          </template>
        </el-table-column>
        
        <el-table-column label="必填" width="80">
          <template #default="scope">
            <el-checkbox 
              v-model="scope.row.required"
              @change="updateParameters"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="默认值" width="120">
          <template #default="scope">
            <el-input 
              v-model="scope.row.defaultValue" 
              placeholder="默认值"
              @input="updateParameters"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="描述" min-width="150">
          <template #default="scope">
            <el-input 
              v-model="scope.row.description" 
              placeholder="参数描述"
              @input="updateParameters"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button 
              type="danger" 
              size="small" 
              @click="removeParameter(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div class="parameter-preview" v-if="parameters.length > 0">
      <el-divider content-position="left">参数预览</el-divider>
      <div class="preview-content">
        <el-tag 
          v-for="param in parameters" 
          :key="param.name"
          :type="param.required ? 'danger' : 'info'"
          class="param-tag"
        >
          {{ param.name }} ({{ param.type }})
          <span v-if="param.required" class="required-mark">*</span>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  availableParameters: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const parameters = ref([...props.modelValue])

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  parameters.value = [...newVal]
}, { deep: true })

// 添加参数
const addParameter = () => {
  const newParam = {
    name: '',
    type: 'string',
    required: false,
    defaultValue: '',
    description: ''
  }
  parameters.value.push(newParam)
  updateParameters()
}

// 删除参数
const removeParameter = (index) => {
  parameters.value.splice(index, 1)
  updateParameters()
}

// 更新参数数据
const updateParameters = () => {
  emit('update:modelValue', parameters.value)
}
</script>

<style lang="scss" scoped>
.rule-parameter-config {
  .parameter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .title {
      font-weight: 600;
      font-size: 14px;
    }
  }
  
  .empty-state {
    padding: 20px 0;
  }
  
  .parameter-list {
    margin-bottom: 16px;
  }
  
  .parameter-preview {
    .preview-content {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .param-tag {
        position: relative;
        
        .required-mark {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
