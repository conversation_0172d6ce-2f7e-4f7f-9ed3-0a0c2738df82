<template>
  <div class="rule-test-panel">
    <el-card>
      <template #header>
        <div class="test-header">
          <span class="title">规则测试</span>
          <el-button
            type="primary"
            size="small"
            @click="executeTest"
            :loading="testing"
          >
            <el-icon><CaretRight /></el-icon>
            执行测试
          </el-button>
        </div>
      </template>
      
      <div class="test-content">
        <el-form label-width="100px" size="small">
          <el-form-item label="测试数据">
            <el-input 
              v-model="testDataJson"
              type="textarea"
              :rows="6"
              placeholder='请输入测试数据（JSON格式）：
{
  "username": "zhangsan",
  "projectScore": 85,
  "departmentScore": 90,
  "coefficient": 1.2
}'
              @input="validateTestData"
            />
            <div v-if="testDataValidation" class="validation-message">
              <el-text 
                :type="testDataValidation.isValid ? 'success' : 'danger'"
                size="small"
              >
                {{ testDataValidation.message }}
              </el-text>
            </div>
          </el-form-item>
          
          <el-form-item label="快速填充">
            <div class="quick-fill-buttons">
              <el-button 
                size="small" 
                @click="fillSampleData('basic')"
              >
                基础数据
              </el-button>
              <el-button 
                size="small" 
                @click="fillSampleData('complete')"
              >
                完整数据
              </el-button>
              <el-button 
                size="small" 
                @click="fillSampleData('edge')"
              >
                边界数据
              </el-button>
              <el-button 
                size="small" 
                @click="clearTestData"
              >
                清空
              </el-button>
            </div>
          </el-form-item>
        </el-form>
        
        <div v-if="testResult" class="test-result">
          <el-divider content-position="left">测试结果</el-divider>
          
          <el-alert 
            :type="testResult.success ? 'success' : 'error'"
            :title="testResult.success ? '测试执行成功' : '测试执行失败'"
            show-icon
            :closable="false"
          >
            <template #default>
              <div v-if="testResult.success" class="success-result">
                <div class="result-item">
                  <strong>计算结果：</strong>
                  <el-tag type="success" size="large">{{ testResult.result }}</el-tag>
                </div>
                <div class="result-item" v-if="testResult.appliedCondition">
                  <strong>匹配条件：</strong>
                  <el-tag type="info">{{ testResult.appliedCondition }}</el-tag>
                </div>
                <div class="result-item" v-if="testResult.executionTime">
                  <strong>执行时间：</strong>
                  <el-tag type="warning">{{ testResult.executionTime }}ms</el-tag>
                </div>
                
                <!-- 执行详情 -->
                <div v-if="testResult.trace && testResult.trace.length > 0" class="execution-trace">
                  <el-divider content-position="left">执行详情</el-divider>
                  <el-timeline>
                    <el-timeline-item
                      v-for="(step, index) in testResult.trace"
                      :key="index"
                      :type="step.success ? 'success' : 'danger'"
                      :icon="step.success ? 'Check' : 'Close'"
                    >
                      <div class="trace-step">
                        <div class="step-title">{{ step.step }}</div>
                        <div class="step-detail" v-if="step.detail">{{ step.detail }}</div>
                        <div class="step-result" v-if="step.result !== undefined">
                          结果: <code>{{ step.result }}</code>
                        </div>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
              
              <div v-else class="error-result">
                <div class="error-message">
                  <strong>错误信息：</strong>{{ testResult.error }}
                </div>
                <div v-if="testResult.details" class="error-details">
                  <strong>详细信息：</strong>{{ testResult.details }}
                </div>
              </div>
            </template>
          </el-alert>
        </div>
        
        <!-- 参数提示 -->
        <div v-if="ruleConfig && ruleConfig.inputParameters" class="parameter-hints">
          <el-divider content-position="left">参数说明</el-divider>
          <div class="parameter-list">
            <div 
              v-for="param in ruleConfig.inputParameters" 
              :key="param.name"
              class="parameter-item"
            >
              <el-tag 
                :type="param.required ? 'danger' : 'info'"
                size="small"
              >
                {{ param.name }}
              </el-tag>
              <span class="param-type">({{ param.type }})</span>
              <span class="param-desc" v-if="param.description">- {{ param.description }}</span>
              <span class="required-mark" v-if="param.required">*必填</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CaretRight } from '@element-plus/icons-vue'
import { testRule } from '@/api/assessment/calculationMethods'

const props = defineProps({
  ruleConfig: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['test-result'])

const testing = ref(false)
const testDataJson = ref('')
const testDataValidation = ref(null)
const testResult = ref(null)

// 监听规则配置变化，清空测试结果
watch(() => props.ruleConfig, () => {
  testResult.value = null
}, { deep: true })

// 验证测试数据
const validateTestData = () => {
  if (!testDataJson.value.trim()) {
    testDataValidation.value = null
    return
  }
  
  try {
    JSON.parse(testDataJson.value)
    testDataValidation.value = {
      isValid: true,
      message: 'JSON格式正确'
    }
  } catch (error) {
    testDataValidation.value = {
      isValid: false,
      message: `JSON格式错误: ${error.message}`
    }
  }
}

// 填充示例数据
const fillSampleData = (type) => {
  const sampleData = {
    basic: {
      department_manager_score: 90,
      project_data: [
        {
          project_manager_score: 85,
          project_participation: 0.8,
          is_leader_of_this_project: false
        },
        {
          project_manager_score: 88,
          project_participation: 0.6,
          is_leader_of_this_project: true
        }
      ]
    },
    complete: {
      department_manager_score: 90,
      project_data: [
        {
          project_manager_score: 85,
          project_participation: 0.8,
          is_leader_of_this_project: false
        },
        {
          project_manager_score: 88,
          project_participation: 0.6,
          is_leader_of_this_project: true
        },
        {
          project_manager_score: 92,
          project_participation: 0.9,
          is_leader_of_this_project: false
        }
      ]
    },
    edge: {
      department_manager_score: 95,
      project_data: [
        {
          project_manager_score: 90,
          project_participation: 1.0,
          is_leader_of_this_project: true
        }
      ]
    }
  }

  testDataJson.value = JSON.stringify(sampleData[type], null, 2)
  validateTestData()
}

// 清空测试数据
const clearTestData = () => {
  testDataJson.value = ''
  testDataValidation.value = null
  testResult.value = null
}

// 执行测试
const executeTest = async () => {
  if (!testDataJson.value.trim()) {
    ElMessage.warning('请输入测试数据')
    return
  }

  if (!testDataValidation.value?.isValid) {
    ElMessage.error('测试数据格式不正确')
    return
  }

  if (!props.ruleConfig || !props.ruleConfig.conditions || props.ruleConfig.conditions.length === 0) {
    ElMessage.warning('请先配置规则条件')
    return
  }

  testing.value = true

  try {
    const testData = JSON.parse(testDataJson.value)

    // 调用后端API进行规则测试
    const response = await testRule({
      ruleConfig: props.ruleConfig,
      testData: testData
    })

    if (response.code === 0) {
      testResult.value = response.data
      emit('test-result', response.data)
    } else {
      testResult.value = {
        success: false,
        error: response.msg || '测试失败'
      }
    }

  } catch (error) {
    console.error('规则测试失败:', error)
    testResult.value = {
      success: false,
      error: error.message || '网络请求失败',
      details: error.response?.data?.msg
    }
  } finally {
    testing.value = false
  }
}

// 模拟规则执行（实际应该调用后端API）
const simulateRuleExecution = async (ruleConfig, testData) => {
  const startTime = Date.now()
  const trace = []
  
  try {
    // 1. 参数验证
    trace.push({
      step: '参数验证',
      success: true,
      detail: `接收到 ${Object.keys(testData).length} 个参数`,
      result: 'PASS'
    })
    
    // 2. 条件匹配
    let matchedCondition = null
    let result = ruleConfig.defaultValue || 0
    
    // 按优先级排序条件
    const sortedConditions = [...ruleConfig.conditions].sort((a, b) => (b.priority || 0) - (a.priority || 0))
    
    for (const condition of sortedConditions) {
      trace.push({
        step: `条件评估: ${condition.name || condition.id}`,
        detail: `条件表达式: ${condition.when}`,
        success: true
      })
      
      // 简单的条件评估模拟
      if (evaluateCondition(condition.when, testData)) {
        matchedCondition = condition
        
        // 计算公式
        result = evaluateFormula(condition.then.formula, testData)
        
        trace.push({
          step: '公式计算',
          detail: `公式: ${condition.then.formula}`,
          result: result,
          success: true
        })
        
        break
      }
    }
    
    if (!matchedCondition) {
      trace.push({
        step: '使用默认值',
        detail: '没有匹配的条件，使用默认值',
        result: result,
        success: true
      })
    }
    
    const executionTime = Date.now() - startTime
    
    return {
      success: true,
      result: result,
      appliedCondition: matchedCondition?.name || '默认值',
      executionTime: executionTime,
      trace: trace
    }
    
  } catch (error) {
    return {
      success: false,
      error: error.message,
      trace: trace
    }
  }
}

// 简单的条件评估（实际应该使用更完善的表达式解析器）
const evaluateCondition = (condition, data) => {
  try {
    // 替换参数值
    let expr = condition
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`\\b${key}\\b`, 'g')
      if (value === null || value === undefined) {
        expr = expr.replace(regex, 'null')
      } else if (typeof value === 'string') {
        expr = expr.replace(regex, `"${value}"`)
      } else {
        expr = expr.replace(regex, value.toString())
      }
    }
    
    // 简单的表达式评估
    expr = expr.replace(/!=/g, '!==').replace(/==/g, '===')
    return Function(`"use strict"; return (${expr})`)()
  } catch (error) {
    console.error('条件评估错误:', error)
    return false
  }
}

// 简单的公式计算（实际应该使用更完善的公式解析器）
const evaluateFormula = (formula, data) => {
  try {
    let expr = formula
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`\\b${key}\\b`, 'g')
      expr = expr.replace(regex, value || 0)
    }
    
    return Function(`"use strict"; return (${expr})`)()
  } catch (error) {
    console.error('公式计算错误:', error)
    return 0
  }
}
</script>

<style lang="scss" scoped>
.rule-test-panel {
  .test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      font-weight: 600;
      font-size: 14px;
    }
  }
  
  .test-content {
    .validation-message {
      margin-top: 4px;
    }
    
    .quick-fill-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .test-result {
      margin-top: 20px;
      
      .success-result {
        .result-item {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .execution-trace {
          margin-top: 16px;
          
          .trace-step {
            .step-title {
              font-weight: 600;
              margin-bottom: 4px;
            }
            
            .step-detail {
              color: #606266;
              font-size: 12px;
              margin-bottom: 4px;
            }
            
            .step-result {
              font-size: 12px;
              
              code {
                background: #f1f2f6;
                padding: 2px 4px;
                border-radius: 2px;
                color: #e83e8c;
              }
            }
          }
        }
      }
      
      .error-result {
        .error-message,
        .error-details {
          margin-bottom: 8px;
        }
      }
    }
    
    .parameter-hints {
      margin-top: 20px;
      
      .parameter-list {
        .parameter-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          
          .param-type {
            color: #909399;
            font-size: 12px;
          }
          
          .param-desc {
            color: #606266;
            font-size: 12px;
          }
          
          .required-mark {
            color: #f56c6c;
            font-size: 12px;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
