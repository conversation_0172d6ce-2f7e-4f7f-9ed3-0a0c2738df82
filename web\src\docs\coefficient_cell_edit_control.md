# 考核系数分配单元格编辑权限控制

## 功能概述

本次改造为考核系数分配tab添加了单元格编辑权限控制，根据用户是否为项目的参与者或负责人来确定单元格的编辑状态。

## 主要改动

### 1. 模板修改

在考核系数分配表格的单元格模板中添加了条件判断：

```vue
<template #default="scope">
  <div
    v-if="isCoefficientCellEditable(scope.row.projectId, member.id)"
    class="coefficient-cell editable-cell"
    :contenteditable="true"
    @blur="updateTabScore(item.name, scope.row.projectId, member.id, $event)"
    @keydown="handleKeydown($event)"
    @paste="handlePaste($event)"
    @input="validateInput($event)"
    @mouseenter="highlightCell"
    @mouseleave="clearEmployeeHighlight"
  >
    {{ formatScoreDisplay(scope.row[`member_${member.id}`]) }}
  </div>
  <div
    v-else
    class="coefficient-cell non-editable-cell"
    :title="`${member.nickName || member.username} 不是项目 ${scope.row.projectName} 的参与者或负责人`"
  >
    -
  </div>
</template>
```

### 2. 权限判断函数

添加了以下核心函数来判断单元格编辑权限：

#### `isCoefficientCellEditable(projectId, userId)`
- 主要判断函数，根据项目ID和用户ID判断单元格是否可编辑
- 检查用户是否为项目负责人或项目成员

#### `getUserNameById(userId)`
- 根据用户ID获取用户名的辅助函数

#### `isUserProjectManager(projectId, userName)`
- 检查用户是否为指定项目的负责人
- 检查数据源：`managerProjects` 和 `adminData.orgMembers[].projects.asManager`

#### `isUserProjectMember(projectId, userName)`
- 检查用户是否为指定项目的成员
- 检查数据源：`managerProjects[].members` 和 `adminData.orgMembers[].projects.asMember`

### 3. 样式改进

添加了不可编辑单元格的专用样式：

```css
.coefficient-cell.non-editable-cell {
  background-color: #f5f7fa;
  color: #909399;
  cursor: not-allowed;
  border: none;
  min-height: 32px;
  line-height: 32px;
  padding: 4px 8px;
  text-align: center;
  font-weight: 500;
  border-radius: 4px;
  user-select: none;
}

.coefficient-cell.non-editable-cell:hover {
  background-color: #f5f7fa;
  color: #909399;
}
```

### 4. 业务逻辑优化

#### 合计计算优化
修改了 `calculateTabMemberTotal` 函数，只计算可编辑单元格的合计：

```javascript
tabData.projects.forEach(project => {
  // 只有可编辑的单元格才参与合计计算
  if (isCoefficientCellEditable(project.id, memberId)) {
    const score = tabData.scores[`${project.id}-${memberId}`] || 0
    total += parseFloat(score) || 0
  }
})
```

#### 校验逻辑优化
修改了 `validateCoefficientsSum` 函数，只校验可编辑单元格：

```javascript
tabData.projects.forEach(project => {
  // 只有可编辑的单元格才参与校验
  if (isCoefficientCellEditable(project.id, member.id)) {
    const key = `${project.id}-${member.id}`
    const score = tabData.scores[key] || 0
    memberTotal += parseFloat(score)
    // ...
  }
})
```

#### 提交数据优化
修改了 `prepareSubmitData` 函数，只提交可编辑单元格的数据：

```javascript
tabData.members.forEach(member => {
  tabData.projects.forEach(project => {
    // 只有可编辑的单元格才能提交
    if (isCoefficientCellEditable(project.id, member.id)) {
      // 处理提交逻辑
    }
  })
})
```

## 数据源说明

权限判断基于以下考核数据结构：

### 项目负责人数据
1. `assessmentData.managerProjects` - 当前用户作为负责人的项目
2. `assessmentData.adminData.orgMembers[].projects.asManager` - 组织成员作为负责人的项目

### 项目成员数据
1. `assessmentData.managerProjects[].members` - 项目的成员列表
2. `assessmentData.adminData.orgMembers[].projects.asMember` - 组织成员作为成员的项目

## 用户体验改进

1. **视觉区分**：不可编辑的单元格显示为灰色背景，内容显示为"-"
2. **鼠标提示**：悬停在不可编辑单元格上会显示提示信息
3. **交互禁用**：不可编辑单元格禁用了文本选择和编辑功能
4. **合计准确**：只有可编辑的单元格参与合计计算，确保数据准确性

## 兼容性说明

- 保持了原有的API接口不变
- 向后兼容现有的数据结构
- 不影响其他功能模块的正常运行

## 问题修复

### 提示信息显示问题
**问题**：不可编辑单元格的提示信息中成员姓名显示为 undefined

**原因**：模板中使用了错误的属性名 `member.nickName`，但实际member对象中使用的是 `member.name`

**修复**：
```vue
<!-- 修复前 -->
:title="`${member.nickName || member.username} 不是项目 ${scope.row.projectName} 的参与者或负责人`"

<!-- 修复后 -->
:title="`${member.name || member.userName} 不是项目 ${scope.row.projectName} 的参与者或负责人`"
```

**说明**：在 `extractMembersData` 函数中，member对象的结构为：
- `id`: member.userId
- `name`: member.nickName (来源于原始数据的nickName)
- `userName`: member.userName

## 测试建议

1. 测试不同角色用户的权限控制
2. 验证项目负责人和成员的编辑权限
3. 检查合计计算的准确性
4. 确认提交数据的正确性
5. 测试样式在不同浏览器下的表现
6. **验证提示信息显示正确的成员姓名**
