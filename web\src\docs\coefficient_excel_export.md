# 考核系数分配Excel导出功能

## 功能概述

重新设计了考核系数分配tab中的Excel导出功能，从原来的调用后端API改为导出当前前端表格的数据，支持权限控制和数据验证。

## 主要改动

### 1. 导出逻辑变更

**原来的实现**：
- 调用后端API `exportAssessmentCoefficients`
- 导出后端数据库中的数据
- 不考虑前端权限控制

**新的实现**：
- 导出当前前端表格显示的数据
- 支持权限控制（不可编辑单元格显示为"-"）
- 包含合计行和合计列
- 数据验证（无数据时不导出）

### 2. 核心函数

#### `exportTabToExcel(tabName)`
主导出函数，负责：
- 数据验证（检查项目和成员数据）
- 调用数据准备函数
- 创建Excel工作簿
- 设置列宽和样式
- 生成并下载文件

#### `prepareExcelData(tabName, tabData)`
数据准备函数，负责：
- 构建表头（项目名称、项目类型、成员列、合计列）
- 构建数据行（包含权限控制）
- 构建合计行
- 数据格式化（百分比显示）

### 3. Excel文件结构

#### 表头结构
```
| 项目名称 | 项目类型 | 张三 | 李四 | 王五 | 合计 |
```

#### 数据行结构
```
| 测试项目 | 项目 | 50.0% | 30.0% | - | 80.0% |
| 开发项目 | 项目 | 20.0% | - | 100.0% | 120.0% |
```

#### 合计行结构
```
| 合计 | | 70.0% | 30.0% | 100.0% | |
```

### 4. 权限控制

- **可编辑单元格**：显示实际的百分比数值（如：50.0%）
- **不可编辑单元格**：显示"-"符号
- **空值处理**：系数为0的可编辑单元格显示为空白
- **合计计算**：只计算可编辑单元格的数值

### 5. 数据验证

导出前进行以下验证：

```javascript
// 检查项目数据
if (!tabData.projects || tabData.projects.length === 0) {
  ElMessage.warning('当前表格没有项目数据，无法导出')
  return
}

// 检查成员数据
if (!tabData.members || tabData.members.length === 0) {
  ElMessage.warning('当前表格没有成员数据，无法导出')
  return
}
```

### 6. 文件命名规则

文件名格式：`考核系数分配_{tabName}_{日期}.xlsx`

示例：
- `考核系数分配_2024年第一季度考核_2024-01-15.xlsx`
- `考核系数分配_月度考核_2024-01-15.xlsx`

### 7. 列宽设置

- 项目名称列：20个字符宽度
- 项目类型列：12个字符宽度
- 成员列：15个字符宽度
- 合计列：15个字符宽度

## 使用场景

1. **数据备份**：导出当前配置的系数分配数据
2. **数据分析**：在Excel中进行进一步的数据分析
3. **报告生成**：作为考核报告的附件
4. **权限展示**：清楚显示哪些用户有权限编辑哪些项目

## 技术实现

### 依赖库
- `xlsx`：用于生成Excel文件

### 关键代码片段

```javascript
// 创建工作簿和工作表
const workbook = XLSX.utils.book_new()
const worksheet = XLSX.utils.aoa_to_sheet(excelData)

// 设置列宽
worksheet['!cols'] = colWidths

// 生成Excel文件
const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
const blob = new Blob([excelBuffer], {
  type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
})
```

## 用户体验改进

1. **即时反馈**：导出前显示"正在生成Excel文件..."提示
2. **错误处理**：数据不足时给出明确的警告信息
3. **成功提示**：导出成功后显示确认消息
4. **文件下载**：自动触发浏览器下载
5. **权限可视化**：在Excel中清楚区分可编辑和不可编辑的数据

## 兼容性说明

- 支持现代浏览器的文件下载功能
- Excel文件兼容Microsoft Excel 2007+
- 支持WPS Office等第三方办公软件

## 测试建议

1. 测试有数据时的正常导出
2. 测试无项目数据时的警告提示
3. 测试无成员数据时的警告提示
4. 验证权限控制在Excel中的正确显示
5. 检查合计计算的准确性
6. 测试文件下载和打开功能
