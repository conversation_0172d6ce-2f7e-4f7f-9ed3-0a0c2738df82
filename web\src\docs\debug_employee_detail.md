# 员工详情查看功能调试说明

## 调试信息输出

在员工评分页面点击"查看详情"按钮时，系统会在浏览器控制台输出详细的调试信息。

## 如何查看控制台输出

### 1. 打开浏览器开发者工具
- **Chrome/Edge**: 按 `F12` 或 `Ctrl+Shift+I`
- **Firefox**: 按 `F12` 或 `Ctrl+Shift+I`
- **Safari**: 按 `Cmd+Option+I` (Mac)

### 2. 切换到 Console 标签页
在开发者工具中找到并点击 "Console" 标签页

### 3. 执行操作
1. 进入项目评分页面
2. 切换到"员工评分"标签页
3. 点击任意员工行的"查看详情"按钮
4. 观察控制台输出

## 输出信息说明

### 🚀 开始阶段
```
🚀 查看详情 - 开始获取用户详细信息
🚀 查看详情 - 用户行数据: {用户的完整行数据}
🚀 查看详情 - 用户名: {用户名}
🚀 查看详情 - 当前tab名称: {当前活跃的tab名称}
🚀 查看详情 - 当前tab信息: {当前tab的完整信息}
🚀 查看详情 - 当前考核配置ID: {当前考核配置的ID}
🚀 查看详情 - API调用参数: {传递给API的完整参数}
```

### 🔍 API响应阶段
```
🔍 查看详情 - 后端返回数据: {完整的API响应}
🔍 查看详情 - 用户名: {用户名}
🔍 查看详情 - 响应状态码: {状态码，0表示成功}
🔍 查看详情 - 响应消息: {响应消息}
🔍 查看详情 - 响应数据: {响应的data字段}
🔍 查看详情 - 用户列表: {users数组}
🔍 查看详情 - 汇总信息: {summary对象}
🔍 查看详情 - 用户数据: {单个用户的完整数据}
🔍 查看详情 - 计算方法: {用户的计算方法信息}
🔍 查看详情 - 参数评分: {用户的参数评分数组}
🔍 查看详情 - 用户汇总: {用户的汇总统计}
```

### 🚫 错误阶段（如果出现错误）
```
🚫 查看详情 - 获取员工详细信息失败: {错误对象}
🚫 查看详情 - 错误详情: {详细的错误信息}
```

## 数据结构说明

### API响应结构
```javascript
{
  code: 0,           // 状态码，0表示成功
  msg: "获取成功",    // 响应消息
  data: {
    users: [         // 用户数组
      {
        userName: "用户名",
        userNickName: "用户昵称",
        calculationMethod: {
          methodId: 1,
          methodName: "计算方法名称",
          description: "方法描述",
          formula: "计算公式",
          assignedParameters: [...]
        },
        parameterScores: [
          {
            parameterName: "参数名称",
            parameterNameCn: "参数中文名",
            dataSource: "数据来源表",
            scoreValue: 90.0,
            assessmentConfigId: 1,
            assessmentConfigName: "考核配置名称",
            projectId: 69,
            projectName: "项目名称",
            scorerUsername: "评分人用户名",
            scorerNickName: "评分人昵称",
            createdAt: "创建时间",
            recordId: 123
          }
        ],
        userSummary: {
          totalParameters: 3,
          scoredParameters: 2,
          unscoredParameters: 1,
          totalRecords: 5
        },
        hasError: false,
        errorMessage: ""
      }
    ],
    summary: {
      totalUsers: 1,
      successUsers: 1,
      errorUsers: 0,
      totalRecords: 5
    }
  }
}
```

## 常见问题排查

### 1. 没有看到任何输出
- 确认已打开浏览器控制台
- 确认在 Console 标签页
- 尝试刷新页面后重新操作

### 2. 看到错误信息
- 检查网络连接
- 确认后端服务是否正常运行
- 查看具体的错误信息进行排查

### 3. 数据为空
- 确认用户是否有分配计算方法
- 确认用户是否有评分数据
- 检查数据库中的相关表是否有数据

## 调试技巧

1. **过滤日志**: 在控制台搜索框输入 "查看详情" 可以只显示相关日志
2. **展开对象**: 点击对象前的三角形可以展开查看详细内容
3. **复制数据**: 右键点击对象可以复制为JSON格式
4. **清空控制台**: 点击控制台的清空按钮或按 `Ctrl+L` 清空之前的日志

## 移除调试信息

测试完成后，如需移除调试信息，可以删除或注释掉相关的 `console.log` 语句。
