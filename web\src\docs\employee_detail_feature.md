# 员工评分详情查看功能

## 功能概述

在员工评分tab下的表格中，每行都有一个"查看详情"按钮，点击后可以查看该员工的详细参数评分信息。

## 功能特点

### 1. 数据来源
- 调用后端API `getUserParameterScores` 获取用户的完整参数评分数据
- 支持从三个评分表中动态获取数据：
  - `assessment_coefficient_allocation` - 考核系数分配表
  - `project_manager_score` - 项目经理评分表  
  - `department_manager_score` - 部门经理评分表

### 2. 显示内容

#### 基本信息
- 成员姓名
- 用户名
- 用户ID
- 邮箱
- 电话
- 权限ID
- 是否管理员

#### 计算方法信息
- 方法名称
- 方法描述
- 计算公式
- 分配的参数列表

#### 评分详情
- 按参数类型分组显示
- 每个参数下的所有评分记录
- 显示评分值、项目信息、评分人、创建时间
- 标注数据来源表

#### 汇总信息
- 总参数数量
- 已评分参数数量
- 未评分参数数量
- 总记录数量

### 3. 用户体验

#### 加载状态
- 点击按钮后显示"正在获取详细评分数据..."的加载提示
- 数据获取完成后自动关闭加载提示

#### 错误处理
- 如果API调用失败，显示错误信息并回退到基本信息显示
- 如果没有获取到详细数据，显示基本信息并提示未能获取详细数据

#### 界面设计
- 使用分层的信息展示，便于阅读
- 不同类型的信息使用不同颜色的标题区分
- 评分状态用颜色标识（已评分为绿色，未评分为红色）
- 弹窗宽度为600px，内容区域支持滚动

## 技术实现

### API调用
```javascript
import { getSingleUserParameterScores } from '@/api/assessment/assessmentData'

const response = await getSingleUserParameterScores(row.username)
```

### 数据处理
- 按参数类型分组显示评分记录
- 格式化时间显示
- 处理空值和异常情况
- 数据来源中文化显示

### 界面渲染
- 使用HTML字符串构建详细信息
- 支持滚动的内容区域
- 响应式的弹窗设计

## 使用方法

1. 进入项目评分页面
2. 切换到"员工评分"标签页（需要管理员权限）
3. 在员工列表中找到要查看的员工
4. 点击该行的"查看详情"按钮
5. 在弹出的详情窗口中查看完整的评分信息

## 注意事项

1. **权限要求**：只有管理员才能看到员工评分标签页
2. **数据实时性**：每次点击都会重新从后端获取最新数据
3. **性能考虑**：单次查询一个用户的数据，响应速度较快
4. **错误恢复**：即使API调用失败，也会显示基本信息，不会完全阻断用户操作

## 扩展可能

1. **批量查看**：可以扩展为支持批量查看多个员工的详情
2. **数据导出**：可以添加将详情数据导出为Excel的功能
3. **历史记录**：可以添加查看历史评分变化的功能
4. **评分对比**：可以添加不同员工之间的评分对比功能
