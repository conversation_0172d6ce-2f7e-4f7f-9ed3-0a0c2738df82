# 员工详情查看功能 - 按考核配置过滤

## 功能改进说明

现在员工详情查看功能已经改进为**只显示当前考核配置下的评分数据**，而不是显示所有考核配置的数据。

## 工作原理

### 1. 获取当前考核配置
- 自动获取当前活跃tab的考核配置ID
- 将该配置ID作为过滤条件传递给后端API

### 2. API调用参数
```javascript
{
  userName: "用户名",
  assessmentConfigIds: [当前考核配置ID], // 只查询当前配置
  parameterNames: [] // 查询所有参数类型
}
```

### 3. 数据过滤
- 后端只返回指定考核配置下的评分数据
- 前端显示时会标明当前查看的是哪个考核配置的数据

## 使用场景

### 场景1：查看特定考核期的评分
1. 切换到某个考核配置的tab（如"2024年第一季度考核"）
2. 在员工评分列表中点击"查看详情"
3. 只会显示该季度考核的评分数据

### 场景2：对比不同考核期的数据
1. 在"2024年第一季度考核"tab中查看员工A的详情
2. 切换到"2024年第二季度考核"tab
3. 再次查看员工A的详情
4. 两次查看的数据会不同，分别对应不同的考核期

## 界面显示改进

### 基本信息区域新增
- **当前考核配置**：显示当前查看的考核配置名称和ID
- 例如：`当前考核配置: 2024年第一季度考核 (ID: 1)`

### 评分详情区域
- 只显示当前考核配置下的评分记录
- 每条记录仍会显示具体的考核配置名称进行确认

## 调试信息

在浏览器控制台中，您可以看到以下新的调试信息：

```
🚀 查看详情 - 当前tab名称: tab_1_2024Q1
🚀 查看详情 - 当前tab信息: {name: "tab_1_2024Q1", title: "2024年第一季度考核", configId: 1, ...}
🚀 查看详情 - 当前考核配置ID: 1
🚀 查看详情 - API调用参数: {userName: "10112531", assessmentConfigIds: [1], parameterNames: []}
```

## 优势

### 1. 数据精确性
- 避免显示无关的历史数据
- 用户只看到当前关心的考核期数据

### 2. 性能提升
- 减少数据传输量
- 提高查询速度

### 3. 用户体验
- 信息更加聚焦
- 减少信息干扰
- 明确显示当前查看的考核配置

### 4. 逻辑一致性
- 与当前页面的上下文保持一致
- 符合用户的操作预期

## 注意事项

### 1. 考核配置切换
- 切换到不同的考核配置tab后，查看详情会显示不同的数据
- 这是正常行为，符合设计预期

### 2. 无数据情况
- 如果用户在当前考核配置下没有评分数据，会显示"暂无评分数据"
- 这不代表用户在其他考核配置下也没有数据

### 3. 配置ID获取
- 如果无法获取当前考核配置ID，会查询所有配置的数据（兜底机制）
- 这种情况下会在调试信息中显示 `当前考核配置ID: null`

## 测试建议

1. **基本功能测试**
   - 在不同的考核配置tab中查看同一员工的详情
   - 确认显示的数据确实对应当前考核配置

2. **边界情况测试**
   - 查看在当前配置下没有评分数据的员工
   - 查看在当前配置下有部分评分数据的员工

3. **调试信息验证**
   - 检查控制台输出的考核配置ID是否正确
   - 确认API调用参数中的assessmentConfigIds数组包含正确的ID
