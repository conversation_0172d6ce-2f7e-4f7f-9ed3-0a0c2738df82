import service from '@/utils/request'
// @Tags Organizational
// @Summary 不需要鉴权的组织架构接口
// @Accept application/json
// @Produce application/json
// @Param data query request.OrganizationalSearch true "分页获取组织架构列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /org/getOrganizationalPublic [get]
export const getOrganizationalPublic = () => {
  return service({
    url: '/org/getOrganizationalPublic',
    method: 'get',
  })
}

//同步角色
export const syncAuthority = () => {
  return service({
    url: '/org/syncAuthority',
    method: 'put',
  })
}
//获取角色列表
export const getSysAuthorityList = () => {
  return service({
    url: '/org/getSysAuthorityList',
    method: 'get',
  })
}
//设置角色权限
export const setAuthorityLevel = (data) => {
  return service({
    url: '/org/setAuthorityLevel',
    method: 'put',
    data,
  })
}

//获取组织树
export const getOrganizationalTree = () => {
  return service({
    url: '/org/getOrganizationalTree',
    method: 'get',
  })
}


//创建组织
export const createOrganizational = (data) => {
  return service({
    url: '/org/createOrganizational',
    method: 'post',
    data,
  })
}
//删除组织
export const deleteOrganizational = (params) => {
  return service({
    url: '/org/deleteOrganizational',
    method: 'delete',
    params,
  })
}
//更新组织
export const updateOrganizational = (data) => {
  return service({
    url: '/org/updateOrganizational',
    method: 'put',
    data,
  })
}

//获取组织成员
export const getOrganizationalMember = (params) => {
  return service({
    url: '/org/getOrganizationalMember',
    method: 'get',
    params,
  })
}
//获取用户列表
export const getUser = () => {
  return service({
    url: '/org/getUser',
    method: 'get',
  })
}
//添加组织成员
export const joinOrganizationalMember = (data) => {
  return service({
    url: '/org/joinOrganizationalMember',
    method: 'post',
    data,
  })
}
//删除组织成员
export const removeOrganizationalMember = (data) => {
  return service({
    url: '/org/removeOrganizationalMember',
    method: 'delete',
    data,
  })
}
// 设置组织成员角色
export const setOrganizationalMemberAuthority = (data) => {
  return service({
    url: '/org/setOrganizationalMemberAuthority',
    method: 'put',
    data,
  })
}
// 获取节点管理员角色列表
export const getNodeAdminAuthorityList = () => {
  return service({
    url: '/org/getNodeAdminAuthorityList',
    method: 'get',
    
  })
}
// 切换登录组织
export const changeLoginOrg = (data) => {
  return service({
    url: '/org/changeLoginOrg',
    method: 'put',
    data
  })
}
//获取用户可登录节点列表
export const getUserLoginList = () => {
  return service({
    url: '/org/getUserLoginList',
    method: 'get',
    
  })
}
//setNodeAdmin 设置节点管理员
export const setNodeAdmin = (data) => {
  return service({
    url: '/org/setNodeAdmin',
    method: 'put',
    data
  })
}

//setAgentManager 设置代理负责人
export const setAgentManager = (data) => {
  return service({
    url: '/org/setAgentManager',
    method: 'put',
    data
  })
}