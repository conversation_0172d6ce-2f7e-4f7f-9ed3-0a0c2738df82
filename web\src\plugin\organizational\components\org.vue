<template>
    <div class="tree-container">
        <el-button type="primary" @click="handleAddEdit(`addRoot`, null)" class="add-root-btn">
            添加根公司
        </el-button>
        <el-tree :data="treeData" :props="defaultProps" :expand-on-click-node="false" default-expand-all
            @node-click="handleNodeClick" node-key="ID" class="org-tree">
            <template #default="{ node, data }">
                <span class="custom-tree-node">
                    <span class="node-label">{{ data.name }}</span>
                    <el-dropdown trigger="click" @click.stop class="node-actions">
                        <span class="action-trigger">
                            <el-icon>
                                <MoreFilled />
                            </el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="handleAddEdit(`addChild`, data)">
                                    <el-icon class="action-icon">
                                        <Plus />
                                    </el-icon>
                                    添加子节点
                                </el-dropdown-item>
                                <el-dropdown-item @click="handleAddEdit(`edit`, data)">
                                    <el-icon class="action-icon">
                                        <Edit />
                                    </el-icon>编辑</el-dropdown-item>
                                <el-dropdown-item @click="handleDelete(data)"><el-icon class="action-icon">
                                        <Delete />
                                    </el-icon>删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </span>
            </template>
        </el-tree>

        <el-dialog v-model="dialogVisible" :title="dialogTitle" width="50%" class="org-dialog">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="dialog-form">
                <el-form-item label="组织名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入组织名称" />
                </el-form-item>

                <el-form-item label="组织类型" prop="type">
                    <el-radio-group v-model="form.type" :disabled="formType === 'addRoot'">
                        <el-radio :label="1">
                            <el-icon>
                                <OfficeBuilding />
                            </el-icon>
                            公司</el-radio>
                        <el-radio :label="2">
                            <el-icon><el-icon-s-custom /></el-icon>
                            <el-icon>
                                <House />
                            </el-icon>
                            部门</el-radio>
                        <el-radio :label="3">
                            <el-icon>
                                <Monitor />
                            </el-icon>
                            其他</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item v-if="formType !== 'addRoot'" label="父组织" prop="parent_id">
                    <el-tree-select v-model="form.parent_id" :data="[nodeRoot, ...treeData]" :props="defaultProps"
                        :render-after-expand="false" check-strictly show-checkbox node-key="ID" placeholder="请选择父组织"
                        :default-expanded-keys="[form.parent_id]" />
                </el-form-item>

                <el-form-item label="排序号">
                    <el-input-number v-model="form.sort" :min="0" />
                </el-form-item>

                <el-form-item label="状态">
                    <el-switch v-model="form.status" active-text="正常" inactive-text="禁用" :active-value="1"
                        :inactive-value="2">
                    </el-switch>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>

</template>

<script setup>
import {
    createOrganizational,// 创建组织
    getOrganizationalTree,// 获取组织树结构
    deleteOrganizational, // 删除组织
    updateOrganizational,//更新组织
} from '@/plugin/organizational/api/organizational'
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const model = defineModel()

// 处理节点点击事件
const handleNodeClick = (data) => {
    // console.log('节点点击:', data)
    model.value = data.ID
    // 这里可以添加业务逻辑，如更新父组件的nodeId等
}

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formType = ref('') // 'addRoot', 'addChild', 'edit'
const formRef = ref()

const form = ref({
    name: '',
    type: 1,
    parent_id: 0,
    sort: 0,
    status: 1
})

const rules = {
    name: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择组织类型', trigger: 'change' }]
}


const treeData = ref([])
const nodeRoot = ref({
    ID: 0,
    name: '根节点',
    children: []
})

const defaultProps = {
    value: 'ID',
    label: 'name',
    children: 'children'
}




const getOrganizationalTreeApi = async () => {
    let res = await getOrganizationalTree()
    res.code === 0 ? treeData.value = res.data : treeData.value = []
}

//删除组织
const handleDelete = (data) => {
    ElMessageBox.confirm('是否删除组织', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        let res = await deleteOrganizational({ ID: data.ID })
        if (res.code === 0) {
            ElMessage.success(res.msg)
            getOrganizationalTreeApi()
        }
    })
}


const handleAddEdit = (type, data) => {
    switch (type) {
        case "addRoot":
            dialogTitle.value = '添加公司节点'
            formType.value = 'addRoot'
            form.value = {
                name: '',
                type: 1,
                parent_id: 0,
                sort: 0,
                status: 1
            }
            dialogVisible.value = true
            break;
        case "addChild":
            dialogTitle.value = '添加部门节点'
            formType.value = 'addChild'
            form.value = {
                name: '',
                type: 2,
                parent_id: data.ID,
                sort: 0,
                status: 1
            }
            dialogVisible.value = true
            break;
        case "edit":
            dialogTitle.value = '编辑节点'
            formType.value = 'edit'
            form.value = {
                ID: data.ID,
                name: data.name,
                type: data.type,
                parent_id: data.parent_id,
                sort: data.sort,
                status: data.status
            }
            // 查找当前节点的父节点名称
            const findParentName = (tree, id) => {
                for (const node of tree) {
                    if (node.ID === id) return node.name
                    if (node.children) {
                        const found = findParentName(node.children, id)
                        if (found) return found
                    }
                }
                return ''
            }
            form.value.parentName = findParentName(treeData.value, data.parent_id)
            dialogVisible.value = true
            break;
        default:
            break;
    }
}



// 检查是否是子节点
const isDescendant = (node, id) => {
    if (node.ID === id) return true
    if (node.children) {
        return node.children.some(child => isDescendant(child, id))
    }
    return false
}

//提交表单
const handleSubmit = (row) => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            if (formType.value === 'addRoot' || formType.value === 'addChild') {
                let res = await createOrganizational(form.value)
                if (res.code === 0) {
                    dialogVisible.value = false
                    ElMessage.success('添加成功')
                    getOrganizationalTreeApi()
                }
            } else if (formType.value === 'edit') {
                let res = await updateOrganizational(form.value)
                if (res.code === 0) {
                    dialogVisible.value = false
                    ElMessage.success('更新成功')
                    getOrganizationalTreeApi()
                }
            }

        }
    })
}

onMounted(() => {
    getOrganizationalTreeApi()
})

</script>

<style scoped>
.tree-container {
    padding: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.add-root-btn {
    margin-bottom: 16px;
    width: 100%;
}

.org-tree {
    flex: 1;
    overflow: auto;
}

.org-tree :deep(.el-tree-node__content) {
    height: 44px;
    line-height: 44px;
    transition: all 0.2s;
}

.org-tree :deep(.el-tree-node__content:hover) {
    background-color: rgba(64, 158, 255, 0.08);
}

.org-tree :deep(.el-tree-node:focus > .el-tree-node__content) {
    background-color: rgba(64, 158, 255, 0.1);
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 12px;
}

.node-label {
    margin-right: 8px;
    font-weight: 500;
    color: #333;
}

.node-actions {
    margin-left: 8px;
    opacity: 0;
    transition: opacity 0.2s;
}

.custom-tree-node:hover .node-actions {
    opacity: 1;
}

.action-trigger {
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    color: #909399;
    transition: all 0.2s;
}

.action-trigger:hover {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409eff;
}

.action-icon {
    margin-right: 6px;
    font-size: 14px;
}

.org-dialog .el-dialog__body {
    padding: 20px 20px 0;
}

.dialog-form .el-form-item {
    margin-bottom: 20px;
}

.dialog-footer {
    text-align: right;
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
}
</style>
