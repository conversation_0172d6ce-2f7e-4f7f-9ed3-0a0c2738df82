<template>
  <div class="user-container">
    <div class="user-header">
      <h3>用户列表</h3>
        <span>当前权限:

          <span v-if="userStore.userInfo.org && userStore.userInfo.org.is_admin"> 有管理员权限 </span>
          <span v-else="userStore.userInfo.org.is_admin"> 无管理员权限 </span>
        </span>
      <el-button :disabled="!model" type="primary" size="small" @click="openJoinDialog">添加用户</el-button>
    </div>

    <el-table :data="MemberList" border stripe style="width: 100%">
      <el-table-column prop="user.nickName" label="姓名" width="120" />
      <el-table-column label="角色">
        <template #default="{ row }">
          <el-select v-model="row.authority_id" placeholder="请选择角色" style="width: 100%"
            @change="setOrganizationalMemberAuthorityChange(row)">
            <el-option v-for="item in AuthorityList" :key="item.authority_id" :label="item.authority.authorityName"
              :value="item.authority_id" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="管理员">
        <template #default="{ row }">
          <tag v-if="row.is_admin" type="success">管理员</tag>
          <el-button size="small" type="text" @click="setNodeAdminApi(row)" class="m-1">设置/取消管理</el-button>
        </template>
      </el-table-column>
      <el-table-column label="代理负责人">
        <template #default="{ row }">
          <tag v-if="row.is_agent_manager" type="warning">代理负责人</tag>
          <el-button size="small" type="text" @click="setAgentManagerApi(row)" class="m-1">设置/取消代理负责人</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'danger' : 'success'">
            {{ row.status === 1 ? '离职' : '在职' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-button size="small" type="text">编辑</el-button>
          <el-button size="small" type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background layout="prev, pager, next, sizes, jumper" :total="pageParams.total"
        :page-size="pageParams.pageSize" :current-page="pageParams.page" :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange" @current-change="handlePageChange" />
    </div>

    <el-dialog v-model="JoinDialog" title="添加成员" width="500px">
      <el-form :model="Joinform" label-width="80px">
        <el-form-item label="选择用户">
          <el-select v-model="Joinform.user_ids" multiple placeholder="请选择用户" style="width: 100%">
            <el-option v-for="user in userList" :key="user.ID" :label="user.nickName" :value="user.ID" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="JoinDialog = false">取消</el-button>
        <el-button type="primary" @click="submitJoin">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  joinOrganizationalMember,// 加入组织
  getOrganizationalMember,// 获取组织成员
  getUser,//  获取用户列表
  removeOrganizationalMember,// 移除组织成员
  setOrganizationalMemberAuthority,//设置组织成员角色
  getNodeAdminAuthorityList,//获取节点管理员角色列表
  setNodeAdmin, // 设置节点管理员
  setAgentManager, // 设置代理负责人

} from '@/plugin/organizational/api/organizational'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, watch, onMounted } from 'vue'
import { useUserStore } from '@/pinia/modules/user'
const userStore = useUserStore()

const model = defineModel()

const pageParams = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

watch(model, (newVal, oldVal) => {
  if (newVal) {
    pageParams.value.page = 1
    getOrganizationalMemberApi()
  }
})

//获取部门成员列表
const getOrganizationalMemberApi = async () => {
  const res = await getOrganizationalMember({
    ID: model.value,
    page: pageParams.value.page,
    pageSize: pageParams.value.pageSize
  })
  if (res.code === 0) {
    MemberList.value = res.data.list
    pageParams.value.total = res.data.total
  } else {
    MemberList.value = []
    pageParams.value.total = 0
  }
}

const handlePageChange = (page) => {
  pageParams.value.page = page
  getOrganizationalMemberApi()
}

const handleSizeChange = (size) => {
  pageParams.value.pageSize = size
  pageParams.value.page = 1
  // 确保重新获取数据时使用新的分页大小
  getOrganizationalMemberApi()
  // 添加调试日志
  console.log('分页大小变化:', size, '当前参数:', pageParams.value)
}

const AuthorityList = ref([])
const MemberList = ref([])
const userList = ref([])
const JoinDialog = ref(false)
const Joinform = ref({
  user_ids: [],
})

const setOrganizationalMemberAuthorityChange = async (row) => {
  let res = await setOrganizationalMemberAuthority({
    user_ids: [row.user_id],
    org_id: row.org_id,
    authority_id: row.authority_id
  })
  if (res.code === 0) {
    ElMessage.success('设置成功')
  } else {
    row.authority_id = row.authority.authorityId
  }


}

// 获取节点管理员角色列表
const getNodeAdminAuthorityListApi = async () => {
  const res = await getNodeAdminAuthorityList()
  res.code === 0 ? AuthorityList.value = res.data : AuthorityList.value = []
}
// 获取用户列表
const getUserListApi = async () => {
  if (userList.value.length > 0) return
  let res = await getUser()
  res.code === 0 ? userList.value = res.data : userList.value = []
}

// 打开添加用户弹窗
const openJoinDialog = () => {
  JoinDialog.value = true
  getUserListApi()
}

// 提交添加用户
const submitJoin = async () => {
  if (!Joinform.value.user_ids.length) {
    ElMessage.warning('请选择至少一个用户')
    return
  }

  const res = await joinOrganizationalMember({
    org_id: model.value,
    user_ids: Joinform.value.user_ids
  })

  if (res.code === 0) {
    JoinDialog.value = false
    ElMessage.success(res.msg)
    getOrganizationalMemberApi()
    Joinform.value.user_ids = []
  }
}



// 删除成员
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要移除成员 ${row.user.nickName} 吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    const res = await removeOrganizationalMember({
      org_id: model.value,
      user_ids: [row.user.ID]
    })
    if (res.code === 0) {
      ElMessage.success('移除成功')
      getOrganizationalMemberApi()
    }
  })
}

//  设置节点管理员
const setNodeAdminApi = async (row) => {
  let res = await setNodeAdmin({
    user_id: row.user_id,
    org_id: row.org_id,
    is_admin: !row.is_admin
  })
  if (res.code === 0) {
    ElMessage.success('设置成功')
  }
  row.is_admin = !row.is_admin
  getOrganizationalMemberApi()
}

//  设置代理负责人
const setAgentManagerApi = async (row) => {
  let res = await setAgentManager({
    user_id: row.user_id,
    org_id: row.org_id,
    is_agent_manager: !row.is_agent_manager
  })
  if (res.code === 0) {
    ElMessage.success('设置代理负责人成功')
  }
  row.is_agent_manager = !row.is_agent_manager
  getOrganizationalMemberApi()
}


onMounted(() => {
  getNodeAdminAuthorityListApi()
})

</script>
<style scoped>
.user-container {
  padding: 16px;
  height: 100%;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.user-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table :deep(.el-table__cell) {
  padding: 12px 0;
}
</style>