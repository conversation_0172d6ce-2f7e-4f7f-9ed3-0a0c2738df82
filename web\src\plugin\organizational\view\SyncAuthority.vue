<template>
    <div>
        <el-button type="primary" @click="syncAuthorityApi">同步角色</el-button>
        <el-table :data="AuthorityList" style="width: 100%" border stripe :cell-style="{ padding: '5px' }"
            :header-cell-style="{ padding: '5px' }">
            <el-table-column prop="authority_id" label="角色ID" width="180" />
            <el-table-column prop="authority.authorityName" label="名称" width="180" />
            <el-table-column label="权限" width="180">
                <template #default="scope">
                    <el-select v-model="scope.row.level" placeholder="请选择" @change="setAuthorityLevelApi(scope.row)">
                        <el-option v-for="item in Permission" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </template>
            </el-table-column>
        </el-table>

    </div>
</template>
<script setup>
import {
    syncAuthority, // 同步角色
    setAuthorityLevel,// 设置权限
    getSysAuthorityList// 获取角色列表

} from '@/plugin/organizational/api/organizational'
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'


const AuthorityList = ref([])
const getAuthorityListApi = async () => {
    let res = await getSysAuthorityList()
    res.code === 0 ? AuthorityList.value = res.data : AuthorityList.value = []

}
const syncAuthorityApi = async () => {
    ElMessageBox.confirm('是否同步角色', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        let res = await syncAuthority()
        if (res.code === 0) {
            ElMessage.success(res.msg)
            getAuthorityListApi()
        }
    })
}

// 权限
const Permission = [
    {
        value: 0,
        label: '无权限'
    },
    {
        value: 1,
        label: '所有权限'
    },
    {
        value: 2,
        label: '公司及子公司'
    },
    {
        value: 3,
        label: '本公司'
    },
    {
        value: 4,
        label: '部门及子部门 本公司范围'
    },
    {
        value: 5,
        label: '本部门'
    },
    {
        value: 6,
        label: '仅自己'
    }
]
const setAuthorityLevelApi = async (row) => {
    let res = await setAuthorityLevel({
        authority_id: row.authority_id,
        level: row.level
    })
    if (res.code === 0) {
        ElMessage.success(res.msg)
        getAuthorityListApi()
    } else {
        ElMessage.error(res.msg)
    }
}


onMounted(() => {
    getAuthorityListApi()
})


</script>
<style></style>