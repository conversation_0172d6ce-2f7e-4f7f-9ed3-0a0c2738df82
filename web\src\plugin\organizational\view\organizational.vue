<template>
  <div class="org-container">
    <div class="org-tree-container">
      <org v-model="nodeId"></org>
    </div>
    <div class="user-list-container">
      <user v-model="nodeId"></user>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import org from '@/plugin/organizational/components/org.vue'
import user from '@/plugin/organizational/components/user.vue'

// 当前选中节点
const nodeId = ref('')
</script>

<style scoped>
.org-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 20px;
  padding: 20px;
}

.org-tree-container {
  flex: 0 0 320px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.user-list-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto;
}

@media (max-width: 768px) {
  .org-container {
    flex-direction: column;
    height: auto;
  }
  
  .org-tree-container {
    flex: 1 1 auto;
    margin-bottom: 20px;
  }
}
</style>
