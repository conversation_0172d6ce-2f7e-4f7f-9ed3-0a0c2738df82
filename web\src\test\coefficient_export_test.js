/**
 * 考核系数分配Excel导出功能测试用例
 */

// 模拟测试数据
const mockTabData = {
  projects: [
    { id: 1, name: '测试项目A', type: '开发项目' },
    { id: 2, name: '测试项目B', type: '维护项目' },
    { id: 3, name: '测试项目C', type: '研发项目' }
  ],
  members: [
    { id: 101, name: '张三', userName: 'zhangsan' },
    { id: 102, name: '李四', userName: 'lisi' },
    { id: 103, name: '王五', userName: 'wangwu' }
  ],
  scores: {
    '1-101': 0.5,   // 张三在项目A: 50%
    '1-102': 0.3,   // 李四在项目A: 30%
    '1-103': 0,     // 王五在项目A: 0%
    '2-101': 0.2,   // 张三在项目B: 20%
    '2-102': 0,     // 李四在项目B: 0%
    '2-103': 0.8,   // 王五在项目B: 80%
    '3-101': 0,     // 张三在项目C: 0%
    '3-102': 0.4,   // 李四在项目C: 40%
    '3-103': 0.6    // 王五在项目C: 60%
  }
}

// 模拟权限控制函数
const mockIsCoefficientCellEditable = (projectId, userId) => {
  // 模拟权限规则：
  // 张三(101)可以编辑项目1和2
  // 李四(102)可以编辑项目1和3
  // 王五(103)可以编辑项目2和3
  const permissions = {
    101: [1, 2],  // 张三
    102: [1, 3],  // 李四
    103: [2, 3]   // 王五
  }
  
  return permissions[userId] && permissions[userId].includes(projectId)
}

// 模拟prepareExcelData函数
const mockPrepareExcelData = (tabName, tabData) => {
  const excelData = []
  
  // 构建表头
  const headers = ['项目名称', '项目类型']
  tabData.members.forEach(member => {
    headers.push(member.name || member.userName || `用户${member.id}`)
  })
  headers.push('合计')
  excelData.push(headers)
  
  // 构建数据行
  tabData.projects.forEach(project => {
    const row = [
      project.name || `项目${project.id}`,
      project.type || '项目'
    ]
    
    let projectTotal = 0
    
    // 添加每个成员的系数数据
    tabData.members.forEach(member => {
      const key = `${project.id}-${member.id}`
      let coefficient = tabData.scores[key] || 0
      
      // 检查单元格是否可编辑
      if (!mockIsCoefficientCellEditable(project.id, member.id)) {
        row.push('-')
      } else {
        const percentValue = (coefficient * 100).toFixed(1) + '%'
        row.push(coefficient > 0 ? percentValue : '')
        projectTotal += parseFloat(coefficient) || 0
      }
    })
    
    // 添加项目合计
    row.push(projectTotal > 0 ? (projectTotal * 100).toFixed(1) + '%' : '')
    excelData.push(row)
  })
  
  // 构建合计行
  const totalRow = ['合计', '']
  
  tabData.members.forEach(member => {
    let memberTotal = 0
    
    tabData.projects.forEach(project => {
      if (mockIsCoefficientCellEditable(project.id, member.id)) {
        const key = `${project.id}-${member.id}`
        const coefficient = tabData.scores[key] || 0
        memberTotal += parseFloat(coefficient) || 0
      }
    })
    
    totalRow.push(memberTotal > 0 ? (memberTotal * 100).toFixed(1) + '%' : '')
  })
  
  totalRow.push('')
  excelData.push(totalRow)
  
  return excelData
}

// 测试用例
console.log('=== 考核系数分配Excel导出测试 ===')

// 测试1：正常数据导出
console.log('\n测试1：正常数据导出')
const excelData = mockPrepareExcelData('测试tab', mockTabData)
console.log('Excel数据结构：')
excelData.forEach((row, index) => {
  console.log(`第${index + 1}行:`, row)
})

// 测试2：权限控制验证
console.log('\n测试2：权限控制验证')
console.log('权限矩阵：')
mockTabData.projects.forEach(project => {
  const permissions = mockTabData.members.map(member => 
    mockIsCoefficientCellEditable(project.id, member.id) ? '✓' : '✗'
  )
  console.log(`项目${project.id}(${project.name}):`, permissions.join(' '))
})

// 测试3：合计计算验证
console.log('\n测试3：合计计算验证')
const totalRow = excelData[excelData.length - 1]
console.log('成员合计:', totalRow.slice(2, -1))

// 预期结果说明
console.log('\n=== 预期结果说明 ===')
console.log('1. 表头应包含：项目名称、项目类型、张三、李四、王五、合计')
console.log('2. 不可编辑的单元格应显示"-"')
console.log('3. 可编辑但为0的单元格应显示空白')
console.log('4. 有数值的单元格应显示百分比格式')
console.log('5. 合计行应正确计算每个成员的总系数')

// 导出结果验证
console.log('\n=== 导出结果验证 ===')
console.log('表头正确:', JSON.stringify(excelData[0]))
console.log('数据行数:', excelData.length - 2, '(不含表头和合计行)')
console.log('合计行正确:', JSON.stringify(excelData[excelData.length - 1]))
