/**
 * 快速测试文件 - 验证公式计算器是否正常工作
 */

import { FormulaCalculator } from './socer/formulaCalculator.js'
import { ParameterMapper } from './socer/parameterMapper.js'

// 快速测试函数
export function quickTest() {
  console.log('🧪 开始快速测试...')

  try {
    const calculator = new FormulaCalculator()
    const mapper = new ParameterMapper()

    // 测试1: 基本计算
    console.log('\n📝 测试1: 基本计算')
    const formula1 = '10'
    const parameters1 = {
      department_manager_score: 88,
      project_manager_score: 90,
      project_participation: 1.0  // 修正为1.0而不是100
    }

    const result1 = calculator.calculate(formula1, parameters1)
    console.log('✅ 计算结果:', result1)

    // 手动验证
    const manual1 = 88 * 0.6 + (90 * 1.0) * 0.4
    console.log('🔧 手动验证:', manual1)
    console.log('🎯 计算正确:', Math.abs(result1 - manual1) < 0.01)

    // 测试2: 处理百分比参数
    console.log('\n📝 测试2: 处理百分比参数')
    const parameters2 = {
      department_manager_score: 88,
      project_manager_score: 90,
      project_participation: 100  // 百分比形式
    }

    // 转换百分比
    if (parameters2.project_participation > 1) {
      parameters2.project_participation = parameters2.project_participation / 100
    }

    const result2 = calculator.calculate(formula1, parameters2)
    console.log('✅ 转换后计算结果:', result2)

    // 测试3: 简单AVG函数
    console.log('\n📝 测试3: 简单AVG函数')
    const formula3 = 'AVG(project_manager_score * project_participation)'
    const result3 = calculator.calculate(formula3, parameters1)
    console.log('✅ AVG函数结果:', result3)
    console.log('🔧 预期结果:', 90 * 1.0)

    return {
      success: true,
      test1: { result: result1, manual: manual1, correct: Math.abs(result1 - manual1) < 0.01 },
      test2: { result: result2 },
      test3: { result: result3 }
    }
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return { success: false, error: error.message }
  }
}

// 验证用户提供的实际数据
export function verifyUserData() {
  console.log('🧪 验证用户提供的实际数据...')

  try {
    const calculator = new FormulaCalculator()

    // 用户提供的实际数据
    const userData = [
      {
        "parameterName": "project_manager_score",
        "scoreValue": 94,
        "projectId": 69,
        "projectName": "测试项目"
      },
      {
        "parameterName": "project_manager_score",
        "scoreValue": 87,
        "projectId": 70,
        "projectName": "test2"
      },
      {
        "parameterName": "project_participation",
        "scoreValue": 10,
        "projectId": 69,
        "projectName": "测试项目"
      },
      {
        "parameterName": "project_participation",
        "scoreValue": 90,
        "projectId": 70,
        "projectName": "test2"
      },
      {
        "parameterName": "department_manager_score",
        "scoreValue": 88,
        "projectId": null
      }
    ]

    console.log('📊 原始数据:', userData)

    // 手动计算验证
    const departmentScore = 88
    const project1_manager = 94
    const project1_participation = 10 / 100 // 转换为小数
    const project2_manager = 87
    const project2_participation = 90 / 100 // 转换为小数

    console.log('📝 计算步骤:')
    console.log(`1. 项目69: ${project1_manager} * ${project1_participation} = ${project1_manager * project1_participation}`)
    console.log(`2. 项目70: ${project2_manager} * ${project2_participation} = ${project2_manager * project2_participation}`)

    const avgProjectScore = ((project1_manager * project1_participation) + (project2_manager * project2_participation)) / 2
    console.log(`3. 平均值: (${project1_manager * project1_participation} + ${project2_manager * project2_participation}) / 2 = ${avgProjectScore}`)

    const finalScore = departmentScore * 0.6 + avgProjectScore * 0.4
    console.log(`4. 最终得分: ${departmentScore} * 0.6 + ${avgProjectScore} * 0.4 = ${finalScore}`)

    // 使用公式计算器验证
    console.log('\n🧮 使用公式计算器验证:')

    // 模拟多项目的AVG计算 - 这里我们需要特殊处理
    // 因为我们的计算器目前不支持多项目数据的自动聚合
    const formula = 'department_manager_score * 0.6 + AVG(project_score_1, project_score_2) * 0.4'
    const parameters = {
      department_manager_score: departmentScore,
      project_score_1: project1_manager * project1_participation,
      project_score_2: project2_manager * project2_participation
    }

    const calculatorResult = calculator.calculate(formula, parameters)
    console.log('🔧 计算器结果:', calculatorResult)
    console.log('🎯 结果一致:', Math.abs(calculatorResult - finalScore) < 0.01)

    return {
      success: true,
      manualCalculation: finalScore,
      calculatorResult: calculatorResult,
      isCorrect: Math.abs(calculatorResult - finalScore) < 0.01,
      details: {
        departmentScore,
        project1: { manager: project1_manager, participation: project1_participation, product: project1_manager * project1_participation },
        project2: { manager: project2_manager, participation: project2_participation, product: project2_manager * project2_participation },
        avgProjectScore,
        finalScore
      }
    }
  } catch (error) {
    console.error('❌ 验证失败:', error)
    return { success: false, error: error.message }
  }
}

// 在浏览器控制台中可用
if (typeof window !== 'undefined') {
  window.quickTest = quickTest
  window.verifyUserData = verifyUserData
}

export default quickTest
