/**
 * 计算集成模块 - 参照 projectAssessmentScore.vue 的计算方式
 * 直接调用后端 calculateByRule API 进行计算
 */

import { ElMessage } from 'element-plus'

/**
 * 批量计算多个用户的最终得分 - 参照 projectAssessmentScore.vue 的实现
 * @param {Array} userNames - 用户名列表
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<Object>} 批量计算结果 {userName: score}
 */
export async function calculateBatchUserScores(userNames, assessmentConfigId) {
  try {
    console.log('🧮 批量计算用户得分 (使用后端API):', { userNames, assessmentConfigId })

    // 动态导入所需的API和工具函数
    const { getSingleUserParameterScores } = await import('@/api/assessment/assessmentData')
    const { calculateByRule } = await import('@/api/assessment/calculationMethods')

    const results = {}

    // 为每个用户单独计算
    for (const userName of userNames) {
      try {
        console.log(`🔍 开始计算用户: ${userName}`)

        // 1. 获取用户参数评分数据
        const response = await getSingleUserParameterScores(
          userName,
          assessmentConfigId ? [assessmentConfigId] : [],
          []
        )

        if (response.code !== 0) {
          console.warn(`❌ 获取用户 ${userName} 参数评分失败: ${response.msg}`)
          results[userName] = null
          continue
        }

        const userData = response.data.users[0]
        if (!userData) {
          console.warn(`❌ 未找到用户 ${userName} 的数据`)
          results[userName] = null
          continue
        }

        // 2. 检查是否有计算方法
        if (!userData.calculationMethod || !userData.calculationMethod.methodId) {
          console.warn(`❌ 用户 ${userName} 未分配计算方法`)
          results[userName] = null
          continue
        }

        // 3. 准备规则计算所需的数据 (参照 projectAssessmentScore.vue 的 prepareRuleCalculationData)
        const ruleCalculationData = await prepareRuleCalculationDataForUser(userData, assessmentConfigId)

        // 4. 检查计算参数值是否完整
        const parameterCheck = checkUserParameterValues(userData)

        // 5. 检查是否有有效数据
        const hasValidData = !ruleCalculationData.hasNoScore && !ruleCalculationData.hasNoCoefficient && parameterCheck.isValid

        if (!hasValidData) {
          console.warn(`❌ 用户 ${userName} 数据不完整，跳过计算`)
          results[userName] = null
          continue
        }

        // 6. 调用规则计算API
        const ruleResponse = await calculateByRule({
          methodId: userData.calculationMethod.methodId,
          parameters: ruleCalculationData
        })

        if (ruleResponse.code === 0 && ruleResponse.data && ruleResponse.data.success) {
          const finalScore = ruleResponse.data.result
          results[userName] = finalScore
          console.log(`✅ 用户 ${userName} 计算成功: ${finalScore}`)
        } else {
          console.warn(`❌ 用户 ${userName} 规则计算失败: ${ruleResponse.msg}`)
          results[userName] = null
        }

      } catch (error) {
        console.error(`❌ 计算用户 ${userName} 时发生错误:`, error)
        results[userName] = null
      }
    }

    console.log('✅ 批量计算完成:', results)
    return results
  } catch (error) {
    console.error('❌ 批量计算用户得分时发生错误:', error)
    ElMessage.error(`批量计算失败: ${error.message}`)
    return {}
  }
}

/**
 * 准备规则计算所需的数据 - 参照 projectAssessmentScore.vue 的实现
 * @param {Object} userData - 用户数据
 * @param {number} currentConfigId - 考核配置ID
 * @returns {Promise<Object>} 规则计算数据
 */
async function prepareRuleCalculationDataForUser(userData, currentConfigId) {
  try {
    // 从用户数据中提取参数评分
    const parameterScores = userData.parameterScores || []
    console.log('🔍 原始参数评分数据:', parameterScores)

    // 初始化结果对象
    let departmentManagerScore = null
    const projectData = []
    let hasActualScore = false
    let hasNoCoefficient = false

    // 处理参数评分数据
    for (const paramScore of parameterScores) {
      if (paramScore.parameterName === 'department_manager_score') {
        departmentManagerScore = paramScore.score
        if (paramScore.score !== null && paramScore.score !== undefined) {
          hasActualScore = true
        }
      } else if (paramScore.parameterName === 'assessment_coefficient_allocation') {
        // 处理考核系数分配数据
        if (paramScore.details && Array.isArray(paramScore.details)) {
          for (const detail of paramScore.details) {
            if (detail.coefficient && detail.coefficient > 0) {
              // 查找对应的项目负责人评分
              const projectManagerScore = parameterScores.find(ps =>
                ps.parameterName === 'project_manager_score' &&
                ps.details &&
                ps.details.some(d => d.projectId === detail.projectId)
              )

              let projectScore = null
              if (projectManagerScore && projectManagerScore.details) {
                const scoreDetail = projectManagerScore.details.find(d => d.projectId === detail.projectId)
                if (scoreDetail) {
                  projectScore = scoreDetail.score
                  if (scoreDetail.score !== null && scoreDetail.score !== undefined) {
                    hasActualScore = true
                  }
                }
              }

              projectData.push({
                project_id: detail.projectId,
                coefficient: detail.coefficient,
                project_manager_score: projectScore
              })
            }
          }
        }

        // 如果没有有效的系数分配，标记为无系数
        if (projectData.length === 0) {
          hasNoCoefficient = true
        }
      }
    }

    // 检查是否有实际的评分数据
    if (!hasActualScore || departmentManagerScore === null) {
      console.warn('🔍 用户没有实际的评分数据，无法进行规则计算')
      return {
        department_manager_score: 0,
        project_data: projectData,
        hasNoScore: true,
        hasNoCoefficient: hasNoCoefficient
      }
    }

    const result = {
      department_manager_score: departmentManagerScore,
      project_data: projectData,
      hasNoScore: false,
      hasNoCoefficient: hasNoCoefficient
    }

    console.log('🔍 准备的规则计算数据:', result)
    return result
  } catch (error) {
    console.error('准备规则计算数据失败:', error)
    throw error
  }
}

/**
 * 检查用户计算参数值是否完整 - 参照 projectAssessmentScore.vue 的实现
 * @param {Object} userData - 用户数据
 * @returns {Object} 检查结果
 */
function checkUserParameterValues(userData) {
  try {
    // 检查是否有计算方法和分配的参数
    if (!userData.calculationMethod || !userData.calculationMethod.assignedParameters) {
      return {
        isValid: true,
        message: '用户没有分配计算方法，跳过参数检查'
      }
    }

    const assignedParameters = userData.calculationMethod.assignedParameters
    const parameterScores = userData.parameterScores || []

    // 检查每个分配的参数是否有对应的评分数据
    const missingParameters = []

    for (const assignedParam of assignedParameters) {
      const parameterName = assignedParam.parameterName

      // 查找对应的参数评分
      const paramScore = parameterScores.find(ps => ps.parameterName === parameterName)

      if (!paramScore) {
        missingParameters.push({
          parameterName: parameterName,
          displayName: getParameterDisplayName(parameterName)
        })
        continue
      }

      // 检查参数值是否为空
      if (parameterName === 'department_manager_score') {
        if (paramScore.score === null || paramScore.score === undefined) {
          missingParameters.push({
            parameterName: parameterName,
            displayName: getParameterDisplayName(parameterName)
          })
        }
      } else if (parameterName === 'assessment_coefficient_allocation') {
        if (!paramScore.details || paramScore.details.length === 0) {
          missingParameters.push({
            parameterName: parameterName,
            displayName: getParameterDisplayName(parameterName)
          })
        }
      } else if (parameterName === 'project_manager_score') {
        if (!paramScore.details || paramScore.details.length === 0) {
          missingParameters.push({
            parameterName: parameterName,
            displayName: getParameterDisplayName(parameterName)
          })
        }
      }
    }

    if (missingParameters.length > 0) {
      return {
        isValid: false,
        missingParameters: missingParameters,
        message: `缺少以下计算参数的值：${missingParameters.map(p => p.displayName).join('、')}`
      }
    }

    return {
      isValid: true,
      message: '所有计算参数值完整'
    }

  } catch (error) {
    console.error('检查用户参数值失败:', error)
    return {
      isValid: false,
      message: '检查参数值时发生错误'
    }
  }
}

/**
 * 获取参数显示名称
 * @param {string} parameterName - 参数名称
 * @returns {string} 显示名称
 */
function getParameterDisplayName(parameterName) {
  const displayNames = {
    'department_manager_score': '部门负责人评分',
    'project_manager_score': '项目负责人评分',
    'assessment_coefficient_allocation': '考核系数分配'
  }
  return displayNames[parameterName] || parameterName
}

/**
 * 为单个用户计算最终得分 - 使用后端API
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<number|null>} 计算结果
 */
export async function calculateUserFinalScore(userName, assessmentConfigId) {
  try {
    console.log('🧮 计算用户最终得分 (使用后端API):', { userName, assessmentConfigId })

    // 动态导入所需的API
    const { getSingleUserParameterScores } = await import('@/api/assessment/assessmentData')
    const { calculateByRule } = await import('@/api/assessment/calculationMethods')

    // 1. 获取用户参数评分数据
    const response = await getSingleUserParameterScores(
      userName,
      assessmentConfigId ? [assessmentConfigId] : [],
      []
    )

    if (response.code !== 0) {
      console.warn(`❌ 获取用户 ${userName} 参数评分失败: ${response.msg}`)
      return null
    }

    const userData = response.data.users[0]
    if (!userData) {
      console.warn(`❌ 未找到用户 ${userName} 的数据`)
      return null
    }

    // 2. 检查是否有计算方法
    if (!userData.calculationMethod || !userData.calculationMethod.methodId) {
      console.warn(`❌ 用户 ${userName} 未分配计算方法`)
      return null
    }

    // 3. 准备规则计算所需的数据
    const ruleCalculationData = await prepareRuleCalculationDataForUser(userData, assessmentConfigId)

    // 4. 检查计算参数值是否完整
    const parameterCheck = checkUserParameterValues(userData)

    // 5. 检查是否有有效数据
    const hasValidData = !ruleCalculationData.hasNoScore && !ruleCalculationData.hasNoCoefficient && parameterCheck.isValid

    if (!hasValidData) {
      console.warn(`❌ 用户 ${userName} 数据不完整，跳过计算`)
      return null
    }

    // 6. 调用规则计算API
    const ruleResponse = await calculateByRule({
      methodId: userData.calculationMethod.methodId,
      parameters: ruleCalculationData
    })

    if (ruleResponse.code === 0 && ruleResponse.data && ruleResponse.data.success) {
      const finalScore = ruleResponse.data.result
      console.log(`✅ 用户 ${userName} 计算成功: ${finalScore}`)
      return finalScore
    } else {
      console.warn(`❌ 用户 ${userName} 规则计算失败: ${ruleResponse.msg}`)
      return null
    }

  } catch (error) {
    console.error(`❌ 计算用户 ${userName} 最终得分时发生错误:`, error)
    return null
  }
}

// 已删除依赖前端计算服务的函数，现在直接使用后端API

/**
 * 在员工评分表格中添加计算得分列
 * @param {Array} employeeData - 员工数据数组
 * @param {number} assessmentConfigId - 考核配置ID
 * @returns {Promise<Array>} 添加了计算得分的员工数据
 */
export async function addCalculatedScoresToEmployeeData(employeeData, assessmentConfigId) {
  try {
    console.log('🧮 为员工数据添加计算得分:', { employeeCount: employeeData.length, assessmentConfigId })

    const updatedData = []
    
    for (const employee of employeeData) {
      const calculatedScore = await calculateUserFinalScore(employee.username, assessmentConfigId)
      
      updatedData.push({
        ...employee,
        calculatedScore: calculatedScore,
        hasCalculatedScore: calculatedScore !== null
      })
    }

    console.log('✅ 员工数据计算得分添加完成')
    return updatedData
  } catch (error) {
    console.error('❌ 为员工数据添加计算得分时发生错误:', error)
    ElMessage.error(`添加计算得分失败: ${error.message}`)
    return employeeData
  }
}

/**
 * 实时计算并更新用户得分（用于实时显示）
 * @param {string} userName - 用户名
 * @param {number} assessmentConfigId - 考核配置ID
 * @param {Function} updateCallback - 更新回调函数
 */
export async function calculateAndUpdateUserScore(userName, assessmentConfigId, updateCallback) {
  try {
    const score = await calculateUserFinalScore(userName, assessmentConfigId)
    
    if (typeof updateCallback === 'function') {
      updateCallback(userName, score)
    }
    
    return score
  } catch (error) {
    console.error('❌ 实时计算并更新用户得分时发生错误:', error)
    if (typeof updateCallback === 'function') {
      updateCallback(userName, null)
    }
    return null
  }
}

// 只导出核心的后端API调用函数
// 其他依赖前端计算服务的函数已被移除
