/**
 * 计算功能测试文件
 * 用于测试公式计算器和参数映射器的功能
 */

import { FormulaCalculator } from './formulaCalculator.js'
import { ParameterMapper } from '../parameterMapper.js'
import { DynamicCalculationService } from '../dynamicCalculationService.js'

/**
 * 测试公式计算器
 */
export function testFormulaCalculator() {
  console.log('🧪 开始测试公式计算器...')
  
  const calculator = new FormulaCalculator()
  
  // 测试用例
  const testCases = [
    {
      name: '基本算术运算',
      formula: '2 + 3 * 4',
      parameters: {},
      expected: 14
    },
    {
      name: '变量替换',
      formula: 'a + b * c',
      parameters: { a: 2, b: 3, c: 4 },
      expected: 14
    },
    {
      name: 'SUM函数',
      formula: 'SUM(a, b, c)',
      parameters: { a: 10, b: 20, c: 30 },
      expected: 60
    },
    {
      name: 'AVG函数',
      formula: 'AVG(a, b, c)',
      parameters: { a: 10, b: 20, c: 30 },
      expected: 20
    },
    {
      name: 'MAX函数',
      formula: 'MAX(a, b, c)',
      parameters: { a: 10, b: 30, c: 20 },
      expected: 30
    },
    {
      name: 'MIN函数',
      formula: 'MIN(a, b, c)',
      parameters: { a: 10, b: 30, c: 20 },
      expected: 10
    },
    {
      name: '复杂嵌套',
      formula: 'SUM(a * 0.4, AVG(b, c) * 0.6)',
      parameters: { a: 100, b: 80, c: 90 },
      expected: 91 // 100*0.4 + 85*0.6 = 40 + 51 = 91
    },
    {
      name: '百分号处理',
      formula: 'a * 40% + b * 60%',
      parameters: { a: 100, b: 80 },
      expected: 88 // 100*0.4 + 80*0.6 = 40 + 48 = 88
    },
    {
      name: 'ROUND函数',
      formula: 'ROUND(a / b, 2)',
      parameters: { a: 22, b: 7 },
      expected: 3.14
    },
    {
      name: 'IF函数',
      formula: 'IF(a > 80, b * 1.2, b * 1.0)',
      parameters: { a: 85, b: 100 },
      expected: 120
    }
  ]
  
  let passedTests = 0
  let totalTests = testCases.length
  
  testCases.forEach((testCase, index) => {
    try {
      const result = calculator.calculate(testCase.formula, testCase.parameters)
      const passed = Math.abs(result - testCase.expected) < 0.01 // 允许小数误差
      
      if (passed) {
        console.log(`✅ 测试 ${index + 1} (${testCase.name}): 通过`)
        passedTests++
      } else {
        console.log(`❌ 测试 ${index + 1} (${testCase.name}): 失败`)
        console.log(`   公式: ${testCase.formula}`)
        console.log(`   参数: ${JSON.stringify(testCase.parameters)}`)
        console.log(`   期望: ${testCase.expected}, 实际: ${result}`)
      }
    } catch (error) {
      console.log(`❌ 测试 ${index + 1} (${testCase.name}): 异常`)
      console.log(`   错误: ${error.message}`)
    }
  })
  
  console.log(`🧪 公式计算器测试完成: ${passedTests}/${totalTests} 通过`)
  return { passed: passedTests, total: totalTests }
}

/**
 * 测试参数映射器
 */
export function testParameterMapper() {
  console.log('🧪 开始测试参数映射器...')
  
  const mapper = new ParameterMapper()
  
  // 模拟 getUserParameterScores 接口返回的数据
  const mockUserData = {
    userName: "test_user",
    userNickName: "测试用户",
    calculationMethod: {
      methodId: 1,
      methodName: "综合评价法",
      formula: "SUM(project_participation * 0.4, project_manager_score * 0.3, department_manager_score * 0.3)",
      assignedParameters: [
        { parameterName: "project_participation", parameterNameCn: "项目参与度" },
        { parameterName: "project_manager_score", parameterNameCn: "项目经理评分" },
        { parameterName: "department_manager_score", parameterNameCn: "部门经理评分" }
      ]
    },
    parameterScores: [
      {
        parameterName: "project_participation",
        parameterNameCn: "项目参与度",
        scoreValue: 85.5,
        dataSource: "assessment_coefficient_allocation"
      },
      {
        parameterName: "project_manager_score",
        parameterNameCn: "项目经理评分",
        scoreValue: 90.0,
        dataSource: "project_manager_score"
      },
      {
        parameterName: "department_manager_score",
        parameterNameCn: "部门经理评分",
        scoreValue: 88.0,
        dataSource: "department_manager_score"
      }
    ]
  }
  
  // 测试参数值提取
  const parameterValues = mapper.extractParameterValues(mockUserData)
  console.log('📊 提取的参数值:', parameterValues)
  
  // 验证结果
  const expectedValues = {
    project_participation: 85.5,
    project_manager_score: 90.0,
    department_manager_score: 88.0
  }
  
  let mapperTestPassed = true
  Object.keys(expectedValues).forEach(key => {
    if (parameterValues[key] !== expectedValues[key]) {
      console.log(`❌ 参数 ${key} 映射失败: 期望 ${expectedValues[key]}, 实际 ${parameterValues[key]}`)
      mapperTestPassed = false
    }
  })
  
  if (mapperTestPassed) {
    console.log('✅ 参数映射器测试通过')
  }
  
  // 测试公式参数提取
  const formula = "SUM(project_participation * 0.4, project_manager_score * 0.3, department_manager_score * 0.3)"
  const extractedParams = mapper.extractParameterNamesFromFormula(formula)
  console.log('📝 从公式提取的参数:', extractedParams)
  
  // 测试参数验证
  const validation = mapper.validateParameterMapping(parameterValues, extractedParams)
  console.log('🔍 参数验证结果:', validation)
  
  return mapperTestPassed
}

/**
 * 测试动态计算服务（模拟测试）
 */
export function testDynamicCalculationService() {
  console.log('🧪 开始测试动态计算服务...')
  
  const service = new DynamicCalculationService()
  
  // 测试公式验证
  const testFormulas = [
    'SUM(a, b, c)',
    'AVG(project_participation, project_manager_score)',
    'a * 0.4 + b * 0.6',
    'IF(a > 80, b * 1.2, b)',
    'ROUND(SUM(a, b) / 2, 2)',
    'invalid_formula((' // 无效公式
  ]
  
  testFormulas.forEach((formula, index) => {
    const validation = service.validateFormula(formula)
    console.log(`🔍 公式 ${index + 1}: ${formula}`)
    console.log(`   验证结果: ${validation.isValid ? '✅ 有效' : '❌ 无效'}`)
    if (!validation.isValid) {
      console.log(`   错误: ${validation.error}`)
    } else {
      console.log(`   提取参数: ${validation.extractedParameters.join(', ')}`)
    }
  })
  
  return true
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行所有计算功能测试...')
  console.log('=' * 50)
  
  const calculatorResult = testFormulaCalculator()
  console.log('=' * 50)
  
  const mapperResult = testParameterMapper()
  console.log('=' * 50)
  
  const serviceResult = testDynamicCalculationService()
  console.log('=' * 50)
  
  console.log('📊 测试总结:')
  console.log(`   公式计算器: ${calculatorResult.passed}/${calculatorResult.total} 通过`)
  console.log(`   参数映射器: ${mapperResult ? '✅ 通过' : '❌ 失败'}`)
  console.log(`   动态计算服务: ${serviceResult ? '✅ 通过' : '❌ 失败'}`)
  
  return {
    calculator: calculatorResult,
    mapper: mapperResult,
    service: serviceResult
  }
}

/**
 * 在浏览器控制台中运行测试
 * 使用方法: 在浏览器控制台中输入 window.runCalculationTests()
 */
if (typeof window !== 'undefined') {
  window.runCalculationTests = runAllTests
  window.testFormulaCalculator = testFormulaCalculator
  window.testParameterMapper = testParameterMapper
  window.testDynamicCalculationService = testDynamicCalculationService
}

// 导出测试函数
export {
  runAllTests,
  testFormulaCalculator,
  testParameterMapper,
  testDynamicCalculationService
}
