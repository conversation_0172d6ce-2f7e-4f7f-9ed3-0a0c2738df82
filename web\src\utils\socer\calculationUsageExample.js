/**
 * 计算功能使用示例
 * 展示如何在 projectAssessmentScore.vue 中集成动态计算功能
 */

import {
  calculateUserFinalScore,
  calculateBatchUserScores,
  calculateWithCustomFormula,
  validateFormulaSync,
  getUserCalculationDetails,
  addCalculatedScoresToEmployeeData
} from './calculationIntegration.js'

/**
 * 示例1: 在员工评分表格中添加计算得分列
 * 在 projectAssessmentScore.vue 中的使用方式
 */
export const exampleAddCalculatedScoreColumn = {
  // 在 Vue 组件的 methods 中添加
  methods: {
    // 加载员工数据时自动计算得分
    async loadEmployeeDataWithScores() {
      try {
        // 获取当前考核配置ID
        const currentConfigId = this.getCurrentConfigId()
        
        // 获取员工基础数据
        const employeeData = this.getEmployeeScoreData()
        
        // 添加计算得分
        const updatedData = await addCalculatedScoresToEmployeeData(employeeData, currentConfigId)
        
        // 更新表格数据
        this.employeeScoreData = updatedData
        
        console.log('✅ 员工数据加载完成，包含计算得分')
      } catch (error) {
        console.error('❌ 加载员工数据时发生错误:', error)
        this.$message.error('加载员工数据失败')
      }
    },

    // 实时计算单个用户得分
    async calculateSingleUserScore(userName) {
      const currentConfigId = this.getCurrentConfigId()
      const score = await calculateUserFinalScore(userName, currentConfigId)
      
      if (score !== null) {
        // 更新表格中的计算得分
        this.updateUserCalculatedScore(userName, score)
        this.$message.success(`用户 ${userName} 计算得分: ${score.toFixed(2)}`)
      }
    },

    // 批量计算所有用户得分
    async calculateAllUserScores() {
      try {
        const currentConfigId = this.getCurrentConfigId()
        const userNames = this.getAllUserNames()
        
        this.$message.info('正在批量计算用户得分...')
        
        const scores = await calculateBatchUserScores(userNames, currentConfigId)
        
        // 更新表格数据
        Object.keys(scores).forEach(userName => {
          if (scores[userName] !== null) {
            this.updateUserCalculatedScore(userName, scores[userName])
          }
        })
        
        this.$message.success('批量计算完成')
      } catch (error) {
        console.error('❌ 批量计算失败:', error)
        this.$message.error('批量计算失败')
      }
    }
  }
}

/**
 * 示例2: 在查看详情弹窗中显示计算过程
 */
export const exampleShowCalculationDetails = {
  methods: {
    // 查看用户计算详情
    async showUserCalculationDetails(row) {
      try {
        const currentConfigId = this.getCurrentConfigId()
        const details = await getUserCalculationDetails(row.username, currentConfigId)
        
        if (details) {
          // 构建详情HTML
          let detailHtml = `
            <div style="text-align: left;">
              <h3 style="color: #409EFF;">计算详情</h3>
              <p><strong>用户:</strong> ${row.username} (${row.nickName})</p>
              <p><strong>计算方法:</strong> ${details.calculationMethod?.methodName || '未分配'}</p>
              <p><strong>计算公式:</strong> ${details.calculationMethod?.formula || '无'}</p>
              
              <h4 style="color: #67C23A;">参数值:</h4>
              <table style="width: 100%; border-collapse: collapse;">
                <tr style="background-color: #f5f5f5;">
                  <th style="border: 1px solid #ddd; padding: 8px;">参数名</th>
                  <th style="border: 1px solid #ddd; padding: 8px;">参数值</th>
                </tr>
          `
          
          Object.keys(details.parameterValues).forEach(paramName => {
            detailHtml += `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${paramName}</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${details.parameterValues[paramName]}</td>
              </tr>
            `
          })
          
          detailHtml += `
              </table>
              
              <h4 style="color: #E6A23C;">验证结果:</h4>
              <p><strong>参数完整性:</strong> ${details.validation.isValid ? '✅ 通过' : '❌ 失败'}</p>
              ${!details.validation.isValid ? `<p><strong>缺失参数:</strong> ${details.validation.missingParameters.join(', ')}</p>` : ''}
            </div>
          `
          
          // 显示弹窗
          this.$msgbox({
            title: '计算详情',
            message: detailHtml,
            dangerouslyUseHTMLString: true,
            showCancelButton: false,
            confirmButtonText: '确定'
          })
        } else {
          this.$message.warning('无法获取计算详情')
        }
      } catch (error) {
        console.error('❌ 显示计算详情失败:', error)
        this.$message.error('获取计算详情失败')
      }
    }
  }
}

/**
 * 示例3: 公式验证功能
 */
export const exampleFormulaValidation = {
  data() {
    return {
      customFormula: '',
      formulaValidation: {
        isValid: false,
        error: null,
        extractedParameters: []
      }
    }
  },
  
  watch: {
    // 监听公式变化，实时验证
    customFormula(newFormula) {
      this.validateFormula(newFormula)
    }
  },
  
  methods: {
    // 验证公式
    validateFormula(formula) {
      if (!formula) {
        this.formulaValidation = {
          isValid: false,
          error: null,
          extractedParameters: []
        }
        return
      }
      
      const validation = validateFormulaSync(formula)
      this.formulaValidation = validation
      
      if (validation.isValid) {
        console.log('✅ 公式验证通过:', validation)
      } else {
        console.log('❌ 公式验证失败:', validation.error)
      }
    },
    
    // 使用自定义公式计算
    async calculateWithCustom(userName) {
      if (!this.formulaValidation.isValid) {
        this.$message.error('公式语法错误，无法计算')
        return
      }
      
      const currentConfigId = this.getCurrentConfigId()
      const result = await calculateWithCustomFormula(userName, currentConfigId, this.customFormula)
      
      if (result !== null) {
        this.$message.success(`自定义公式计算结果: ${result.toFixed(2)}`)
        return result
      }
    }
  }
}

/**
 * 示例4: 在表格中添加计算得分列的模板
 */
export const exampleTableColumnTemplate = `
<!-- 在员工评分表格中添加计算得分列 -->
<el-table-column label="计算得分" width="120" align="center">
  <template #default="{ row }">
    <div v-if="row.hasCalculatedScore">
      <span style="color: #67C23A; font-weight: bold;">
        {{ row.calculatedScore?.toFixed(2) || '0.00' }}
      </span>
      <el-button 
        type="text" 
        size="small" 
        @click="showUserCalculationDetails(row)"
        style="margin-left: 5px;"
      >
        详情
      </el-button>
    </div>
    <div v-else>
      <el-button 
        type="primary" 
        size="small" 
        @click="calculateSingleUserScore(row.username)"
      >
        计算
      </el-button>
    </div>
  </template>
</el-table-column>
`

/**
 * 示例5: 完整的集成代码片段
 */
export const exampleCompleteIntegration = `
// 在 projectAssessmentScore.vue 的 <script setup> 中添加

import {
  calculateUserFinalScore,
  calculateBatchUserScores,
  getUserCalculationDetails,
  validateFormulaSync
} from '@/utils/calculationIntegration.js'

// 添加响应式数据
const calculatedScores = ref({}) // 存储计算得分
const isCalculating = ref(false) // 计算状态

// 添加计算相关方法
const calculateUserScore = async (userName) => {
  try {
    isCalculating.value = true
    const currentConfigId = getCurrentConfigId()
    const score = await calculateUserFinalScore(userName, currentConfigId)
    
    if (score !== null) {
      calculatedScores.value[userName] = score
      ElMessage.success(\`用户 \${userName} 计算得分: \${score.toFixed(2)}\`)
    }
  } catch (error) {
    console.error('计算失败:', error)
    ElMessage.error('计算失败')
  } finally {
    isCalculating.value = false
  }
}

const batchCalculateScores = async () => {
  try {
    isCalculating.value = true
    const currentConfigId = getCurrentConfigId()
    const userNames = getAllUserNames()
    
    ElMessage.info('正在批量计算...')
    const scores = await calculateBatchUserScores(userNames, currentConfigId)
    
    Object.assign(calculatedScores.value, scores)
    ElMessage.success('批量计算完成')
  } catch (error) {
    console.error('批量计算失败:', error)
    ElMessage.error('批量计算失败')
  } finally {
    isCalculating.value = false
  }
}

const showCalculationDetails = async (row) => {
  const currentConfigId = getCurrentConfigId()
  const details = await getUserCalculationDetails(row.username, currentConfigId)
  
  if (details) {
    // 显示详情弹窗的代码...
  }
}
`

// 导出所有示例
export {
  exampleAddCalculatedScoreColumn,
  exampleShowCalculationDetails,
  exampleFormulaValidation,
  exampleTableColumnTemplate,
  exampleCompleteIntegration
}
