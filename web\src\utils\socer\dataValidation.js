/**
 * 数据验证和标准化工具函数
 */

/**
 * 标准化项目成员数据为字符串数组
 * @param {*} members - 成员数据，可能是数组、对象或其他格式
 * @returns {string[]} - 标准化后的字符串数组
 */
export function standardizeProjectMembers(members) {
  // 如果为空或null，返回空数组
  if (!members) {
    return []
  }
  
  // 如果已经是字符串数组，直接返回（过滤空字符串）
  if (Array.isArray(members) && members.every(item => typeof item === 'string')) {
    return members.filter(item => item && item.trim())
  }
  
  // 如果是数组但包含非字符串元素，尝试转换
  if (Array.isArray(members)) {
    return members
      .map(item => {
        if (typeof item === 'string') return item
        if (typeof item === 'number') return String(item)
        if (item && typeof item === 'object' && item.userName) return item.userName
        if (item && typeof item === 'object' && item.username) return item.username
        if (item && typeof item === 'object' && item.id) return String(item.id)
        return null
      })
      .filter(item => item && item.trim()) // 过滤空值和空字符串
  }
  
  // 如果是对象格式，尝试提取memberIds或其他可能的字段
  if (typeof members === 'object' && members !== null) {
    // 检查常见的成员ID字段
    const possibleFields = ['memberIds', 'userIds', 'members', 'users']
    for (const field of possibleFields) {
      if (members[field] && Array.isArray(members[field])) {
        return standardizeProjectMembers(members[field])
      }
    }
    
    // 如果是空对象，返回空数组
    if (Object.keys(members).length === 0) {
      return []
    }
    
    // 如果对象有id或username字段，尝试提取
    if (members.id || members.username || members.userName) {
      const id = members.id || members.username || members.userName
      return [String(id)]
    }
  }
  
  // 其他情况，返回空数组
  console.warn('无法识别的成员数据格式:', members)
  return []
}

/**
 * 验证项目表单数据
 * @param {Object} formData - 项目表单数据
 * @returns {Object|null} - 验证通过返回标准化后的数据，失败返回null
 */
export function validateProjectFormData(formData) {
  try {
    // 深拷贝数据，避免修改原始数据
    const validatedData = JSON.parse(JSON.stringify(formData))
    
    // 标准化成员数据
    validatedData.members = standardizeProjectMembers(validatedData.members)
    
    // 验证必填字段
    const validationRules = [
      {
        field: 'name',
        message: '项目名称不能为空',
        validate: (value) => value && value.trim()
      },
      {
        field: 'departmentId',
        message: '请选择所属部门',
        validate: (value) => value !== undefined && value !== null && value !== ''
      },
      {
        field: 'managerId',
        message: '请选择项目负责人',
        validate: (value) => value && value.trim()
      }
    ]
    
    // 执行验证
    for (const rule of validationRules) {
      if (!rule.validate(validatedData[rule.field])) {
        throw new Error(rule.message)
      }
    }
    
    // 验证项目负责人不在成员列表中
    if (validatedData.members.includes(validatedData.managerId)) {
      throw new Error('项目负责人不能同时在项目成员中')
    }
    
    return validatedData
    
  } catch (error) {
    console.error('项目数据验证失败:', error)
    return {
      error: error.message || '数据格式错误，请检查输入'
    }
  }
}

/**
 * 验证用户选择数据
 * @param {Array} selectedUsers - 选中的用户数组
 * @param {Array} availableUsers - 可选用户列表
 * @returns {string[]} - 验证后的用户名数组
 */
export function validateUserSelection(selectedUsers, availableUsers) {
  if (!Array.isArray(selectedUsers)) {
    return []
  }
  
  return selectedUsers
    .map(userId => {
      const user = availableUsers.find(u => u.userId === userId || u.id === userId)
      return user?.userName || user?.username
    })
    .filter(username => username && username.trim())
}

/**
 * 检查数据格式是否为标准的字符串数组
 * @param {*} data - 要检查的数据
 * @returns {boolean} - 是否为标准格式
 */
export function isStandardStringArray(data) {
  return Array.isArray(data) && data.every(item => typeof item === 'string' && item.trim())
}

/**
 * 格式化错误消息
 * @param {string} field - 字段名
 * @param {string} message - 错误消息
 * @returns {string} - 格式化后的错误消息
 */
export function formatValidationError(field, message) {
  return `${field}: ${message}`
}

/**
 * 批量验证多个字段
 * @param {Object} data - 要验证的数据
 * @param {Array} rules - 验证规则数组
 * @returns {Object} - 验证结果 {isValid: boolean, errors: string[]}
 */
export function validateMultipleFields(data, rules) {
  const errors = []
  
  for (const rule of rules) {
    try {
      if (!rule.validate(data[rule.field])) {
        errors.push(formatValidationError(rule.field, rule.message))
      }
    } catch (error) {
      errors.push(formatValidationError(rule.field, error.message))
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 数据类型转换工具
 */
export const DataConverter = {
  /**
   * 转换为字符串
   */
  toString(value) {
    if (value === null || value === undefined) return ''
    return String(value).trim()
  },
  
  /**
   * 转换为数字
   */
  toNumber(value) {
    const num = Number(value)
    return isNaN(num) ? 0 : num
  },
  
  /**
   * 转换为布尔值
   */
  toBoolean(value) {
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true' || value === '1'
    }
    return Boolean(value)
  },
  
  /**
   * 转换为数组
   */
  toArray(value) {
    if (Array.isArray(value)) return value
    if (value === null || value === undefined) return []
    return [value]
  }
}
