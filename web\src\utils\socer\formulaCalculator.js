/**
 * 公式计算器 - 动态解析和计算考核公式
 * 支持函数：SUM, AVG, MAX, MIN, SQRT, ABS, ROUND, IF, CASE
 * 支持运算符：+, -, *, /, ^, **, (), 比较运算符
 */

class FormulaCalculator {
  constructor() {
    // 支持的函数列表
    this.supportedFunctions = [
      'SUM', 'AVG', 'MAX', 'MIN', 'COUNT',
      'SQRT', 'ABS', 'ROUND',
      'IF', 'CASE'
    ]
    
    // 运算符优先级
    this.operatorPrecedence = {
      '||': 1, '&&': 2,
      '==': 3, '!=': 3, '>=': 3, '<=': 3, '>': 3, '<': 3,
      '+': 4, '-': 4,
      '*': 5, '/': 5, '%': 5,
      '^': 6, '**': 6,
      'unary': 7
    }
  }

  /**
   * 计算公式
   * @param {string} formula - 计算公式
   * @param {Object} parameters - 参数值映射 {参数名: 值}
   * @returns {number|null} 计算结果
   */
  calculate(formula, parameters = {}) {
    try {
      if (!formula || typeof formula !== 'string') {
        throw new Error('公式不能为空')
      }

      console.log('🧮 开始计算公式:', formula)
      console.log('📊 参数值:', parameters)

      // 预处理公式
      const processedFormula = this.preprocessFormula(formula)
      console.log('🔧 预处理后的公式:', processedFormula)

      // 解析并计算
      const result = this.evaluateExpression(processedFormula, parameters)
      console.log('✅ 计算结果:', result)

      return result
    } catch (error) {
      console.error('❌ 公式计算错误:', error.message)
      console.error('📝 公式:', formula)
      console.error('📊 参数:', parameters)
      return null
    }
  }

  /**
   * 预处理公式
   * @param {string} formula - 原始公式
   * @returns {string} 处理后的公式
   */
  preprocessFormula(formula) {
    let processed = formula.trim()
    
    // 移除注释
    processed = processed.replace(/\/\/.*$/gm, '')
    processed = processed.replace(/\/\*[\s\S]*?\*\//g, '')
    
    // 处理百分号
    processed = processed.replace(/(\d+(?:\.\d+)?)%/g, '($1/100)')
    
    // 处理幂运算符 ** 转换为 ^
    processed = processed.replace(/\*\*/g, '^')
    
    // 标准化空格
    processed = processed.replace(/\s+/g, ' ').trim()
    
    return processed
  }

  /**
   * 计算表达式
   * @param {string} expression - 表达式
   * @param {Object} parameters - 参数映射
   * @returns {number} 计算结果
   */
  evaluateExpression(expression, parameters) {
    // 解析表达式为tokens
    const tokens = this.tokenize(expression)
    console.log('🔤 Tokens:', tokens)
    
    // 转换为后缀表达式
    const postfix = this.infixToPostfix(tokens)
    console.log('📝 后缀表达式:', postfix)
    
    // 计算后缀表达式
    return this.evaluatePostfix(postfix, parameters)
  }

  /**
   * 词法分析 - 将表达式分解为tokens
   * @param {string} expression - 表达式
   * @returns {Array} tokens数组
   */
  tokenize(expression) {
    const tokens = []
    let i = 0
    
    while (i < expression.length) {
      const char = expression[i]
      
      // 跳过空格
      if (char === ' ') {
        i++
        continue
      }
      
      // 数字
      if (this.isDigit(char) || char === '.') {
        let num = ''
        while (i < expression.length && (this.isDigit(expression[i]) || expression[i] === '.')) {
          num += expression[i]
          i++
        }
        tokens.push({ type: 'NUMBER', value: parseFloat(num) })
        continue
      }
      
      // 标识符（变量名或函数名）
      if (this.isLetter(char) || char === '_') {
        let identifier = ''
        while (i < expression.length && (this.isAlphaNumeric(expression[i]) || expression[i] === '_')) {
          identifier += expression[i]
          i++
        }
        
        // 检查是否是函数
        if (this.supportedFunctions.includes(identifier.toUpperCase())) {
          tokens.push({ type: 'FUNCTION', value: identifier.toUpperCase() })
        } else {
          tokens.push({ type: 'VARIABLE', value: identifier })
        }
        continue
      }
      
      // 运算符和特殊字符
      if (char === '(' || char === ')' || char === ',') {
        tokens.push({ type: char, value: char })
        i++
        continue
      }
      
      // 双字符运算符
      if (i < expression.length - 1) {
        const twoChar = expression.substr(i, 2)
        if (['>=', '<=', '==', '!=', '&&', '||'].includes(twoChar)) {
          tokens.push({ type: 'OPERATOR', value: twoChar })
          i += 2
          continue
        }
      }
      
      // 单字符运算符
      if (['+', '-', '*', '/', '^', '>', '<'].includes(char)) {
        tokens.push({ type: 'OPERATOR', value: char })
        i++
        continue
      }
      
      // 未知字符
      throw new Error(`未知字符: ${char} at position ${i}`)
    }
    
    return tokens
  }

  /**
   * 中缀表达式转后缀表达式（调度场算法）
   * @param {Array} tokens - tokens数组
   * @returns {Array} 后缀表达式tokens
   */
  infixToPostfix(tokens) {
    const output = []
    const operators = []
    const functionArgCounts = [] // 记录函数参数数量

    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i]

      if (token.type === 'NUMBER' || token.type === 'VARIABLE') {
        output.push(token)
        // 如果在函数内，增加参数计数（只在第一次遇到参数时计数）
        if (functionArgCounts.length > 0 && functionArgCounts[functionArgCounts.length - 1] === 0) {
          functionArgCounts[functionArgCounts.length - 1] = 1
        }
      } else if (token.type === 'FUNCTION') {
        operators.push(token)
        functionArgCounts.push(0) // 初始化参数计数
      } else if (token.type === ',') {
        // 函数参数分隔符，弹出到左括号
        while (operators.length > 0 && operators[operators.length - 1].type !== '(') {
          output.push(operators.pop())
        }
        // 增加当前函数的参数计数
        if (functionArgCounts.length > 0) {
          functionArgCounts[functionArgCounts.length - 1]++
        }
      } else if (token.type === 'OPERATOR') {
        while (operators.length > 0 &&
               operators[operators.length - 1].type === 'OPERATOR' &&
               this.operatorPrecedence[operators[operators.length - 1].value] >= this.operatorPrecedence[token.value]) {
          output.push(operators.pop())
        }
        operators.push(token)
      } else if (token.type === '(') {
        operators.push(token)
      } else if (token.type === ')') {
        while (operators.length > 0 && operators[operators.length - 1].type !== '(') {
          output.push(operators.pop())
        }
        if (operators.length > 0) {
          operators.pop() // 移除左括号
        }
        // 如果栈顶是函数，也要弹出并记录参数数量
        if (operators.length > 0 && operators[operators.length - 1].type === 'FUNCTION') {
          const func = operators.pop()
          let argCount = functionArgCounts.pop() || 0
          // 如果有参数，至少是1个
          if (argCount > 0) {
            argCount = argCount
          } else {
            // 检查是否有单个表达式作为参数
            argCount = 1
          }
          func.argCount = argCount
          output.push(func)
        }
      }
    }

    while (operators.length > 0) {
      output.push(operators.pop())
    }

    return output
  }

  /**
   * 计算后缀表达式
   * @param {Array} postfix - 后缀表达式tokens
   * @param {Object} parameters - 参数映射
   * @returns {number} 计算结果
   */
  evaluatePostfix(postfix, parameters) {
    const stack = []
    
    for (const token of postfix) {
      if (token.type === 'NUMBER') {
        stack.push(token.value)
      } else if (token.type === 'VARIABLE') {
        const value = parameters[token.value]
        if (value === undefined || value === null) {
          console.warn(`⚠️ 参数 ${token.value} 未找到，使用默认值 0`)
          stack.push(0)
        } else {
          stack.push(Number(value))
        }
      } else if (token.type === 'OPERATOR') {
        const result = this.applyOperator(token.value, stack)
        stack.push(result)
      } else if (token.type === 'FUNCTION') {
        const result = this.applyFunction(token, stack)
        stack.push(result)
      }
    }
    
    if (stack.length !== 1) {
      throw new Error('表达式计算错误：栈中剩余元素数量不正确')
    }
    
    return stack[0]
  }

  /**
   * 应用运算符
   * @param {string} operator - 运算符
   * @param {Array} stack - 操作数栈
   * @returns {number} 运算结果
   */
  applyOperator(operator, stack) {
    if (stack.length < 2) {
      throw new Error(`运算符 ${operator} 需要两个操作数`)
    }
    
    const b = stack.pop()
    const a = stack.pop()
    
    switch (operator) {
      case '+': return a + b
      case '-': return a - b
      case '*': return a * b
      case '/': 
        if (b === 0) throw new Error('除零错误')
        return a / b
      case '^': return Math.pow(a, b)
      case '>': return a > b ? 1 : 0
      case '<': return a < b ? 1 : 0
      case '>=': return a >= b ? 1 : 0
      case '<=': return a <= b ? 1 : 0
      case '==': return a === b ? 1 : 0
      case '!=': return a !== b ? 1 : 0
      case '&&': return (a && b) ? 1 : 0
      case '||': return (a || b) ? 1 : 0
      default:
        throw new Error(`不支持的运算符: ${operator}`)
    }
  }

  /**
   * 应用函数
   * @param {Object} funcToken - 函数token（包含函数名和参数数量）
   * @param {Array} stack - 参数栈
   * @returns {number} 函数结果
   */
  applyFunction(funcToken, stack) {
    const funcName = funcToken.value
    const argCount = funcToken.argCount || 0

    switch (funcName) {
      case 'SUM':
        return this.applySumFunction(stack, argCount)
      case 'AVG':
        return this.applyAvgFunction(stack, argCount)
      case 'MAX':
        return this.applyMaxFunction(stack, argCount)
      case 'MIN':
        return this.applyMinFunction(stack, argCount)
      case 'COUNT':
        return this.applyCountFunction(stack, argCount)
      case 'SQRT':
        return this.applySqrtFunction(stack)
      case 'ABS':
        return this.applyAbsFunction(stack)
      case 'ROUND':
        return this.applyRoundFunction(stack)
      case 'IF':
        return this.applyIfFunction(stack)
      default:
        throw new Error(`不支持的函数: ${funcName}`)
    }
  }

  /**
   * SUM函数实现
   */
  applySumFunction(stack, argCount) {
    const args = []
    for (let i = 0; i < argCount; i++) {
      if (stack.length > 0) {
        args.unshift(stack.pop())
      }
    }
    return args.reduce((sum, val) => sum + (Number(val) || 0), 0)
  }

  /**
   * AVG函数实现
   */
  applyAvgFunction(stack, argCount) {
    console.log('🔧 AVG函数调用，参数数量:', argCount, '栈内容:', [...stack])

    // 对于单个表达式参数（如 project_manager_score * project_participation）
    // 我们只需要取栈顶的一个值
    if (argCount === 1 || argCount === 0) {
      if (stack.length > 0) {
        const value = stack.pop()
        console.log('🔧 AVG单个参数值:', value)
        return Number(value) || 0
      }
      return 0
    }

    // 多个参数的情况
    const args = []
    for (let i = 0; i < argCount; i++) {
      if (stack.length > 0) {
        args.unshift(stack.pop())
      }
    }
    console.log('🔧 AVG多个参数:', args)

    if (args.length === 0) return 0
    const sum = args.reduce((sum, val) => sum + (Number(val) || 0), 0)
    const avg = sum / args.length
    console.log('🔧 AVG计算结果:', avg)
    return avg
  }

  /**
   * MAX函数实现
   */
  applyMaxFunction(stack, argCount) {
    const args = []
    for (let i = 0; i < argCount; i++) {
      if (stack.length > 0) {
        args.unshift(stack.pop())
      }
    }
    if (args.length === 0) return 0
    return Math.max(...args.map(val => Number(val) || 0))
  }

  /**
   * MIN函数实现
   */
  applyMinFunction(stack, argCount) {
    const args = []
    for (let i = 0; i < argCount; i++) {
      if (stack.length > 0) {
        args.unshift(stack.pop())
      }
    }
    if (args.length === 0) return 0
    return Math.min(...args.map(val => Number(val) || 0))
  }

  /**
   * COUNT函数实现
   */
  applyCountFunction(stack, argCount) {
    const args = []
    for (let i = 0; i < argCount; i++) {
      if (stack.length > 0) {
        args.unshift(stack.pop())
      }
    }
    return args.filter(val => val !== null && val !== undefined && val !== '').length
  }

  /**
   * SQRT函数实现
   */
  applySqrtFunction(stack) {
    if (stack.length < 1) {
      throw new Error('SQRT函数需要1个参数')
    }
    const value = stack.pop()
    if (value < 0) {
      throw new Error('SQRT函数不能计算负数的平方根')
    }
    return Math.sqrt(Number(value) || 0)
  }

  /**
   * ABS函数实现
   */
  applyAbsFunction(stack) {
    if (stack.length < 1) {
      throw new Error('ABS函数需要1个参数')
    }
    const value = stack.pop()
    return Math.abs(Number(value) || 0)
  }

  /**
   * ROUND函数实现
   */
  applyRoundFunction(stack) {
    if (stack.length < 1) {
      throw new Error('ROUND函数需要至少1个参数')
    }

    let digits = 0
    if (stack.length >= 2) {
      digits = stack.pop()
    }
    const value = stack.pop()

    const multiplier = Math.pow(10, digits)
    return Math.round((Number(value) || 0) * multiplier) / multiplier
  }

  /**
   * IF函数实现
   */
  applyIfFunction(stack) {
    if (stack.length < 3) {
      throw new Error('IF函数需要3个参数：条件、真值、假值')
    }

    const falseValue = stack.pop()
    const trueValue = stack.pop()
    const condition = stack.pop()

    return condition ? trueValue : falseValue
  }

  // 工具函数
  isDigit(char) {
    return char >= '0' && char <= '9'
  }

  isLetter(char) {
    return (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z')
  }

  isAlphaNumeric(char) {
    return this.isLetter(char) || this.isDigit(char)
  }
}

export { FormulaCalculator }
