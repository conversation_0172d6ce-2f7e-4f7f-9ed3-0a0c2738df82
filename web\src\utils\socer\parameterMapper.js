/**
 * 参数映射器 - 将接口返回的评分数据映射到公式参数
 * 处理 getUserParameterScores 接口返回的数据结构
 */

class ParameterMapper {
  constructor() {
    // 参数数据源映射
    this.dataSourceMapping = {
      'assessment_coefficient_allocation': '考核系数分配',
      'project_manager_score': '项目经理评分',
      'department_manager_score': '部门经理评分'
    }
  }

  /**
   * 从用户参数评分数据中提取参数值映射
   * @param {Object} userParameterScores - getUserParameterScores接口返回的用户数据
   * @returns {Object} 参数名到值的映射
   */
  extractParameterValues(userParameterScores) {
    try {
      console.log('🔍 开始提取参数值:', userParameterScores)

      if (!userParameterScores || !userParameterScores.parameterScores) {
        console.warn('⚠️ 用户参数评分数据为空')
        return {}
      }

      const parameterValues = {}
      const parameterScores = userParameterScores.parameterScores

      // 🔧 特殊处理：对于多项目数据，需要分别处理
      const projectBasedParams = {} // 按项目分组的参数
      const globalParams = {} // 全局参数（如department_manager_score）

      // 遍历所有参数评分记录
      parameterScores.forEach(scoreDetail => {
        const parameterName = scoreDetail.parameterName
        const scoreValue = scoreDetail.scoreValue
        const projectId = scoreDetail.projectId

        console.log(`📊 处理参数: ${parameterName}, 值: ${scoreValue}, 项目: ${projectId || '全局'}, 来源: ${scoreDetail.dataSource}`)

        // 如果参数值不为null且不为undefined，则添加到映射中
        if (scoreValue !== null && scoreValue !== undefined) {
          if (projectId) {
            // 项目相关参数
            if (!projectBasedParams[projectId]) {
              projectBasedParams[projectId] = {}
            }
            projectBasedParams[projectId][parameterName] = Number(scoreValue)
          } else {
            // 全局参数
            globalParams[parameterName] = Number(scoreValue)
          }
        } else {
          console.log(`⚠️ 参数 ${parameterName} 的值为空，跳过`)
        }
      })

      console.log('📊 按项目分组的参数:', projectBasedParams)
      console.log('📊 全局参数:', globalParams)

      // 处理全局参数
      Object.assign(parameterValues, globalParams)

      // 🧮 处理多项目参数 - 计算加权平均值
      if (Object.keys(projectBasedParams).length > 0) {
        // 计算 project_manager_score * project_participation 的平均值
        const projectProducts = []

        Object.keys(projectBasedParams).forEach(projectId => {
          const projectData = projectBasedParams[projectId]
          const managerScore = projectData.project_manager_score
          const participation = projectData.project_participation

          if (managerScore !== undefined && participation !== undefined) {
            // 转换参与度为小数（如果大于1）
            const participationDecimal = participation > 1 ? participation / 100 : participation
            const product = managerScore * participationDecimal
            projectProducts.push(product)

            console.log(`📊 项目${projectId}: ${managerScore} * ${participationDecimal} = ${product}`)
          }
        })

        if (projectProducts.length > 0) {
          // 计算平均值
          const avgProduct = projectProducts.reduce((sum, val) => sum + val, 0) / projectProducts.length
          console.log(`📊 项目评分乘积平均值: ${avgProduct}`)

          // 为了兼容现有公式，我们设置一个虚拟的参数值
          // 使得 AVG(project_manager_score * project_participation) 等于我们计算的平均值
          parameterValues.project_manager_score = 1 // 设为1
          parameterValues.project_participation = avgProduct // 设为平均值

          console.log(`🔧 设置虚拟参数值以匹配公式: project_manager_score=1, project_participation=${avgProduct}`)
        }
      }

      console.log('✅ 参数值提取完成:', parameterValues)
      return parameterValues

    } catch (error) {
      console.error('❌ 提取参数值时发生错误:', error)
      return {}
    }
  }

  /**
   * 从批量用户数据中提取指定用户的参数值
   * @param {Object} batchUserData - getUserParameterScores接口返回的批量数据
   * @param {string} userName - 目标用户名
   * @returns {Object} 参数名到值的映射
   */
  extractParameterValuesForUser(batchUserData, userName) {
    try {
      if (!batchUserData || !batchUserData.users) {
        console.warn('⚠️ 批量用户数据为空')
        return {}
      }

      // 查找指定用户的数据
      const userData = batchUserData.users.find(user => user.userName === userName)
      
      if (!userData) {
        console.warn(`⚠️ 未找到用户 ${userName} 的数据`)
        return {}
      }

      return this.extractParameterValues(userData)

    } catch (error) {
      console.error('❌ 从批量数据中提取用户参数值时发生错误:', error)
      return {}
    }
  }

  /**
   * 根据计算方法的分配参数创建默认参数映射
   * @param {Object} calculationMethod - 计算方法对象
   * @param {number} defaultValue - 默认值
   * @returns {Object} 参数名到默认值的映射
   */
  createDefaultParameterMapping(calculationMethod, defaultValue = 0) {
    try {
      const parameterValues = {}

      if (calculationMethod && calculationMethod.assignedParameters) {
        calculationMethod.assignedParameters.forEach(param => {
          parameterValues[param.parameterName] = defaultValue
        })
      }

      console.log('📝 创建默认参数映射:', parameterValues)
      return parameterValues

    } catch (error) {
      console.error('❌ 创建默认参数映射时发生错误:', error)
      return {}
    }
  }

  /**
   * 合并多个参数映射
   * @param {...Object} mappings - 多个参数映射对象
   * @returns {Object} 合并后的参数映射
   */
  mergeParameterMappings(...mappings) {
    try {
      const merged = {}

      mappings.forEach((mapping, index) => {
        if (mapping && typeof mapping === 'object') {
          Object.keys(mapping).forEach(key => {
            if (mapping[key] !== null && mapping[key] !== undefined) {
              merged[key] = mapping[key]
              console.log(`🔗 合并参数 ${key}: ${mapping[key]} (来源映射 ${index + 1})`)
            }
          })
        }
      })

      console.log('✅ 参数映射合并完成:', merged)
      return merged

    } catch (error) {
      console.error('❌ 合并参数映射时发生错误:', error)
      return {}
    }
  }

  /**
   * 验证参数映射的完整性
   * @param {Object} parameterMapping - 参数映射
   * @param {Array} requiredParameters - 必需的参数列表
   * @returns {Object} 验证结果 {isValid: boolean, missingParameters: Array}
   */
  validateParameterMapping(parameterMapping, requiredParameters = []) {
    try {
      const missingParameters = []

      requiredParameters.forEach(paramName => {
        if (parameterMapping[paramName] === undefined || parameterMapping[paramName] === null) {
          missingParameters.push(paramName)
        }
      })

      const isValid = missingParameters.length === 0

      console.log(`🔍 参数映射验证结果: ${isValid ? '✅ 通过' : '❌ 失败'}`)
      if (!isValid) {
        console.log('❌ 缺失参数:', missingParameters)
      }

      return {
        isValid,
        missingParameters
      }

    } catch (error) {
      console.error('❌ 验证参数映射时发生错误:', error)
      return {
        isValid: false,
        missingParameters: requiredParameters
      }
    }
  }

  /**
   * 从公式中提取参数名列表
   * @param {string} formula - 计算公式
   * @returns {Array} 参数名列表
   */
  extractParameterNamesFromFormula(formula) {
    try {
      if (!formula || typeof formula !== 'string') {
        return []
      }

      // 使用正则表达式匹配变量名（字母开头，包含字母、数字、下划线）
      const parameterRegex = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g
      const matches = formula.match(parameterRegex) || []

      // 过滤掉函数名和关键字
      const functionNames = ['SUM', 'AVG', 'MAX', 'MIN', 'COUNT', 'SQRT', 'ABS', 'ROUND', 'IF', 'CASE']
      const keywords = ['true', 'false', 'null', 'undefined']
      
      const parameters = matches.filter(match => {
        const upperMatch = match.toUpperCase()
        return !functionNames.includes(upperMatch) && !keywords.includes(upperMatch)
      })

      // 去重
      const uniqueParameters = [...new Set(parameters)]

      console.log('📝 从公式中提取的参数:', uniqueParameters)
      return uniqueParameters

    } catch (error) {
      console.error('❌ 从公式中提取参数名时发生错误:', error)
      return []
    }
  }

  /**
   * 格式化参数值用于显示
   * @param {Object} parameterMapping - 参数映射
   * @returns {Object} 格式化后的参数映射
   */
  formatParameterValues(parameterMapping) {
    try {
      const formatted = {}

      Object.keys(parameterMapping).forEach(key => {
        const value = parameterMapping[key]
        if (typeof value === 'number') {
          // 保留2位小数
          formatted[key] = Math.round(value * 100) / 100
        } else {
          formatted[key] = value
        }
      })

      return formatted

    } catch (error) {
      console.error('❌ 格式化参数值时发生错误:', error)
      return parameterMapping
    }
  }
}

export { ParameterMapper }
