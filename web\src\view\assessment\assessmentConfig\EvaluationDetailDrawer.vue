<template>
  <el-drawer
    v-model="visible"
    :title="`考核配置「${configName}」的评价详情`"
    :size="'95%'"
    destroy-on-close
    :before-close="handleClose"
  >
    <div v-loading="loading" class="evaluation-detail-container">
      <!-- 评价详情表格 -->
      <div class="evaluation-table">
        <el-table
          :data="evaluationData"
          stripe
          style="width: 100%; height: 100%"
          :default-sort="{ prop: 'status', order: 'ascending' }"
        >
          <el-table-column label="姓名" prop="userNickName" min-width="100" fixed="left" />
          <el-table-column label="所在部门" prop="departmentName" min-width="120" />
          <el-table-column label="员工编号" prop="employeeId" min-width="120" />
          <el-table-column label="计算方法" min-width="150">
            <template #default="scope">
              {{ scope.row.calculationMethod?.methodName || '未设置' }}
            </template>
          </el-table-column>

          <!-- 考核系数分配列 -->
          <el-table-column label="考核系数分配" min-width="200" v-if="hasProjectParticipation">
            <template #default="scope">
              <div v-if="scope.row.projectDetails && scope.row.projectDetails.length > 0" style="margin: -12px; padding: 12px;">
                <div
                  v-for="(project, index) in scope.row.projectDetails"
                  :key="index"
                  :style="{ borderBottom: index < scope.row.projectDetails.length - 1 ? '1px solid #e4e7ed' : 'none', paddingBottom: '4px', marginBottom: '4px', marginLeft: '-12px', marginRight: '-12px', paddingLeft: '12px', paddingRight: '12px' }"
                >
                  <div>
                    <span
                      :title="project.projectName"
                      style="display: inline-block; max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; vertical-align: top;"
                    >
                      {{ project.projectName }}
                    </span>
                    - {{ project.coefficient }}
                  </div>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 项目负责人评分列 -->
          <el-table-column label="项目负责人评分" min-width="150" v-if="hasProjectManagerScore">
            <template #default="scope">
              <div v-if="scope.row.projectDetails && scope.row.projectDetails.length > 0" style="margin: -12px; padding: 12px;">
                <div
                  v-for="(project, index) in scope.row.projectDetails"
                  :key="index"
                  :style="{ borderBottom: index < scope.row.projectDetails.length - 1 ? '1px solid #e4e7ed' : 'none', paddingBottom: '4px', marginBottom: '4px', marginLeft: '-12px', marginRight: '-12px', paddingLeft: '12px', paddingRight: '12px' }"
                >
                  <div>
                    <span v-if="project.projectManagerScore">
                      {{ project.projectManagerScore }}
                      <span v-if="project.isSubstituted" class="substituted-label">（部门负责人评分替代）</span>
                    </span>
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 部门负责人评分列 -->
          <el-table-column label="部门负责人评分" min-width="150" v-if="hasDepartmentManagerScore">
            <template #default="scope">
              <span v-if="scope.row.departmentManagerScore">{{ scope.row.departmentManagerScore }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="奖金" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.bonus !== null && scope.row.bonus !== undefined">¥{{ scope.row.bonus.toLocaleString() }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="问题描述" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.issues && scope.row.issues.length > 0">
                <div v-for="(issue, index) in scope.row.issues" :key="index">
                  {{ issue }}
                </div>
              </div>
              <span v-else>无</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportData">导出数据</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getEvaluationDetailsByConfigId } from '@/api/assessment/assessmentData'

defineOptions({
  name: 'EvaluationDetailDrawer'
})

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  configId: {
    type: Number,
    default: null
  },
  configName: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const evaluationData = ref([])
const statistics = ref({})


// 获取评估详情数据
const fetchEvaluationDetails = async () => {
  if (!props.configId) {
    ElMessage.warning('考核配置ID不能为空')
    return
  }

  loading.value = true
  try {
    const response = await getEvaluationDetailsByConfigId(props.configId)
    console.log('API响应数据:', response) // 调试日志

    if (response.code === 0) {
      console.log('响应数据结构:', response.data) // 调试日志
      evaluationData.value = response.data.users || []
      statistics.value = response.data.statistics || {}

      console.log('设置的用户数据:', evaluationData.value) // 调试日志
      console.log('统计数据:', statistics.value) // 调试日志



      ElMessage.success(`获取评估详情成功，共${evaluationData.value.length}条数据`)
    } else {
      ElMessage.error(response.msg || '获取评估详情失败')
    }
  } catch (error) {
    console.error('获取评估详情失败:', error)
    ElMessage.error('获取评估详情失败')
  } finally {
    loading.value = false
  }
}



// 计算属性：判断是否显示特定列
const hasProjectParticipation = computed(() => {
  return evaluationData.value.some(user =>
    user.projectDetails && user.projectDetails.length > 0
  )
})

const hasProjectManagerScore = computed(() => {
  return evaluationData.value.some(user =>
    user.projectDetails && user.projectDetails.some(project =>
      project.projectManagerScore !== null && project.projectManagerScore !== undefined
    )
  )
})

const hasDepartmentManagerScore = computed(() => {
  return evaluationData.value.some(user =>
    user.departmentManagerScore !== null && user.departmentManagerScore !== undefined
  )
})

// 监听抽屉打开状态，自动获取数据
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.configId) {
    fetchEvaluationDetails()
  }
})

/*
// 假数据（保留作为备用，现在使用真实数据）
const mockData = ref([
  {
    userNickName: '张三',
    employeeId: 'EMP001',
    calculationMethod: '项目参与度+项目负责人评分',
    status: 'normal',
    projectParticipation: 85,
    projectDetails: [
      { projectName: '智能客服系统开发项目', coefficient: 0.8, projectManagerScore: 4.5 },
      { projectName: '数据分析平台', coefficient: 0.2, projectManagerScore: 4.2 }
    ],
    departmentManagerScore: 4.2,
    bonus: 8500,
    issues: []
  }
  // ... 其他假数据
])
*/





// 关闭抽屉
const handleClose = () => {
  visible.value = false
  emit('close')
}

// 导出数据
const exportData = async () => {
  try {
    ElMessage.info('正在计算最终结果并准备导出数据...')

    // 动态导入xlsx和计算服务
    const XLSX = await import('xlsx')
    const { calculateByRule } = await import('@/api/assessment/calculationMethods')

    // 批量计算所有用户的最终得分
    const finalScores = {}

    for (const user of evaluationData.value) {
      try {
        // 检查是否有计算方法
        if (!user.calculationMethod || !user.calculationMethod.methodId) {
          console.warn(`❌ 用户 ${user.username} 未分配计算方法`)
          finalScores[user.username] = null
          continue
        }

        // 直接使用evaluationData中的数据准备计算参数
        console.log(`🔍 用户 ${user.username} 原始数据:`, {
          departmentManagerScore: user.departmentManagerScore,
          projectDetails: user.projectDetails
        })

        const ruleCalculationData = {
          department_manager_score: user.departmentManagerScore || 0,
          project_data: user.projectDetails?.map(project => {
            // 系数可能已经是小数形式，需要检查
            const participation = project.coefficient > 1 ? project.coefficient / 100 : project.coefficient
            return {
              project_id: project.projectId,
              project_manager_score: project.projectManagerScore || 0,
              project_participation: participation,
              is_leader_of_this_project: project.isSubstituted || false // 使用替代标识判断是否为负责人
            }
          }) || []
        }

        console.log(`🔍 用户 ${user.username} 转换后的计算数据:`, ruleCalculationData)

        // 检查数据完整性
        const hasValidData = user.status === 'normal' &&
                           (user.departmentManagerScore !== null ||
                            (user.projectDetails && user.projectDetails.length > 0))

        if (!hasValidData) {
          console.warn(`❌ 用户 ${user.username} 数据不完整，跳过计算`)
          finalScores[user.username] = null
          continue
        }

        // 调用规则计算API
        const ruleResponse = await calculateByRule({
          methodId: user.calculationMethod.methodId,
          parameters: ruleCalculationData
        })

        if (ruleResponse.code === 0 && ruleResponse.data && ruleResponse.data.success) {
          finalScores[user.username] = ruleResponse.data.result
          console.log(`✅ 用户 ${user.username} 计算成功: ${ruleResponse.data.result}`)
        } else {
          console.warn(`❌ 用户 ${user.username} 规则计算失败: ${ruleResponse.msg}`)
          finalScores[user.username] = null
        }

      } catch (error) {
        console.error(`❌ 计算用户 ${user.username} 时发生错误:`, error)
        finalScores[user.username] = null
      }
    }

    // 准备Excel导出数据
    const excelData = evaluationData.value.map(user => ({
      '姓名': user.userNickName,
      '所在部门': user.departmentName || '未设置',
      '员工编号': user.employeeId,
      '计算方法': user.calculationMethod?.methodName || '未设置',
      '考核系数分配': user.projectDetails?.map(p => `${p.projectName}-${p.coefficient}`).join('; ') || '无',
      '项目负责人评分': user.projectDetails?.map(p => `${p.projectName}: ${p.projectManagerScore || '-'}`).join('; ') || '无',
      '部门负责人评分': user.departmentManagerScore || '无',
      '奖金': user.bonus || '无',
      '最终结果': finalScores[user.username] !== null ? finalScores[user.username]?.toFixed(2) : '计算失败',
      '状态': user.status === 'normal' ? '正常' : user.status === 'incomplete' ? '不完整' : '未参与',
      '问题描述': user.issues?.join('; ') || '无'
    }))

    // 创建工作簿 - 修复XLSX使用方式
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 姓名
      { wch: 20 }, // 所在部门
      { wch: 15 }, // 员工编号
      { wch: 20 }, // 计算方法
      { wch: 30 }, // 考核系数分配
      { wch: 30 }, // 项目负责人评分
      { wch: 15 }, // 部门负责人评分
      { wch: 12 }, // 奖金
      { wch: 12 }, // 最终结果
      { wch: 10 }, // 状态
      { wch: 40 }  // 问题描述
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '评价详情')

    // 生成文件名
    const fileName = `评价详情_${props.configName}_${new Date().toISOString().slice(0, 10)}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success('Excel文件导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}
</script>

<style scoped>
.evaluation-detail-container {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.evaluation-table {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.evaluation-table .el-table {
  flex: 1;
}





.parameter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.parameter-values {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.parameter-value-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.parameter-name {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.issue-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table .el-table__header {
  background-color: #f8f9fa;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 父表头样式 */
:deep(.el-table__header .el-table__cell) {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

/* 表格等分布局 */
:deep(.el-table) {
  table-layout: fixed;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 12px;
  word-wrap: break-word;
  word-break: break-all;
}

/* 确保表格列自适应宽度 */
:deep(.el-table__body-wrapper) {
  overflow-x: auto;
}

/* 替代评分标签样式 */
.substituted-label {
  font-size: 11px;
  color: #909399;
  font-style: italic;
  margin-left: 4px;
}



/* 标签页样式 */
.detail-tabs .el-tabs__item {
  font-weight: 500;
}

.detail-tabs .el-tabs__item.is-active {
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-overview .el-col {
    margin-bottom: 10px;
  }

  .parameter-values {
    font-size: 12px;
  }

  .parameter-value-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
