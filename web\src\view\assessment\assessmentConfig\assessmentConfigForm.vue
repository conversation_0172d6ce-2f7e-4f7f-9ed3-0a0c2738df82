
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="考核配置名称:" prop="assessmentName">
    <el-input v-model="formData.assessmentName" :clearable="true" placeholder="请输入考核配置名称" />
</el-form-item>
        <el-form-item label="考核类型:" prop="assessmentType">
    <el-input v-model="formData.assessmentType" :clearable="true" placeholder="请输入考核类型" />
</el-form-item>
        <el-form-item label="考核周期:" prop="assessmentPeriod">
    <el-input v-model="formData.assessmentPeriod" :clearable="true" placeholder="请输入考核周期" />
</el-form-item>
        <el-form-item label="是否归档:" prop="isArchived">
    <el-switch v-model="formData.isArchived" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
        <el-form-item label="算法关联:" prop="algorithmRelationId">
    <el-input v-model.number="formData.algorithmRelationId" :clearable="true" placeholder="请输入算法关联" />
</el-form-item>
        <el-form-item label="奖金关联:" prop="bonusRelationId">
    <el-input v-model.number="formData.bonusRelationId" :clearable="true" placeholder="请输入奖金关联" />
</el-form-item>
        <el-form-item label="高分配额关联:" prop="scoreQuotaId">
    <el-input v-model.number="formData.scoreQuotaId" :clearable="true" placeholder="请输入高分配额关联" />
</el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="删除时间:" prop="deletedAt">
    <el-date-picker v-model="formData.deletedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createAssessmentConfig,
  updateAssessmentConfig,
  findAssessmentConfig
} from '@/api/assessment/assessmentConfig'

defineOptions({
    name: 'AssessmentConfigForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            assessmentName: '',
            assessmentType: '',
            assessmentPeriod: '',
            isArchived: false,
            algorithmRelationId: undefined,
            bonusRelationId: undefined,
            scoreQuotaId: undefined,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: new Date(),
        })
// 验证规则
const rule = reactive({
               assessmentName : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               assessmentType : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               assessmentPeriod : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               algorithmRelationId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               bonusRelationId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               scoreQuotaId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findAssessmentConfig({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createAssessmentConfig(formData.value)
               break
             case 'update':
               res = await updateAssessmentConfig(formData.value)
               break
             default:
               res = await createAssessmentConfig(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
