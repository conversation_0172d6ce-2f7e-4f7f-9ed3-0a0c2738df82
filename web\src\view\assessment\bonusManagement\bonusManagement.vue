<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
            <el-form-item label="名称" prop="bonusName">
  <el-input v-model="searchInfo.bonusName" placeholder="搜索条件" />
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="名称" prop="bonusName" width="120" />

            <el-table-column label="部门奖金分配" prop="departmentAllocations" width="200">
    <template #default="scope">
        <el-popover
          placement="right"
          :width="400"
          trigger="hover"
        >
          <template #default>
            <el-table :data="formatDepartmentAllocations(scope.row.departmentAllocations)" border>
              <el-table-column prop="departmentName" label="部门名称" width="150" />
              <el-table-column prop="allocatedAmount" label="分配金额" width="100">
                <template #default="props">
                  {{ props.row.allocatedAmount.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="remainingAmount" label="剩余金额" width="100">
                <template #default="props">
                  {{ props.row.remainingAmount.toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
          </template>
          <template #reference>
            <el-tag type="info">查看部门分配</el-tag>
          </template>
        </el-popover>
    </template>
</el-table-column>
            <el-table-column align="left" label="总金额" width="120">
              <template #default="scope">
                {{ calculateTotalAmount(scope.row.departmentAllocations).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column align="left" label="剩余总额" width="120">
              <template #default="scope">
                {{ calculateRemainingAmount(scope.row.departmentAllocations).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column align="left" label="描述" prop="description" width="120" />

            <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="更新时间" prop="updatedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateBonusManagementFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="名称:" prop="bonusName">
    <el-input v-model="formData.bonusName" :clearable="true" placeholder="请输入名称" />
</el-form-item>
            <el-form-item label="部门奖金分配:" prop="departmentAllocations">
    <div class="department-allocations-container">
      <div class="department-allocations-header">
        <el-button type="primary" size="small" @click="addDepartmentAllocation">添加部门分配</el-button>
        <div>总金额: {{ calculateTotalAmount(formData.departmentAllocations).toFixed(2) }}</div>
      </div>
      
      <el-table :data="departmentAllocationsList" border style="width: 100%; margin-top: 10px;">
        <el-table-column label="部门">
          <template #default="scope">
            <el-select
              v-model="scope.row.departmentId"
              filterable
              placeholder="选择部门"
              style="width: 100%"
              @change="(val) => handleDepartmentChange(val, scope.$index)"
            >
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.ID"
                :label="dept.name"
                :value="dept.ID"
              />
            </el-select>
          </template>
        </el-table-column>
        <!-- <el-table-column label="部门名称">
          <template #default="scope">
            {{ getDepartmentName(scope.row.departmentId) }}
          </template>
        </el-table-column> -->
        <el-table-column label="分配金额">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.allocatedAmount"
              :min="0"
              :precision="2"
              :step="100"
              style="width: 100%"
              @change="updateDepartmentAllocations"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="已使用金额">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.usedAmount"
              :min="0"
              :max="scope.row.allocatedAmount"
              :precision="2"
              :step="100"
              style="width: 100%"
              @change="updateDepartmentAllocations"
            />
          </template>
        </el-table-column> -->
        <el-table-column label="剩余金额">
          <template #default="scope">
            {{ (scope.row.allocatedAmount - scope.row.usedAmount).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button
              type="danger"
              size="small"
              @click="removeDepartmentAllocation(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
</el-form-item>
            <el-form-item label="描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入描述" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="名称">
    {{ detailFrom.bonusName }}
</el-descriptions-item>
                    <el-descriptions-item label="部门奖金分配">
    <el-table :data="formatDepartmentAllocations(detailFrom.departmentAllocations)" border style="width: 100%">
      <el-table-column prop="departmentName" label="部门名称" width="150" />
      <el-table-column prop="allocatedAmount" label="分配金额" width="120">
        <template #default="scope">
          {{ scope.row.allocatedAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="usedAmount" label="已使用金额" width="120">
        <template #default="scope">
          {{ scope.row.usedAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="remainingAmount" label="剩余金额" width="120">
        <template #default="scope">
          {{ scope.row.remainingAmount.toFixed(2) }}
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 10px; font-weight: bold;">
      总金额: {{ calculateTotalAmount(detailFrom.departmentAllocations).toFixed(2) }}
      &nbsp;&nbsp;|&nbsp;&nbsp;
      剩余总额: {{ calculateRemainingAmount(detailFrom.departmentAllocations).toFixed(2) }}
    </div>
</el-descriptions-item>
                    <el-descriptions-item label="描述">
    {{ detailFrom.description }}
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ detailFrom.createdAt }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ detailFrom.updatedAt }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createBonusManagement,
  deleteBonusManagement,
  deleteBonusManagementByIds,
  updateBonusManagement,
  findBonusManagement,
  getBonusManagementList
} from '@/api/assessment/bonusManagement'

// 导入组织相关API
import { getOrganizationalTree } from '@/plugin/organizational/api/organizational'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, computed, onMounted } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'BonusManagement'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            bonusName: '',
            departmentAllocations: [],
            description: '',
            createdAt: new Date(),
            updatedAt: new Date(),
        })

// 部门选项
const departmentOptions = ref([])
// 部门分配列表（用于表格展示）
const departmentAllocationsList = ref([])

// 验证规则
const rule = reactive({
               bonusName : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               departmentAllocations : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getBonusManagementList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 获取部门树
const getDepartmentTree = async () => {
  try {
    const res = await getOrganizationalTree()
    if (res.code === 0) {
      // 递归提取所有部门
      const extractDepartments = (nodes) => {
        let departments = []
        if (!nodes || !nodes.length) return departments
        
        nodes.forEach(node => {
          // 类型2表示部门
          if (node.type === 2) {
            departments.push({
              ID: node.ID,
              name: node.name
            })
          }
          
          if (node.children && node.children.length) {
            departments = [...departments, ...extractDepartments(node.children)]
          }
        })
        
        return departments
      }
      
      departmentOptions.value = extractDepartments(res.data)
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 初始化
onMounted(() => {
  getTableData()
  getDepartmentTree()
})

// 格式化部门分配数据用于显示
const formatDepartmentAllocations = (allocations) => {
  if (!allocations) return []
  
  try {
    const allocs = typeof allocations === 'string' ? JSON.parse(allocations) : allocations
    return allocs.map(item => ({
      ...item,
      departmentName: getDepartmentName(item.departmentId),
      remainingAmount: item.allocatedAmount - item.usedAmount
    }))
  } catch (error) {
    console.error('格式化部门分配数据失败:', error)
    return []
  }
}

// 根据部门ID获取部门名称
const getDepartmentName = (departmentId) => {
  const department = departmentOptions.value.find(dept => dept.ID === departmentId)
  return department ? department.name : `部门${departmentId}`
}

// 计算总金额
const calculateTotalAmount = (allocations) => {
  if (!allocations) return 0
  
  try {
    const allocs = typeof allocations === 'string' ? JSON.parse(allocations) : allocations
    return allocs.reduce((sum, item) => sum + (Number(item.allocatedAmount) || 0), 0)
  } catch (error) {
    console.error('计算总金额失败:', error)
    return 0
  }
}

// 计算剩余总额
const calculateRemainingAmount = (allocations) => {
  if (!allocations) return 0
  
  try {
    const allocs = typeof allocations === 'string' ? JSON.parse(allocations) : allocations
    return allocs.reduce((sum, item) => {
      const allocated = Number(item.allocatedAmount) || 0
      const used = Number(item.usedAmount) || 0
      return sum + (allocated - used)
    }, 0)
  } catch (error) {
    console.error('计算剩余总额失败:', error)
    return 0
  }
}

// 添加部门分配
const addDepartmentAllocation = () => {
  departmentAllocationsList.value.push({
    departmentId: null,
    departmentName: '',
    allocatedAmount: 0,
    usedAmount: 0,
    remainingAmount: 0
  })
  updateDepartmentAllocations()
}

// 移除部门分配
const removeDepartmentAllocation = (index) => {
  departmentAllocationsList.value.splice(index, 1)
  updateDepartmentAllocations()
}

// 处理部门选择变化
const handleDepartmentChange = (departmentId, index) => {
  const department = departmentOptions.value.find(dept => dept.ID === departmentId)
  if (department) {
    departmentAllocationsList.value[index].departmentName = department.name
  }
  updateDepartmentAllocations()
}

// 更新部门分配数据到formData
const updateDepartmentAllocations = () => {
  formData.value.departmentAllocations = departmentAllocationsList.value.map(item => ({
    departmentId: item.departmentId,
    departmentName: getDepartmentName(item.departmentId),
    allocatedAmount: Number(item.allocatedAmount) || 0,
    usedAmount: Number(item.usedAmount) || 0,
    remainingAmount: (Number(item.allocatedAmount) || 0) - (Number(item.usedAmount) || 0)
  }))
}

// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteBonusManagementFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteBonusManagementByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateBonusManagementFunc = async(row) => {
    const res = await findBonusManagement({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        // 初始化部门分配列表
        departmentAllocationsList.value = formatDepartmentAllocations(formData.value.departmentAllocations)
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteBonusManagementFunc = async (row) => {
    const res = await deleteBonusManagement({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    formData.value = {
        bonusName: '',
        departmentAllocations: [],
        description: '',
        createdAt: new Date(),
        updatedAt: new Date(),
    }
    departmentAllocationsList.value = []
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        bonusName: '',
        departmentAllocations: [],
        description: '',
        createdAt: new Date(),
        updatedAt: new Date(),
    }
    departmentAllocationsList.value = []
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              
              // 验证部门分配
              if (!formData.value.departmentAllocations || formData.value.departmentAllocations.length === 0) {
                ElMessage({
                  type: 'warning',
                  message: '请至少添加一个部门分配'
                })
                btnLoading.value = false
                return
              }
              
              // 验证每个部门分配是否选择了部门
              const invalidDept = formData.value.departmentAllocations.find(item => !item.departmentId)
              if (invalidDept) {
                ElMessage({
                  type: 'warning',
                  message: '请为所有分配选择部门'
                })
                btnLoading.value = false
                return
              }
              
              // 验证部门是否重复
              const deptIds = formData.value.departmentAllocations.map(item => item.departmentId)
              if (new Set(deptIds).size !== deptIds.length) {
                ElMessage({
                  type: 'warning',
                  message: '存在重复的部门分配，请检查'
                })
                btnLoading.value = false
                return
              }
              
              // 计算总金额和剩余金额
              const totalAmount = calculateTotalAmount(formData.value.departmentAllocations)
              const remainingAmount = calculateRemainingAmount(formData.value.departmentAllocations)
              
              // 添加总金额和剩余金额到表单数据
              formData.value.totalAmount = totalAmount
              formData.value.remainingAmount = remainingAmount
              
              let res
              switch (type.value) {
                case 'create':
                  res = await createBonusManagement(formData.value)
                  break
                case 'update':
                  res = await updateBonusManagement(formData.value)
                  break
                default:
                  res = await createBonusManagement(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findBonusManagement({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>
.department-allocations-container {
  width: 100%;
}

.department-allocations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>