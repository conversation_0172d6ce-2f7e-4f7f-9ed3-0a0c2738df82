
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
            <el-form-item label="配额名称" prop="quotaName">
  <el-input v-model="searchInfo.quotaName" placeholder="搜索条件" />
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="配额名称" prop="quotaName" width="120" />

            <el-table-column label="部门高分配额详情" prop="departmentQuotas" width="200">
    <template #default="scope">
        <el-popover
          placement="right"
          :width="400"
          trigger="hover"
        >
          <template #default>
            <el-table :data="formatDepartmentQuotas(scope.row.departmentQuotas)" border>
              <el-table-column prop="departmentName" label="部门名称" width="150" />
              <el-table-column prop="quotaAmount" label="配额数量" width="100">
                <template #default="props">
                  {{ props.row.quotaAmount }}
                </template>
              </el-table-column>
              <el-table-column prop="usedAmount" label="已使用数量" width="100">
                <template #default="props">
                  {{ props.row.usedAmount }}
                </template>
              </el-table-column>
            </el-table>
          </template>
          <template #reference>
            <el-tag type="info">查看部门配额</el-tag>
          </template>
        </el-popover>
    </template>
</el-table-column>
            <el-table-column align="left" label="总配额" width="120">
              <template #default="scope">
                {{ calculateTotalQuota(scope.row.departmentQuotas) }}
              </template>
            </el-table-column>
            <el-table-column align="left" label="剩余总配额" width="120">
              <template #default="scope">
                {{ calculateRemainingQuota(scope.row.departmentQuotas) }}
              </template>
            </el-table-column>
            <el-table-column align="left" label="描述" prop="description" width="120" />

            <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="更新时间" prop="updatedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="删除时间" prop="deletedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.deletedAt) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateScoreQuotaManagementFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="配额名称:" prop="quotaName">
    <el-input v-model="formData.quotaName" :clearable="true" placeholder="请输入配额名称" />
</el-form-item>
            <el-form-item label="部门高分配额详情:" prop="departmentQuotas">
    <div class="department-quotas-container">
      <div class="department-quotas-header">
        <el-button type="primary" size="small" @click="addDepartmentQuota">添加部门配额</el-button>
        <!-- <div>总配额: {{ calculateTotalQuota(formData.departmentQuotas) }}</div> -->
      </div>

      <el-table :data="departmentQuotasList" border style="width: 100%; margin-top: 10px;">
        <el-table-column label="部门">
          <template #default="scope">
            <el-select
              v-model="scope.row.departmentId"
              filterable
              placeholder="选择部门"
              style="width: 100%"
              @change="(val) => handleDepartmentChange(val, scope.$index)"
            >
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.ID"
                :label="dept.name"
                :value="dept.ID"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="配额数量">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.quotaAmount"
              :min="0"
              :precision="0"
              :step="1"
              style="width: 100%"
              @change="updateDepartmentQuotas"
            />
          </template>
        </el-table-column>
        <el-table-column label="已使用数量">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.usedAmount"
              :min="0"
              :max="scope.row.quotaAmount"
              :precision="0"
              :step="1"
              style="width: 100%"
              @change="updateDepartmentQuotas"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button
              type="danger"
              size="small"
              @click="removeDepartmentQuota(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
</el-form-item>
            <el-form-item label="描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入描述" />
</el-form-item>
            <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="删除时间:" prop="deletedAt">
    <el-date-picker v-model="formData.deletedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="配额名称">
    {{ detailFrom.quotaName }}
</el-descriptions-item>
                    <el-descriptions-item label="部门高分配额详情">
    <el-table :data="formatDepartmentQuotas(detailFrom.departmentQuotas)" border style="width: 100%">
      <el-table-column prop="departmentName" label="部门名称" width="150" />
      <el-table-column prop="quotaAmount" label="配额数量" width="120">
        <template #default="scope">
          {{ scope.row.quotaAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="usedAmount" label="已使用数量" width="120">
        <template #default="scope">
          {{ scope.row.usedAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="remainingAmount" label="剩余数量" width="120">
        <template #default="scope">
          {{ scope.row.remainingAmount }}
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 10px; font-weight: bold;">
      总配额: {{ calculateTotalQuota(detailFrom.departmentQuotas) }}
      &nbsp;&nbsp;|&nbsp;&nbsp;
      剩余配额: {{ calculateRemainingQuota(detailFrom.departmentQuotas) }}
    </div>
</el-descriptions-item>
                    <el-descriptions-item label="描述">
    {{ detailFrom.description }}
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ detailFrom.createdAt }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ detailFrom.updatedAt }}
</el-descriptions-item>
                    <el-descriptions-item label="删除时间">
    {{ detailFrom.deletedAt }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createScoreQuotaManagement,
  deleteScoreQuotaManagement,
  deleteScoreQuotaManagementByIds,
  updateScoreQuotaManagement,
  findScoreQuotaManagement,
  getScoreQuotaManagementList
} from '@/api/assessment/scoreQuotaManagement'

// 导入组织相关API
import { getOrganizationalTree } from '@/plugin/organizational/api/organizational'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, computed, onMounted } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'ScoreQuotaManagement'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            quotaName: '',
            departmentQuotas: [],
            description: '',
        })

// 部门选项
const departmentOptions = ref([])
// 部门配额列表（用于表格展示）
const departmentQuotasList = ref([])


// 验证规则
const rule = reactive({
               quotaName : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               departmentQuotas : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getScoreQuotaManagementList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 获取部门树
const getDepartmentTree = async () => {
  try {
    const res = await getOrganizationalTree()
    if (res.code === 0) {
      // 递归提取所有部门
      const extractDepartments = (nodes) => {
        let departments = []
        if (!nodes || !nodes.length) return departments

        nodes.forEach(node => {
          // 类型2表示部门
          if (node.type === 2) {
            departments.push({
              ID: node.ID,
              name: node.name
            })
          }

          if (node.children && node.children.length) {
            departments = [...departments, ...extractDepartments(node.children)]
          }
        })

        return departments
      }

      departmentOptions.value = extractDepartments(res.data)
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 初始化
onMounted(() => {
  getTableData()
  getDepartmentTree()
})

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteScoreQuotaManagementFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteScoreQuotaManagementByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 格式化部门配额数据用于显示
const formatDepartmentQuotas = (quotas) => {
  if (!quotas) return []

  try {
    const allocs = typeof quotas === 'string' ? JSON.parse(quotas) : quotas
    return allocs.map(item => ({
      ...item,
      departmentName: getDepartmentName(item.departmentId),
      quotaAmount: Number(item.quotaAmount) || 0,
      usedAmount: Number(item.usedAmount) || 0,
      remainingAmount: (Number(item.quotaAmount) || 0) - (Number(item.usedAmount) || 0)
    }))
  } catch (error) {
    console.error('格式化部门配额数据失败:', error)
    return []
  }
}

// 根据部门ID获取部门名称
const getDepartmentName = (departmentId) => {
  const department = departmentOptions.value.find(dept => dept.ID === departmentId)
  return department ? department.name : `部门${departmentId}`
}

// 计算总配额
const calculateTotalQuota = (quotas) => {
  if (!quotas) return 0

  try {
    const allocs = typeof quotas === 'string' ? JSON.parse(quotas) : quotas
    return allocs.reduce((sum, item) => sum + (Number(item.quotaAmount) || 0), 0)
  } catch (error) {
    console.error('计算总配额失败:', error)
    return 0
  }
}

// 计算剩余总配额
const calculateRemainingQuota = (quotas) => {
  if (!quotas) return 0

  try {
    const allocs = typeof quotas === 'string' ? JSON.parse(quotas) : quotas
    return allocs.reduce((sum, item) => {
      const quota = Number(item.quotaAmount) || 0
      const used = Number(item.usedAmount) || 0
      return sum + (quota - used)
    }, 0)
  } catch (error) {
    console.error('计算剩余总配额失败:', error)
    return 0
  }
}

// 添加部门配额
const addDepartmentQuota = () => {
  departmentQuotasList.value.push({
    departmentId: null,
    departmentName: '',
    quotaAmount: 0,
    usedAmount: 0
  })
  updateDepartmentQuotas()
}

// 移除部门配额
const removeDepartmentQuota = (index) => {
  departmentQuotasList.value.splice(index, 1)
  updateDepartmentQuotas()
}

// 处理部门选择变化
const handleDepartmentChange = (departmentId, index) => {
  const department = departmentOptions.value.find(dept => dept.ID === departmentId)
  if (department) {
    departmentQuotasList.value[index].departmentName = department.name
  }
  updateDepartmentQuotas()
}

// 更新部门配额数据到formData
const updateDepartmentQuotas = () => {
  formData.value.departmentQuotas = departmentQuotasList.value.map(item => ({
    departmentId: item.departmentId,
    departmentName: getDepartmentName(item.departmentId),
    quotaAmount: Number(item.quotaAmount) || 0,
    usedAmount: Number(item.usedAmount) || 0
  }))
}

// 更新行
const updateScoreQuotaManagementFunc = async(row) => {
    const res = await findScoreQuotaManagement({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        // 初始化部门配额列表
        departmentQuotasList.value = formatDepartmentQuotas(formData.value.departmentQuotas)
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteScoreQuotaManagementFunc = async (row) => {
    const res = await deleteScoreQuotaManagement({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    formData.value = {
        quotaName: '',
        departmentQuotas: [],
        description: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: new Date(),
    }
    departmentQuotasList.value = []
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        quotaName: '',
        departmentQuotas: [],
        description: '',
        }
    departmentQuotasList.value = []
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false

              // 验证部门配额
              if (!formData.value.departmentQuotas || formData.value.departmentQuotas.length === 0) {
                ElMessage({
                  type: 'warning',
                  message: '请至少添加一个部门配额'
                })
                btnLoading.value = false
                return
              }

              // 验证每个部门配额是否选择了部门
              const invalidDept = formData.value.departmentQuotas.find(item => !item.departmentId)
              if (invalidDept) {
                ElMessage({
                  type: 'warning',
                  message: '请为所有配额选择部门'
                })
                btnLoading.value = false
                return
              }

              // 验证部门是否重复
              const deptIds = formData.value.departmentQuotas.map(item => item.departmentId)
              if (new Set(deptIds).size !== deptIds.length) {
                ElMessage({
                  type: 'warning',
                  message: '存在重复的部门配额，请检查'
                })
                btnLoading.value = false
                return
              }

              // 计算总配额
              const totalQuota = calculateTotalQuota(formData.value.departmentQuotas)
              const remainingQuota = calculateRemainingQuota(formData.value.departmentQuotas)

              // 添加总配额和剩余配额到表单数据
              formData.value.totalQuota = totalQuota
              formData.value.remainingQuota = remainingQuota

              let res
              switch (type.value) {
                case 'create':
                  res = await createScoreQuotaManagement(formData.value)
                  break
                case 'update':
                  res = await updateScoreQuotaManagement(formData.value)
                  break
                default:
                  res = await createScoreQuotaManagement(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findScoreQuotaManagement({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>
.department-quotas-container {
  width: 100%;
}

.department-quotas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
