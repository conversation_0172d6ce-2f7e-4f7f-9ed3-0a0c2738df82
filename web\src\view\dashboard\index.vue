<template>
  <div>
    <div class="flex gap-4 p-2">
      <div
        class="flex-none w-52 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded p-4"
      >
        <div class="flex justify-between items-center">
          <span class="text font-bold">考核类别</span>
          <el-button type="primary" @click="openDrawer"> 新增 </el-button>
        </div>
        <el-scrollbar class="mt-4" style="height: calc(100vh - 300px)">
          <div v-if="loading" class="text-center py-4">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span class="ml-2">加载中...</span>
          </div>
          <div v-else-if="menuItems.length === 0" class="text-center py-4 text-gray-500">
            暂无数据
          </div>
          <div
            v-else
            v-for="item in menuItems"
            :key="item.id"
            class="rounded flex justify-between items-center px-2 py-4 cursor-pointer mt-2 hover:bg-blue-50 dark:hover:bg-blue-900 bg-gray-50 dark:bg-gray-800 gap-4"
            :class="
              selectID === item.id
                ? 'text-active'
                : 'text-slate-700 dark:text-slate-50'
            "
            @click="toDetail(item)"
          >
            <span class="max-w-[160px] truncate">{{ item.categoryName }}</span>
            <div class="min-w-[40px]">
              <el-icon
                class="text-blue-500"
                @click.stop="updateItem(item)"
              >
                <Edit />
              </el-icon>
              <el-icon
                class="ml-2 text-red-500"
                @click="deleteItem(item)"
              >
                <Delete />
              </el-icon>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div
        class="flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded p-4 flex flex-col"
      >
        <!-- 上半部分内容区域 - 参数设置 -->
        <div class="flex-1 flex flex-col">
          <div class="mb-4">
            <h3 class="text-lg font-bold mb-4">参数设置</h3>

            <!-- 操作按钮区域 -->
            <div class="flex justify-start items-center mb-4">
              <div class="flex gap-2">
                <el-button type="primary" @click="openParameterDrawer">新增</el-button>
                <el-button
                  type="danger"
                  :disabled="!selectedParameters.length"
                  @click="batchDeleteParameters"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 参数表格 -->
            <el-table
              :data="parameterData"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              max-height="300"
              :cell-style="{ padding: '4px 0' }"
              v-loading="parameterLoading"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="parameterName" label="参数名称" min-width="1" />
              <el-table-column prop="roleId" label="关联评分人" min-width="1">
                <template #default="scope">
                  <el-tag :type="getRoleTagType(scope.row.roleId)">
                    {{ getRoleLabel(scope.row.roleId) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="parameterNameCn" label="中文名称" min-width="1" />
              <el-table-column label="操作" min-width="1">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="isSystemParameter(scope.row.parameterName)"
                    @click="editParameter(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    :disabled="isSystemParameter(scope.row.parameterName)"
                    @click="deleteParameter(scope.row)"
                  >
                    删除
                  </el-button>
                  <el-tag
                    v-if="isSystemParameter(scope.row.parameterName)"
                    type="info"
                    size="small"
                    style="margin-left: 8px;"
                  >
                    系统参数
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 分隔线 -->
        <el-divider class="my-1" />

        <!-- 下半部分内容区域 - 计算方法 -->
        <div class="flex-1 flex flex-col">
          <div class="mb-4">
            <h3 class="text-lg font-bold mb-4">计算方法</h3>

            <!-- 操作按钮区域 -->
            <div class="flex justify-start items-center mb-4">
              <div class="flex gap-2">
                <el-button type="primary" @click="openMethodDrawer">新增</el-button>
                <el-button
                  type="danger"
                  :disabled="!selectedMethods.length"
                  @click="batchDeleteMethods"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 计算方法表格 -->
            <el-table
              :data="methodData"
              style="width: 100%"
              @selection-change="handleMethodSelectionChange"
              max-height="300"
              :cell-style="{ padding: '4px 0' }"
              v-loading="methodLoading"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="methodName" label="计算方法名称" min-width="1" />
              <el-table-column prop="description" label="方法描述" min-width="1" />
              <el-table-column label="操作" min-width="1">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editMethod(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteMethod(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <el-drawer
      v-model="drawerFormVisible"
      :size="appStore.drawerSize"
      :show-close="false"
      :before-close="closeDrawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{
            type === 'create' ? '添加考核类别' : '修改考核类别'
          }}</span>
          <div>
            <el-button @click="closeDrawer"> 取 消 </el-button>
            <el-button type="primary" @click="enterDrawer"> 确 定 </el-button>
          </div>
        </div>
      </template>
      <el-form
        ref="drawerForm"
        :model="formData"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item label="类别名称" prop="categoryName">
          <el-input
            v-model="formData.categoryName"
            placeholder="请输入类别名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="类别描述" prop="description">
          <el-input
            v-model="formData.description"
            placeholder="请输入类别描述"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="formData.sortOrder"
            :min="0"
            placeholder="请输入排序值"
            :style="{ width: '100%' }"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 参数设置抽屉 -->
    <el-drawer
      v-model="parameterDrawerVisible"
      :size="appStore.drawerSize"
      :show-close="false"
      :before-close="closeParameterDrawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{
            parameterType === 'create' ? '添加参数' : '修改参数'
          }}</span>
          <div>
            <el-button @click="closeParameterDrawer"> 取 消 </el-button>
            <el-button type="primary" @click="submitParameter"> 确 定 </el-button>
          </div>
        </div>
      </template>
      <el-form
        ref="parameterForm"
        :model="parameterFormData"
        :rules="parameterRules"
        label-width="110px"
      >
        <el-form-item label="参数名称" prop="parameterName">
          <el-input
            v-model="parameterFormData.parameterName"
            placeholder="请输入英文参数名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="关联评分人" prop="roleId">
          <el-select
            v-model="parameterFormData.roleId"
            placeholder="请选择关联评分人"
            clearable
            :loading="authorityLoading"
            :style="{ width: '100%' }"
          >
            <el-option
              v-for="authority in availableAuthorities"
              :key="authority.authorityId"
              :label="authority.disabled ? `${authority.authorityName} (已被使用)` : authority.authorityName"
              :value="authority.authorityId"
              :disabled="authority.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="中文名称" prop="parameterNameCn">
          <el-input
            v-model="parameterFormData.parameterNameCn"
            placeholder="请输入中文名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 计算方法抽屉 -->
    <el-drawer
      v-model="methodDrawerVisible"
      :size="appStore.drawerSize"
      :show-close="false"
      :before-close="closeMethodDrawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{
            methodType === 'create' ? '添加计算方法' : '修改计算方法'
          }}</span>
          <div>
            <el-button @click="closeMethodDrawer"> 取 消 </el-button>
            <el-button type="primary" @click="submitMethod"> 确 定 </el-button>
          </div>
        </div>
      </template>
      <el-form
        ref="methodForm"
        :model="methodFormData"
        :rules="methodRules"
        label-width="110px"
      >
        <el-form-item label="方法名称" prop="methodName">
          <el-input
            v-model="methodFormData.methodName"
            placeholder="请输入计算方法名称"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>
        <el-form-item label="方法描述" prop="description">
          <el-input
            v-model="methodFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入方法描述"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>

        <!-- 参数选择 -->
        <el-form-item label="参数配置">
          <div class="space-y-2">
            <div
              v-for="param in parameterData"
              :key="param.id"
              class="flex items-center"
            >
              <el-checkbox
                v-model="methodFormData.selectedParameters"
                :label="param.id"
                @change="updateAvailableParameters"
              >
                是否需要{{ param.parameterNameCn }}
              </el-checkbox>
            </div>
          </div>
        </el-form-item>

        <!-- 可用参数显示 -->
        <el-form-item label="可用参数" v-if="availableParameters.length > 0">
          <div class="text-sm text-gray-600 bg-gray-50 p-2 rounded">
            {{ availableParameters.join(', ') }}
          </div>
        </el-form-item>

        <!-- 计算公式 -->
        <el-form-item label="计算公式" prop="formula">
          <el-input
            v-model="methodFormData.formula"
            type="textarea"
            :rows="4"
            placeholder="请输入计算公式
支持函数：SUM(a,b,c) AVG(a,b,c) MAX(a,b,c) MIN(a,b,c) SQRT(a) ABS(a) ROUND(a,2) IF(条件,真值,假值)
运算符：+ - * / () ^ (幂运算)
示例：SUM(项目成员精力分配*对应的项目评价+项目负责人精力分配*机构负责人评价)*40%+机构负责人评价*60%"
            clearable
            :style="{ width: '100%' }"
          />
        </el-form-item>

        <!-- 分配人员 -->
        <el-form-item label="分配人员">
          <div v-loading="userLoading" style="min-height: 300px;">
            <el-transfer
              v-model="methodFormData.assignedUsers"
              :data="availableUsers"
              :titles="['可选用户', '已分配用户']"
              :button-texts="['移除', '添加']"
              :format="{
                noChecked: '${total}',
                hasChecked: '${checked}/${total}'
              }"
              filterable
              filter-placeholder="搜索用户"
              style="text-align: left; display: inline-block"
            />
          </div>
          <div class="text-xs text-gray-500 mt-2">
            注意：已被其他计算方法分配的用户将显示为禁用状态
          </div>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Edit, Delete, Loading } from '@element-plus/icons-vue'
  import { useAppStore } from '@/pinia'
  import {
    createAssessmentCategories,
    deleteAssessmentCategories,
    updateAssessmentCategories,
    getAssessmentCategoriesList
  } from '@/api/assessment/assessmentCategories'
  import { getAuthorityList } from '@/api/authority'
  import { getUserList } from '@/api/user'
  // 导入组织相关API
  import {
    getUserLoginList,
    getOrganizationalMember,
    getOrganizationalTree,
    getUser
  } from '@/plugin/organizational/api/organizational'
  import {
    createCalculationParameters,
    deleteCalculationParameters,
    deleteCalculationParametersByIds,
    updateCalculationParameters,
    getCalculationParametersList,
    checkParameterUsage
  } from '@/api/assessment/calculationParameters'
  import {
    createCalculationMethods,
    deleteCalculationMethods,
    deleteCalculationMethodsByIds,
    updateCalculationMethods,
    findCalculationMethods,
    getCalculationMethodsList
  } from '@/api/assessment/calculationMethods'

  defineOptions({
    name: 'Dashboard'
  })

  const appStore = useAppStore()

  const selectID = ref(0)

  const formData = ref({
    categoryName: null,
    description: null,
    sortOrder: 0
  })

  const rules = ref({
    categoryName: [
      {
        required: true,
        message: '请输入类别名称',
        trigger: 'blur'
      }
    ]
  })

  // 考核类别数据
  const menuItems = ref([])
  const loading = ref(false)

  // 参数设置相关数据
  const parameterData = ref([])
  const selectedParameters = ref([])
  const parameterLoading = ref(false)

  // 系统参数列表（禁止编辑和删除）
  const systemParameters = ['project_participation', 'project_manager_score']

  // 检查是否为系统参数
  const isSystemParameter = (parameterName) => {
    return systemParameters.includes(parameterName)
  }

  // 角色数据
  const authorityList = ref([])
  const authorityLoading = ref(false)

  const parameterDrawerVisible = ref(false)
  const parameterType = ref('')
  const parameterFormData = ref({
    parameterName: '',
    roleId: null,
    parameterNameCn: ''
  })

  const parameterRules = ref({
    parameterName: [
      {
        required: true,
        message: '请输入参数名称',
        trigger: 'blur'
      },
      {
        pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
        message: '参数名称必须为英文，只能包含字母、数字和下划线，且不能以数字开头',
        trigger: 'blur'
      }
    ],
    roleId: [
      {
        required: true,
        message: '请选择关联评分人',
        trigger: 'change'
      },
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback()
            return
          }

          // 检查角色是否已被其他参数使用
          const usedRoleIds = new Set()
          parameterData.value.forEach(param => {
            // 如果是编辑模式，排除当前编辑的参数
            if (parameterType.value === 'update' && param.id === parameterFormData.value.id) {
              return
            }

            if (param.roleId) {
              if (typeof param.roleId === 'string' && param.roleId.includes(',')) {
                param.roleId.split(',').forEach(id => {
                  const roleId = parseInt(id.trim())
                  if (!isNaN(roleId)) {
                    usedRoleIds.add(roleId)
                  }
                })
              } else {
                const roleId = parseInt(param.roleId)
                if (!isNaN(roleId)) {
                  usedRoleIds.add(roleId)
                }
              }
            }
          })

          if (usedRoleIds.has(value)) {
            callback(new Error('该评分人已被其他参数关联，请选择其他评分人'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  })

  // 计算可用的角色列表（过滤已被使用的角色）
  const availableAuthorities = computed(() => {
    if (!authorityList.value || !parameterData.value) {
      return authorityList.value || []
    }

    // 获取当前已使用的角色ID列表
    const usedRoleIds = new Set()
    parameterData.value.forEach(param => {
      // 如果是编辑模式，排除当前编辑的参数
      if (parameterType.value === 'update' && param.id === parameterFormData.value.id) {
        return
      }

      if (param.roleId) {
        // 处理单个角色ID
        if (typeof param.roleId === 'string' && param.roleId.includes(',')) {
          // 处理逗号分隔的多个角色ID
          param.roleId.split(',').forEach(id => {
            const roleId = parseInt(id.trim())
            if (!isNaN(roleId)) {
              usedRoleIds.add(roleId)
            }
          })
        } else {
          // 单个角色ID
          const roleId = parseInt(param.roleId)
          if (!isNaN(roleId)) {
            usedRoleIds.add(roleId)
          }
        }
      }
    })

    // 返回带有禁用状态的角色列表
    return authorityList.value.map(authority => ({
      ...authority,
      disabled: usedRoleIds.has(authority.authorityId)
    }))
  })

  // 查询考核类别数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await getAssessmentCategoriesList({
        page: 1,
        pageSize: 999 // 获取所有数据
      })
      if (res.code === 0) {
        menuItems.value = res.data.list || []
        if (menuItems.value.length > 0) {
          selectID.value = menuItems.value[0].id
        }
      }
    } catch (error) {
      console.error('获取考核类别失败:', error)
      ElMessage.error('获取考核类别失败')
    } finally {
      loading.value = false
    }
  }

  getTableData()

  // 获取角色列表
  const getAuthorityListData = async () => {
    authorityLoading.value = true
    try {
      const res = await getAuthorityList()
      if (res.code === 0) {
        authorityList.value = res.data || []
      }
    } catch (error) {
      console.error('获取角色列表失败:', error)
      ElMessage.error('获取角色列表失败')
    } finally {
      authorityLoading.value = false
    }
  }

  getAuthorityListData()

  // 获取参数列表
  const getParameterListData = async () => {
    parameterLoading.value = true
    try {
      const res = await getCalculationParametersList({
        page: 1,
        pageSize: 999 // 获取所有数据
      })
      if (res.code === 0) {
        parameterData.value = res.data.list || []
      }
    } catch (error) {
      console.error('获取参数列表失败:', error)
      ElMessage.error('获取参数列表失败')
    } finally {
      parameterLoading.value = false
    }
  }

  // 获取计算方法列表
  const getMethodListData = async () => {
    methodLoading.value = true
    try {
      const res = await getCalculationMethodsList({
        page: 1,
        pageSize: 999 // 获取所有数据
      })
      if (res.code === 0) {
        methodData.value = res.data.list || []
      }
    } catch (error) {
      console.error('获取计算方法列表失败:', error)
      ElMessage.error('获取计算方法列表失败')
    } finally {
      methodLoading.value = false
    }
  }

  // 获取权限范围内的成员
  const getMembersInScope = async () => {
    try {
      // console.log('开始获取权限范围内的成员...')

      // 获取用户的登录节点权限
      const userLoginRes = await getUserLoginList()
      // console.log('用户登录权限响应:', userLoginRes)

      if (userLoginRes.code !== 0 || !userLoginRes.data) {
        console.warn('获取用户登录权限失败，尝试获取所有用户')
        return await getAllUsers()
      }

      // 去重组织ID
      const orgIds = [...new Set(userLoginRes.data.map(item => item.org_id))]
      // console.log('用户有权限的组织ID列表:', orgIds)

      if (orgIds.length === 0) {
        console.warn('用户没有任何组织权限，获取所有用户')
        return await getAllUsers()
      }

      // 并发获取所有组织的成员，使用正确的参数名 ID
      const memberPromises = orgIds.map(orgId =>
        getOrganizationalMember({
          ID: orgId,  // 使用 ID 而不是 orgId
          page: 1,
          pageSize: 100  // 增加页面大小以获取更多成员
        }).catch(error => {
          console.error(`获取组织${orgId}成员失败:`, error)
          return { code: -1, data: { list: [] } }
        })
      )

      const memberResults = await Promise.all(memberPromises)
      // console.log('所有组织成员API响应:', memberResults)

      // 合并所有成员并去重
      const allMembers = []
      const userIdSet = new Set()

      memberResults.forEach((result, index) => {
        // console.log(`组织${orgIds[index]}的成员API响应:`, result)

        if (result.code === 0 && result.data && result.data.list) {
          const memberList = result.data.list
          // console.log(`组织${orgIds[index]}的成员列表:`, memberList)

          memberList.forEach(member => {
            const userId = member.user_id || member.User?.ID
            const user = member.User || member.user

            if (userId && !userIdSet.has(userId)) {
              userIdSet.add(userId)

              // 获取组织名称，优先使用关联的组织信息
              let orgName = '未知组织'
              if (member.Organizational?.name) {
                orgName = member.Organizational.name
              } else if (member.organizational?.name) {
                orgName = member.organizational.name
              } else if (member.orgName) {
                orgName = member.orgName
              } else {
                // 如果没有组织名称，尝试从组织树中查找
                orgName = getOrgNameById(orgIds[index]) || `组织${orgIds[index]}`
              }

              // 构建用户显示名称
              const userName = user?.nickName || user?.userName || `用户${userId}`

              allMembers.push({
                key: userId,
                label: `${userName} (${orgName})`,
                disabled: false,
                userId: userId,
                userName: user?.userName,
                nickName: user?.nickName,
                orgId: member.org_id || orgIds[index],
                orgName: orgName
              })
            }
          })
        }
      })

      // console.log('权限范围内的最终成员列表:', allMembers)

      // 如果权限范围内没有成员，回退到获取所有用户
      if (allMembers.length === 0) {
        // console.log('权限范围内没有成员，回退到获取所有用户')
        return await getAllUsers()
      }

      return allMembers

    } catch (error) {
      console.error('获取权限范围内成员失败:', error)
      // 出错时回退到获取所有用户
      return await getAllUsers()
    }
  }

  // 回退方案：获取所有用户
  const getAllUsers = async () => {
    try {
      // console.log('获取所有用户列表...')
      const userRes = await getUser()
      // console.log('所有用户API响应:', userRes)

      if (userRes.code !== 0 || !userRes.data) {
        console.warn('获取所有用户失败，尝试系统用户接口:', userRes)
        // 如果组织用户接口失败，尝试系统用户接口
        const systemUserRes = await getUserList({
          page: 1,
          pageSize: 999,
          username: '',
          nickName: '',
          phone: '',
          email: ''
        })

        if (systemUserRes.code === 0) {
          return (systemUserRes.data.list || []).map(user => ({
            key: user.ID,
            label: user.nickName || user.userName,
            disabled: false,
            userId: user.ID,
            userName: user.userName,
            nickName: user.nickName,
            orgId: null,
            orgName: '系统用户'
          }))
        }
        return []
      }

      const userList = Array.isArray(userRes.data) ? userRes.data : []
      // console.log('解析到的用户列表:', userList)

      return userList.map(user => ({
        key: user.ID || user.id,
        label: `${user.nickName || user.userName || `用户${user.ID}`}`,
        disabled: false,
        userId: user.ID || user.id,
        userName: user.userName,
        nickName: user.nickName,
        orgId: null,
        orgName: '系统用户'
      })).filter(member => member.userId)

    } catch (error) {
      console.error('获取所有用户失败:', error)
      return []
    }
  }

  // 获取用户列表
  const getUserListData = async () => {
    userLoading.value = true
    try {
      const members = await getMembersInScope()
      allUsers.value = members
      // console.log('可访问用户:', allUsers.value)
    } catch (error) {
      console.error('加载用户失败:', error)
      ElMessage.error('获取用户列表失败')
    } finally {
      userLoading.value = false
    }
  }

  // 加载组织树并构建组织ID到名称的映射
  const loadOrganizationTree = async () => {
    try {
      const treeRes = await getOrganizationalTree()
      if (treeRes.code === 0 && treeRes.data) {
        organizationTree.value = treeRes.data

        // 递归构建组织ID到名称的映射
        const buildOrgMap = (orgs) => {
          if (Array.isArray(orgs)) {
            orgs.forEach(org => {
              const id = org.ID || org.id
              const name = org.name || org.Name
              if (id && name) {
                organizationMap.value.set(id, name)
              }
              // 递归处理子组织
              const children = org.children || org.Children
              if (children && children.length > 0) {
                buildOrgMap(children)
              }
            })
          }
        }

        buildOrgMap(treeRes.data)
        // console.log('组织映射表:', organizationMap.value)
      }
    } catch (error) {
      console.error('加载组织树失败:', error)
    }
  }

  // 根据组织ID获取组织名称
  const getOrgNameById = (orgId) => {
    return organizationMap.value.get(orgId) || null
  }

  const toDetail = (row) => {
    selectID.value = row.id
  }

  const drawerFormVisible = ref(false)
  const type = ref('')

  const updateItem = async (row) => {
    type.value = 'update'
    formData.value = { ...row }
    drawerFormVisible.value = true
  }

  const closeDrawer = () => {
    drawerFormVisible.value = false
    formData.value = {
      categoryName: null,
      description: null,
      sortOrder: 0
    }
  }

  const deleteItem = async (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await deleteAssessmentCategories({ id: row.id })
        if (res.code === 0) {
          ElMessage.success('删除成功')
          getTableData()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
  }

  const drawerForm = ref(null)
  const enterDrawer = async () => {
    drawerForm.value.validate(async (valid) => {
      if (!valid) return

      try {
        let res
        if (type.value === 'create') {
          res = await createAssessmentCategories(formData.value)
        } else {
          res = await updateAssessmentCategories(formData.value)
        }

        if (res.code === 0) {
          ElMessage.success('操作成功')
          closeDrawer()
          getTableData()
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  const openDrawer = () => {
    type.value = 'create'
    drawerForm.value && drawerForm.value.clearValidate()
    drawerFormVisible.value = true
  }

  // 参数设置相关方法
  const handleSelectionChange = (selection) => {
    selectedParameters.value = selection
  }

  const getRoleTagType = (roleId) => {
    // 根据角色ID返回不同的标签类型
    const authority = authorityList.value.find(auth => auth.authorityId === roleId)
    if (!authority) return 'info'

    // 可以根据角色名称或ID设置不同颜色
    const typeMap = {
      888: 'danger',    // 管理员
      8881: 'warning',  // 项目经理
      9528: 'info'      // 普通用户
    }
    return typeMap[roleId] || 'primary'
  }

  const getRoleLabel = (roleId) => {
    const authority = authorityList.value.find(auth => auth.authorityId === roleId)
    return authority ? authority.authorityName : `角色ID: ${roleId}`
  }

  const openParameterDrawer = () => {
    parameterType.value = 'create'
    parameterFormData.value = {
      parameterName: '',
      roleId: null,
      parameterNameCn: ''
    }
    parameterDrawerVisible.value = true
  }

  const editParameter = (row) => {
    // 检查是否为系统参数
    if (isSystemParameter(row.parameterName)) {
      ElMessage.warning('系统参数不允许编辑')
      return
    }

    parameterType.value = 'update'
    parameterFormData.value = { ...row }
    parameterDrawerVisible.value = true
  }

  const closeParameterDrawer = () => {
    parameterDrawerVisible.value = false
    parameterFormData.value = {
      parameterName: '',
      roleId: null,
      parameterNameCn: ''
    }
  }

  const parameterForm = ref(null)
  const submitParameter = () => {
    parameterForm.value.validate(async (valid) => {
      if (!valid) return

      try {
        // 如果是编辑模式，先检查参数使用情况
        if (parameterType.value === 'update' && parameterFormData.value.id) {
          const usageCheck = await checkParameterUsage({ id: parameterFormData.value.id })
          if (usageCheck.code === 0 && usageCheck.data.hasUnarchived) {
            ElMessage.error(usageCheck.data.message)
            return
          }
        }

        let res
        if (parameterType.value === 'create') {
          res = await createCalculationParameters(parameterFormData.value)
        } else {
          res = await updateCalculationParameters(parameterFormData.value)
        }

        if (res.code === 0) {
          ElMessage.success('操作成功')
          closeParameterDrawer()
          getParameterListData() // 刷新参数列表
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  const deleteParameter = async (row) => {
    try {
      // 检查是否为系统参数
      if (isSystemParameter(row.parameterName)) {
        ElMessage.warning('系统参数不允许删除')
        return
      }

      // 先检查参数使用情况
      const usageCheck = await checkParameterUsage({ id: row.id })
      if (usageCheck.code === 0 && usageCheck.data.hasUnarchived) {
        ElMessage.error(usageCheck.data.message)
        return
      }

      ElMessageBox.confirm('确定要删除这个参数吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteCalculationParameters({ id: row.id })
          if (res.code === 0) {
            ElMessage.success('删除成功')
            getParameterListData() // 刷新参数列表
          } else {
            ElMessage.error(res.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      })
    } catch (error) {
      console.error('检查参数使用情况失败:', error)
      ElMessage.error('检查失败，无法删除')
    }
  }

  const batchDeleteParameters = async () => {
    try {
      // 过滤掉系统参数
      const systemParams = selectedParameters.value.filter(param => isSystemParameter(param.parameterName))
      const nonSystemParams = selectedParameters.value.filter(param => !isSystemParameter(param.parameterName))

      if (systemParams.length > 0) {
        const systemParamNames = systemParams.map(param => param.parameterNameCn || param.parameterName).join('、')
        ElMessage.warning(`以下系统参数不允许删除：${systemParamNames}`)

        if (nonSystemParams.length === 0) {
          return // 如果全部都是系统参数，直接返回
        }
      }

      // 检查非系统参数的使用情况
      const usageChecks = await Promise.all(
        nonSystemParams.map(param => checkParameterUsage({ id: param.id }))
      )

      const blockedParams = []
      usageChecks.forEach((check, index) => {
        if (check.code === 0 && check.data.hasUnarchived) {
          blockedParams.push({
            name: nonSystemParams[index].parameterNameCn || nonSystemParams[index].parameterName,
            configs: check.data.unarchivedConfigs.map(config => config.configName).join('、')
          })
        }
      })

      if (blockedParams.length > 0) {
        const blockedInfo = blockedParams.map(param =>
          `${param.name}（被考核配置：${param.configs} 使用）`
        ).join('\n')
        ElMessage.error(`以下参数存在未归档的考核配置，无法删除：\n${blockedInfo}`)
        return
      }

      // 计算可删除的参数数量（排除系统参数和被使用的参数）
      const deletableParams = nonSystemParams.filter(param =>
        !blockedParams.some(blocked => blocked.name === (param.parameterNameCn || param.parameterName))
      )

      if (deletableParams.length === 0) {
        ElMessage.warning('没有可删除的参数')
        return
      }

      ElMessageBox.confirm(`确定要删除选中的 ${deletableParams.length} 个参数吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const selectedIds = deletableParams.map(item => item.id)
          const res = await deleteCalculationParametersByIds({ ids: selectedIds })
          if (res.code === 0) {
            selectedParameters.value = []
            ElMessage.success('批量删除成功')
            getParameterListData() // 刷新参数列表
          } else {
            ElMessage.error(res.msg || '批量删除失败')
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          ElMessage.error('批量删除失败')
        }
      })
    } catch (error) {
      console.error('检查参数使用情况失败:', error)
      ElMessage.error('检查失败，无法删除')
    }
  }

  // 计算方法相关数据
  const methodData = ref([])
  const selectedMethods = ref([])
  const methodLoading = ref(false)

  // 可用参数列表
  const availableParameters = ref([])

  // 用户数据
  const allUsers = ref([])
  const userLoading = ref(false)

  // 组织相关数据
  const organizationTree = ref([])
  const organizationMap = ref(new Map())

  // 动态计算可用用户列表（排除已被其他计算方法分配的用户）
  const availableUsers = computed(() => {
    // 获取当前正在编辑的方法ID
    const currentMethodId = methodFormData.value.id

    // 获取所有其他计算方法已分配的用户ID
    const assignedUserIds = new Set()
    methodData.value.forEach(method => {
      // 排除当前正在编辑的方法
      if (method.id !== currentMethodId && method.assignedUsers) {
        method.assignedUsers.forEach(userId => {
          assignedUserIds.add(userId)
        })
      }
    })

    // 返回用户列表，已被分配的用户设为禁用
    return allUsers.value.map(user => ({
      ...user,
      disabled: assignedUserIds.has(user.key)
    }))
  })

  const methodDrawerVisible = ref(false)
  const methodType = ref('')
  const methodFormData = ref({
    methodName: '',
    description: '',
    selectedParameters: [],
    formula: '',
    assignedUsers: []
  })

  const methodRules = ref({
    methodName: [
      {
        required: true,
        message: '请输入计算方法名称',
        trigger: 'blur'
      }
    ],
    description: [
      {
        required: true,
        message: '请输入方法描述',
        trigger: 'blur'
      }
    ]
  })

  // 计算方法相关方法
  const handleMethodSelectionChange = (selection) => {
    selectedMethods.value = selection
  }

  // 更新可用参数列表
  const updateAvailableParameters = () => {
    availableParameters.value = parameterData.value
      .filter(param => methodFormData.value.selectedParameters.includes(param.id))
      .map(param => param.parameterName)
  }

  const openMethodDrawer = () => {
    methodType.value = 'create'
    methodFormData.value = {
      methodName: '',
      description: '',
      selectedParameters: [],
      formula: '',
      assignedUsers: []
    }
    availableParameters.value = []
    methodDrawerVisible.value = true
  }

  const editMethod = async (row) => {
    methodType.value = 'update'

    try {
      // 从后端获取完整的计算方法数据
      const res = await findCalculationMethods({ id: row.id })
      if (res.code === 0) {
        methodFormData.value = {
          ...res.data,
          selectedParameters: res.data.selectedParameters || [],
          formula: res.data.formula || '',
          assignedUsers: res.data.assignedUsers || []
        }
        updateAvailableParameters()
        methodDrawerVisible.value = true
      } else {
        ElMessage.error('获取计算方法详情失败')
      }
    } catch (error) {
      console.error('获取计算方法详情失败:', error)
      ElMessage.error('获取计算方法详情失败')
    }
  }

  const closeMethodDrawer = () => {
    methodDrawerVisible.value = false
    methodFormData.value = {
      methodName: '',
      description: '',
      selectedParameters: [],
      formula: '',
      assignedUsers: []
    }
    availableParameters.value = []
  }

  const methodForm = ref(null)
  const submitMethod = () => {
    methodForm.value.validate(async (valid) => {
      if (!valid) return

      try {
        let res
        if (methodType.value === 'create') {
          res = await createCalculationMethods(methodFormData.value)
        } else {
          res = await updateCalculationMethods(methodFormData.value)
        }

        if (res.code === 0) {
          ElMessage.success('操作成功')
          closeMethodDrawer()
          getMethodListData() // 刷新计算方法列表
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  const deleteMethod = (row) => {
    ElMessageBox.confirm('确定要删除这个计算方法吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res = await deleteCalculationMethods({ id: row.id })
        if (res.code === 0) {
          ElMessage.success('删除成功')
          getMethodListData() // 刷新计算方法列表
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
  }

  const batchDeleteMethods = () => {
    ElMessageBox.confirm(`确定要删除选中的 ${selectedMethods.value.length} 个计算方法吗?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const selectedIds = selectedMethods.value.map(item => item.id)
        const res = await deleteCalculationMethodsByIds({ ids: selectedIds })
        if (res.code === 0) {
          selectedMethods.value = []
          ElMessage.success('批量删除成功')
          getMethodListData() // 刷新计算方法列表
        } else {
          ElMessage.error(res.msg || '批量删除失败')
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败')
      }
    })
  }

  // 初始化数据
  const initData = async () => {
    await loadOrganizationTree()    // 加载组织树
    await getUserListData()         // 加载用户列表
    getParameterListData()          // 加载参数列表
    getMethodListData()             // 加载计算方法列表
  }

  initData()
</script>

<style lang="scss" scoped>
.active {
  background-color: var(--el-color-primary) !important;
  color: #fff;
}
</style>
