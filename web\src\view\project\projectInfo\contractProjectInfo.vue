
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
            <el-form-item label="项目名称" prop="name">
  <el-input v-model="searchInfo.name" placeholder="搜索条件" />
</el-form-item>
            
            <el-form-item label="所属部门" prop="departmentId">
  <el-select v-model="searchInfo.departmentId" placeholder="请选择部门" clearable style="width: 200px">
    <el-option
      v-for="org in organizationOptions"
      :key="org.value"
      :label="org.label"
      :value="org.value"
    />
  </el-select>
</el-form-item>
            
            <el-form-item label="项目负责人" prop="managerId">
  <el-input v-model="searchInfo.managerId" placeholder="搜索条件" />
</el-form-item>
            
            <!-- <el-form-item label="项目类型" prop="type">
  <el-input v-model="searchInfo.type" placeholder="搜索条件" disabled/>
</el-form-item> -->
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="项目名称" prop="name" width="120" />

            <el-table-column align="left" label="所属部门" prop="departmentName" width="150" />

            <el-table-column align="left" label="项目负责人" prop="managerName" width="120" />

            <el-table-column align="left" label="项目类型" prop="type" width="120" />

            <el-table-column label="项目成员" prop="memberNames" width="200">
    <template #default="scope">
        <div v-if="scope.row.memberNames && scope.row.memberNames.length > 0">
            <el-tag
                v-for="(member, index) in scope.row.memberNames"
                :key="index"
                size="small"
                style="margin: 2px;"
                type="info"
            >
                {{ member }}
            </el-tag>
        </div>
        <span v-else style="color: #999;">暂无成员</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="创建时间" prop="createdAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.createdAt) }}</template>
</el-table-column>
            <el-table-column align="left" label="更新时间" prop="updatedAt" width="180">
   <template #default="scope">{{ formatDate(scope.row.updatedAt) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateProjectInfoFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="项目名称:" prop="name">
    <el-input v-model="formData.name" :clearable="true" placeholder="请输入项目名称" />
</el-form-item>
            <el-form-item label="所属部门:" prop="departmentId">
    <el-select v-model="formData.departmentId" placeholder="请选择部门" clearable style="width: 100%">
      <el-option
        v-for="org in organizationOptions"
        :key="org.value"
        :label="org.label"
        :value="org.value"
      />
    </el-select>
</el-form-item>
            <el-form-item label="项目负责人:" prop="managerId">
    <el-select v-model="formData.managerId" placeholder="请选择项目负责人" clearable style="width: 100%">
      <el-option
        v-for="member in accessibleMembers"
        :key="member.userId"
        :label="member.label"
        :value="member.userName"
      />
    </el-select>
</el-form-item>
            <el-form-item label="项目类型:" prop="type">
    <el-input v-model="formData.type" :clearable="false" placeholder="项目类型" disabled />
</el-form-item>
            <el-form-item label="项目成员:" prop="members">
    <el-select
      v-model="selectedMembers"
      multiple
      placeholder="请选择项目成员"
      style="width: 100%"
      @change="handleMembersChange"
    >
      <el-option
        v-for="member in accessibleMembers"
        :key="member.userId"
        :label="member.label"
        :value="member.userId"
      />
    </el-select>
    <div v-if="selectedMembers.length > 0" style="margin-top: 8px;">
      <el-tag
        v-for="memberId in selectedMembers"
        :key="memberId"
        closable
        @close="removeMember(memberId)"
        style="margin-right: 8px; margin-bottom: 4px;"
      >
        {{ getMemberDisplayName(memberId) }}
      </el-tag>
    </div>
</el-form-item>
            <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="项目名称">
    {{ detailFrom.name }}
</el-descriptions-item>
                    <el-descriptions-item label="所属部门">
    {{ detailFrom.departmentName }}
</el-descriptions-item>
                    <el-descriptions-item label="项目负责人">
    {{ detailFrom.managerName }}
</el-descriptions-item>
                    <el-descriptions-item label="项目类型">
    {{ detailFrom.type }}
</el-descriptions-item>
                    <el-descriptions-item label="项目成员">
    <div v-if="detailFrom.memberNames && detailFrom.memberNames.length > 0">
        <el-tag
            v-for="(member, index) in detailFrom.memberNames"
            :key="index"
            size="small"
            style="margin: 2px;"
            type="info"
        >
            {{ member }}
        </el-tag>
    </div>
    <span v-else style="color: #999;">暂无成员</span>
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ detailFrom.createdAt }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ detailFrom.updatedAt }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createProjectInfo,
  deleteProjectInfo,
  deleteProjectInfoByIds,
  updateProjectInfo,
  findProjectInfo,
  getProjectInfoList
} from '@/api/project/projectInfo'

// 导入组织相关API
import {
  getUserLoginList,
  getOrganizationalMember,
  getOrganizationalTree,
  getUser
} from '@/plugin/organizational/api/organizational'



// 全量引入格式化工具 请按需保留
import { formatDate } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"
import { useUserStore } from '@/pinia/modules/user'

// 导入数据验证工具
import { validateProjectFormData, standardizeProjectMembers } from '@/utils/socer/dataValidation'




defineOptions({
    name: 'ContractProjectInfo'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()
const userStore = useUserStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 组织选项数据
const organizationOptions = ref([])

// 成员相关数据
const accessibleMembers = ref([])
const selectedMembers = ref([])

// 组织树数据，用于获取组织名称
const organizationTree = ref([])
const organizationMap = ref(new Map())

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            name: '',
            departmentId: undefined,
            managerId: '',
            type: '承揽项目',
            members: [], // 初始化为空数组而不是空对象
            createdAt: new Date(),
            updatedAt: new Date(),
        })



// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               departmentId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
              ],
               managerId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  searchInfo.value.type="承揽项目"
  const table = await getProjectInfoList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 加载用户有权限访问的组织选项
const loadOrganizationOptions = async () => {
  try {
    // 首先尝试获取用户可登录的节点列表（用户有权限的组织）
    const userLoginRes = await getUserLoginList()
    if (userLoginRes.code === 0 && userLoginRes.data && userLoginRes.data.length > 0) {
      // 如果用户有特定的登录节点权限，使用这些节点
      organizationOptions.value = userLoginRes.data.map(item => ({
        label: item.organizational?.name || `组织${item.org_id}`,
        value: item.org_id
      }))
      // console.log('加载用户权限组织选项:', organizationOptions.value)
    } else {
      // 如果没有特定权限或获取失败，则获取完整的组织树（可能需要根据实际权限控制）
      console.log('用户没有特定组织权限，加载完整组织树')
      const treeRes = await getOrganizationalTree()
      if (treeRes.code === 0 && treeRes.data) {
        const options = []
        // 递归遍历组织树，提取所有组织
        const traverseOrg = (orgs) => {
          if (Array.isArray(orgs)) {
            orgs.forEach(org => {
              const id = org.ID || org.id
              const name = org.name || org.Name
              if (id && name) {
                options.push({
                  label: name,
                  value: id
                })
              }
              // 递归处理子组织
              const children = org.children || org.Children
              if (children && children.length > 0) {
                traverseOrg(children)
              }
            })
          }
        }
        traverseOrg(treeRes.data)
        organizationOptions.value = options
        console.log('加载完整组织树选项:', organizationOptions.value)
      } else {
        console.error('获取组织树失败:', treeRes)
      }
    }
  } catch (error) {
    console.error('加载组织选项失败:', error)
    // 如果所有方法都失败，至少提供一个空选项
    organizationOptions.value = []
  }
}

// 加载组织树并构建组织ID到名称的映射
const loadOrganizationTree = async () => {
  try {
    const treeRes = await getOrganizationalTree()
    if (treeRes.code === 0 && treeRes.data) {
      organizationTree.value = treeRes.data

      // 递归构建组织ID到名称的映射
      const buildOrgMap = (orgs) => {
        if (Array.isArray(orgs)) {
          orgs.forEach(org => {
            const id = org.ID || org.id
            const name = org.name || org.Name
            if (id && name) {
              organizationMap.value.set(id, name)
            }
            // 递归处理子组织
            const children = org.children || org.Children
            if (children && children.length > 0) {
              buildOrgMap(children)
            }
          })
        }
      }

      buildOrgMap(treeRes.data)
      console.log('组织映射表:', organizationMap.value)
    }
  } catch (error) {
    console.error('加载组织树失败:', error)
  }
}

// 根据组织ID获取组织名称
const getOrgNameById = (orgId) => {
  return organizationMap.value.get(orgId) || null
}

// 修复后的方案：正确使用 getOrganizationalMember API
const getMembersInScope = async () => {
  try {
    console.log('开始获取权限范围内的成员...')

    // 获取用户的登录节点权限
    const userLoginRes = await getUserLoginList()
    console.log('用户登录权限响应:', userLoginRes)

    if (userLoginRes.code !== 0 || !userLoginRes.data) {
      console.warn('获取用户登录权限失败，尝试获取所有用户')
      return await getAllUsers()
    }

    // 去重组织ID
    const orgIds = [...new Set(userLoginRes.data.map(item => item.org_id))]
    console.log('用户有权限的组织ID列表:', orgIds)

    if (orgIds.length === 0) {
      console.warn('用户没有任何组织权限，获取所有用户')
      return await getAllUsers()
    }

    // 并发获取所有组织的成员，使用正确的参数名 ID
    const memberPromises = orgIds.map(orgId =>
      getOrganizationalMember({
        ID: orgId,  // 使用 ID 而不是 orgId
        page: 1,
        pageSize: 100  // 增加页面大小以获取更多成员
      }).catch(error => {
        console.error(`获取组织${orgId}成员失败:`, error)
        return { code: -1, data: { list: [] } }
      })
    )

    const memberResults = await Promise.all(memberPromises)
    console.log('所有组织成员API响应:', memberResults)

    // 合并所有成员并去重
    const allMembers = []
    const userIdSet = new Set()

    memberResults.forEach((result, index) => {
      console.log(`组织${orgIds[index]}的成员API响应:`, result)

      if (result.code === 0 && result.data && result.data.list) {
        const memberList = result.data.list
        console.log(`组织${orgIds[index]}的成员列表:`, memberList)

        memberList.forEach(member => {
          const userId = member.user_id || member.User?.ID
          const user = member.User || member.user

          if (userId && !userIdSet.has(userId)) {
            userIdSet.add(userId)

            // 获取组织名称，优先使用关联的组织信息
            let orgName = '未知组织'
            if (member.Organizational?.name) {
              orgName = member.Organizational.name
            } else if (member.organizational?.name) {
              orgName = member.organizational.name
            } else if (member.orgName) {
              orgName = member.orgName
            } else {
              // 如果没有组织名称，尝试从组织树中查找
              orgName = getOrgNameById(orgIds[index]) || `组织${orgIds[index]}`
            }

            // 构建用户显示名称
            const userName = user?.nickName || user?.userName || `用户${userId}`

            allMembers.push({
              userId: userId,
              userName: user?.userName,
              nickName: user?.nickName,
              orgId: member.org_id || orgIds[index],
              orgName: orgName,
              isAdmin: member.is_admin || false,
              label: `${userName} (${orgName})`
            })
          }
        })
      }
    })

    console.log('权限范围内的最终成员列表:', allMembers)

    // 如果权限范围内没有成员，回退到获取所有用户
    if (allMembers.length === 0) {
      console.log('权限范围内没有成员，回退到获取所有用户')
      return await getAllUsers()
    }

    return allMembers

  } catch (error) {
    console.error('获取权限范围内成员失败:', error)
    // 出错时回退到获取所有用户
    return await getAllUsers()
  }
}

// 回退方案：获取所有用户
const getAllUsers = async () => {
  try {
    console.log('获取所有用户列表...')
    const userRes = await getUser()
    console.log('所有用户API响应:', userRes)

    if (userRes.code !== 0 || !userRes.data) {
      console.warn('获取所有用户失败:', userRes)
      return []
    }

    const userList = Array.isArray(userRes.data) ? userRes.data : []
    console.log('解析到的用户列表:', userList)

    return userList.map(user => ({
      userId: user.ID || user.id,
      userName: user.userName,
      nickName: user.nickName,
      orgId: null,
      orgName: '系统用户',
      isAdmin: false,
      label: `${user.nickName || user.userName || `用户${user.ID}`}`
    })).filter(member => member.userId)

  } catch (error) {
    console.error('获取所有用户失败:', error)
    return []
  }
}

// 加载可访问的成员列表
const loadAccessibleMembers = async () => {
  try {
    const members = await getMembersInScope()
    accessibleMembers.value = members
    console.log('可访问成员:', accessibleMembers.value)
  } catch (error) {
    console.error('加载成员失败:', error)
  }
}

// 处理成员选择变化
const handleMembersChange = (selectedIds) => {
  // 后端期望的是用户名数组，不是复杂的对象结构
  const selectedUsernames = selectedIds.map(id => {
    const member = accessibleMembers.value.find(m => m.userId === id)
    return member?.userName || `user_${id}` // 使用用户名，如果没有则使用默认格式
  }).filter(username => username) // 过滤掉空值

  // 直接设置为用户名数组，这是后端期望的格式
  formData.value.members = selectedUsernames

  // 实时校验：如果项目负责人在成员列表中，给出提示
  if (formData.value.managerId && selectedUsernames.includes(formData.value.managerId)) {
    ElMessage({
      type: 'warning',
      message: '注意：项目负责人不能同时在项目成员中'
    })
  }

  console.log('处理后的成员数据:', formData.value.members)
}

// 数据格式验证和标准化函数
const validateAndStandardizeFormData = (data) => {
  const result = validateProjectFormData(data)

  if (result.error) {
    ElMessage.error(result.error)
    return null
  }

  console.log('数据验证通过，标准化后的数据:', result)
  return result
}

// 移除成员
const removeMember = (memberId) => {
  const index = selectedMembers.value.indexOf(memberId)
  if (index > -1) {
    selectedMembers.value.splice(index, 1)
    handleMembersChange(selectedMembers.value)
  }
}

// 获取成员显示名称
const getMemberDisplayName = (memberId) => {
  const member = accessibleMembers.value.find(m => m.userId === memberId)
  return member ? member.label : `用户${memberId}`
}

// 获取当前登录用户的组织ID
const getCurrentUserOrgId = () => {
  // 从用户Store中获取当前登录用户的组织ID
  return userStore.userInfo?.org_id || null
}

// 设置默认组织ID为当前用户的组织
const setDefaultOrganization = async () => {
  try {
    // 首先尝试从userStore获取
    let currentUserOrgId = getCurrentUserOrgId()

    // 如果userStore中没有org_id，尝试通过getUserLoginList获取
    if (!currentUserOrgId) {
      console.log('userStore中未找到org_id，尝试获取用户登录列表...')
      const userLoginRes = await getUserLoginList()
      if (userLoginRes.code === 0 && userLoginRes.data && userLoginRes.data.length > 0) {
        // 查找当前登录的组织
        const currentOrg = userLoginRes.data.find(item => item.org_id === userStore.userInfo?.org_id)
        if (currentOrg) {
          currentUserOrgId = currentOrg.org_id
        } else {
          // 如果没有找到当前登录组织，使用第一个有权限的组织
          currentUserOrgId = userLoginRes.data[0]?.org_id
        }
      }
    }

    // 设置默认的组织ID
    if (currentUserOrgId) {
      searchInfo.value.departmentId = currentUserOrgId
      console.log('设置默认组织ID为当前登录用户的组织:', currentUserOrgId)
    } else {
      console.log('未能获取到用户的组织ID，将显示所有组织的项目')
    }
  } catch (error) {
    console.error('设置默认组织失败:', error)
  }
}

// 初始化数据
const initData = async () => {
  await loadOrganizationOptions()
  await loadOrganizationTree()    // 加载组织树
  await loadAccessibleMembers()   // 加载成员列表
  await setDefaultOrganization()  // 设置默认组织
  getTableData()
}

initData()

// ============== 表格控制部分结束 ===============






// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteProjectInfoFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteProjectInfoByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateProjectInfoFunc = async(row) => {
    const res = await findProjectInfo({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data

        // 标准化从后端获取的成员数据
        formData.value.members = standardizeProjectMembers(formData.value.members)

        // 回显项目成员 - 现在members是用户名数组
        if (formData.value.members && Array.isArray(formData.value.members)) {
          // 根据用户名找到对应的用户ID
          selectedMembers.value = formData.value.members.map(username => {
            const member = accessibleMembers.value.find(m => m.userName === username)
            return member?.userId
          }).filter(id => id) // 过滤掉找不到的用户

          console.log('标准化后的成员用户名:', formData.value.members)
          console.log('回显的成员ID:', selectedMembers.value)
        } else {
          selectedMembers.value = []
        }

        dialogFormVisible.value = true
    }
}


// 删除行
const deleteProjectInfoFunc = async (row) => {
    const res = await deleteProjectInfo({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    selectedMembers.value = []  // 清空选中的成员
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    selectedMembers.value = []  // 清空选中的成员
    formData.value = {
        name: '',
        departmentId: undefined,
        managerId: '',
        type: '承揽项目',
        members: [], // 重置为空数组而不是空对象
        createdAt: new Date(),
        updatedAt: new Date(),
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false

             // 校验：项目负责人不能同时在项目成员中
             if (formData.value.managerId && formData.value.members && Array.isArray(formData.value.members)) {
               if (formData.value.members.includes(formData.value.managerId)) {
                 btnLoading.value = false
                 ElMessage({
                   type: 'warning',
                   message: '项目负责人不能同时在项目成员中，请重新选择'
                 })
                 return
               }
             }

             // 数据格式验证和标准化
             const validatedData = validateAndStandardizeFormData(formData.value)
             if (!validatedData) {
               btnLoading.value = false
               return
             }

             // 调试：打印提交前的数据
             console.log('提交前的表单数据:', validatedData)
             console.log('选中的成员:', selectedMembers.value)
             console.log('成员数据:', validatedData.members)

              let res
              switch (type.value) {
                case 'create':
                  res = await createProjectInfo(validatedData)
                  break
                case 'update':
                  res = await updateProjectInfo(validatedData)
                  break
                default:
                  res = await createProjectInfo(validatedData)
                  break
              }

              // 调试：打印API响应
              console.log('API响应:', res)

              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              } else {
                ElMessage({
                  type: 'error',
                  message: res.msg || '提交失败'
                })
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findProjectInfo({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>
