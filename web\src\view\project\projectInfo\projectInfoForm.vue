
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="项目名称:" prop="name">
    <el-input v-model="formData.name" :clearable="true" placeholder="请输入项目名称" />
</el-form-item>
        <el-form-item label="所属部门ID:" prop="departmentId">
    <el-input v-model.number="formData.departmentId" :clearable="true" placeholder="请输入所属部门ID" />
</el-form-item>
        <el-form-item label="项目负责人:" prop="managerId">
    <el-input v-model="formData.managerId" :clearable="true" placeholder="请输入项目负责人" />
</el-form-item>
        <el-form-item label="项目类型:" prop="type">
    <el-input v-model="formData.type" :clearable="true" placeholder="请输入项目类型" />
</el-form-item>
        <el-form-item label="项目成员:" prop="members">
    <!-- 调试信息 -->
    <div style="color: red; font-size: 12px; margin-bottom: 8px;">
      调试：可访问成员数量 {{ accessibleMembers.length }}，已选择 {{ selectedMembers.length }}
    </div>

    <el-select
      v-model="selectedMembers"
      multiple
      placeholder="请选择项目成员"
      style="width: 100%"
      @change="handleMembersChange"
    >
      <el-option
        v-for="member in accessibleMembers"
        :key="member.userId"
        :label="member.label"
        :value="member.userId"
      />
    </el-select>
    <div v-if="selectedMembers.length > 0" style="margin-top: 8px;">
      <el-tag
        v-for="memberId in selectedMembers"
        :key="memberId"
        closable
        @close="removeMember(memberId)"
        style="margin-right: 8px; margin-bottom: 4px;"
      >
        {{ getMemberDisplayName(memberId) }}
      </el-tag>
    </div>
</el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createProjectInfo,
  updateProjectInfo,
  findProjectInfo
} from '@/api/project/projectInfo'

// 导入组织相关API
import {
  getUserLoginList,
  getOrganizationalUserList
} from '@/plugin/organizational/api/organizational'

defineOptions({
    name: 'ProjectInfoForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            name: '',
            departmentId: undefined,
            managerId: '',
            type: '',
            members: {},
            createdAt: new Date(),
            updatedAt: new Date(),
        })

// 成员相关数据
const accessibleMembers = ref([])
const selectedMembers = ref([])
// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               departmentId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               managerId : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()

// 方案四：获取权限范围内的成员
const getMembersInScope = async () => {
  try {
    // 获取用户的登录节点权限
    const userLoginRes = await getUserLoginList()
    if (userLoginRes.code !== 0 || !userLoginRes.data) {
      return []
    }

    // 去重组织ID
    const orgIds = [...new Set(userLoginRes.data.map(item => item.org_id))]

    // 并发获取所有组织的成员
    const memberPromises = orgIds.map(orgId =>
      getOrganizationalUserList({ orgId }).catch(() => ({ data: [] }))
    )

    const memberResults = await Promise.all(memberPromises)

    // 合并所有成员并去重
    const allMembers = []
    const userIdSet = new Set()

    memberResults.forEach((result, index) => {
      if (result.code === 0 && result.data) {
        result.data.forEach(member => {
          if (!userIdSet.has(member.user_id)) {
            userIdSet.add(member.user_id)
            allMembers.push({
              userId: member.user_id,
              userName: member.user?.userName,
              nickName: member.user?.nickName,
              orgId: member.org_id,
              orgName: member.organizational?.name,
              isAdmin: member.is_admin,
              label: `${member.user?.nickName || member.user?.userName} (${member.organizational?.name})`
            })
          }
        })
      }
    })

    return allMembers
  } catch (error) {
    console.error('获取权限范围内成员失败:', error)
    return []
  }
}

// 加载可访问的成员列表
const loadAccessibleMembers = async () => {
  try {
    const members = await getMembersInScope()
    accessibleMembers.value = members
    console.log('可访问成员:', accessibleMembers.value)
  } catch (error) {
    console.error('加载成员失败:', error)
  }
}

// 处理成员选择变化
const handleMembersChange = (selectedIds) => {
  // 将选中的成员ID数组转换为JSON格式存储
  const selectedMemberData = selectedIds.map(id => {
    const member = accessibleMembers.value.find(m => m.userId === id)
    return {
      userId: id,
      userName: member?.userName,
      nickName: member?.nickName,
      orgName: member?.orgName
    }
  })

  formData.value.members = {
    memberIds: selectedIds,
    memberData: selectedMemberData
  }
}

// 移除成员
const removeMember = (memberId) => {
  const index = selectedMembers.value.indexOf(memberId)
  if (index > -1) {
    selectedMembers.value.splice(index, 1)
    handleMembersChange(selectedMembers.value)
  }
}

// 获取成员显示名称
const getMemberDisplayName = (memberId) => {
  const member = accessibleMembers.value.find(m => m.userId === memberId)
  return member ? member.label : `用户${memberId}`
}

// 初始化方法
const init = async () => {
  // 先加载可访问的成员列表
  await loadAccessibleMembers()

  // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
  if (route.query.id) {
    const res = await findProjectInfo({ ID: route.query.id })
    if (res.code === 0) {
      formData.value = res.data
      type.value = 'update'

      // 回显项目成员
      if (formData.value.members && formData.value.members.memberIds) {
        selectedMembers.value = formData.value.members.memberIds
      }
    }
  } else {
    type.value = 'create'
  }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createProjectInfo(formData.value)
               break
             case 'update':
               res = await updateProjectInfo(formData.value)
               break
             default:
               res = await createProjectInfo(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
