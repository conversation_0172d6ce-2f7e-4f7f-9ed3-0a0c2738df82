<template>
  <div class="assessment-table-container">
    <div class="table-wrapper" ref="tableWrapper">
      <table class="assessment-table">
        <!-- 表头 -->
        <thead class="table-header">
          <tr>
            <th class="project-header" style="position: sticky !important; left: 0px !important; top: 0px !important; z-index: 300 !important;">
              {{ headerLabel }}
            </th>
            <th
              v-for="(member, memberIndex) in members"
              :key="member.id"
              class="member-header"
              :data-col="memberIndex"
              @mouseenter="highlightColumn(memberIndex)"
              @mouseleave="clearHighlight"
            >
              <div class="member-info">
                <div class="member-name">{{ member.nickName || member.username }}</div>
                <div class="member-role">{{ member.authorityName }}</div>
              </div>
            </th>
          </tr>
        </thead>
        
        <!-- 表体 -->
        <tbody>
          <tr
            v-for="(project, projectIndex) in projects"
            :key="project.id"
            :data-row="projectIndex"
            @mouseenter="highlightRow(projectIndex)"
            @mouseleave="clearHighlight"
          >
            <td class="project-name" style="position: sticky !important; left: 0px !important; z-index: 50 !important;">
              <div class="project-info">
                <div class="project-title">{{ project.projectName }}</div>
                <div class="project-type">{{ project.projectType || '自研项目' }}</div>
              </div>
            </td>
            <td
              v-for="(member, memberIndex) in members"
              :key="`${project.id}-${member.id}`"
              class="score-cell"
              :data-row="projectIndex"
              :data-col="memberIndex"
              @mouseenter="highlightCell(projectIndex, memberIndex)"
              @mouseleave="clearHighlight"
            >
              <slot 
                name="cell" 
                :project="project" 
                :member="member" 
                :projectIndex="projectIndex" 
                :memberIndex="memberIndex"
                :score="getScore(project.id, member.id)"
              >
                {{ getScore(project.id, member.id) }}
              </slot>
            </td>
          </tr>
        </tbody>
        
        <!-- 合计行 -->
        <tfoot v-if="showTotal">
          <tr class="total-row">
            <td class="total-label" style="position: sticky !important; left: 0px !important; bottom: 0px !important; z-index: 200 !important;">
              {{ totalLabel }}
            </td>
            <td
              v-for="member in members"
              :key="`total-${member.id}`"
              class="total-value"
              style="position: sticky !important; bottom: 0px !important; z-index: 50 !important;"
            >
              <slot name="total" :member="member" :total="calculateMemberTotal(member.id)">
                {{ calculateMemberTotal(member.id) }}
              </slot>
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'

// Props 定义
const props = defineProps({
  // 表格数据
  projects: {
    type: Array,
    default: () => []
  },
  members: {
    type: Array,
    default: () => []
  },
  scores: {
    type: Array,
    default: () => []
  },
  // 表格配置
  headerLabel: {
    type: String,
    default: '项目 \\ 人员'
  },
  totalLabel: {
    type: String,
    default: '合计'
  },
  showTotal: {
    type: Boolean,
    default: true
  },
  // 高亮配置
  enableHighlight: {
    type: Boolean,
    default: true
  }
})

// Emits 定义
const emit = defineEmits(['cell-click', 'cell-change'])

// 响应式数据
const tableWrapper = ref(null)
const highlightedRow = ref(-1)
const highlightedCol = ref(-1)

// 获取分数的方法
const getScore = (projectId, memberId) => {
  const scoreRecord = props.scores.find(s => s.projectId === projectId && s.memberId === memberId)
  return scoreRecord ? scoreRecord.score : 0
}

// 计算成员总分
const calculateMemberTotal = (memberId) => {
  return props.scores
    .filter(score => score.memberId === memberId)
    .reduce((total, score) => total + (score.score || 0), 0)
}

// 高亮功能
const highlightRow = (rowIndex) => {
  if (!props.enableHighlight) return
  highlightedRow.value = rowIndex
  updateHighlight()
}

const highlightColumn = (colIndex) => {
  if (!props.enableHighlight) return
  highlightedCol.value = colIndex
  updateHighlight()
}

const highlightCell = (rowIndex, colIndex) => {
  if (!props.enableHighlight) return
  highlightedRow.value = rowIndex
  highlightedCol.value = colIndex
  updateHighlight()
}

const clearHighlight = () => {
  if (!props.enableHighlight) return
  highlightedRow.value = -1
  highlightedCol.value = -1
  updateHighlight()
}

const updateHighlight = () => {
  if (!tableWrapper.value) return
  
  // 清除所有高亮
  const allCells = tableWrapper.value.querySelectorAll('td, th')
  allCells.forEach(cell => {
    cell.classList.remove('row-highlight', 'col-highlight', 'cell-highlight')
  })
  
  if (highlightedRow.value >= 0) {
    const rowCells = tableWrapper.value.querySelectorAll(`[data-row="${highlightedRow.value}"]`)
    rowCells.forEach(cell => cell.classList.add('row-highlight'))
  }
  
  if (highlightedCol.value >= 0) {
    const colCells = tableWrapper.value.querySelectorAll(`[data-col="${highlightedCol.value}"]`)
    colCells.forEach(cell => cell.classList.add('col-highlight'))
  }
  
  if (highlightedRow.value >= 0 && highlightedCol.value >= 0) {
    const targetCell = tableWrapper.value.querySelector(`[data-row="${highlightedRow.value}"][data-col="${highlightedCol.value}"]`)
    if (targetCell) {
      targetCell.classList.add('cell-highlight')
    }
  }
}

// 应用 sticky 定位
const applySticky = () => {
  if (!tableWrapper.value) return
  
  // 表头容器
  const thead = tableWrapper.value.querySelector('thead')
  if (thead) {
    thead.style.setProperty('position', 'sticky', 'important')
    thead.style.setProperty('top', '0px', 'important')
    thead.style.setProperty('z-index', '250', 'important')
  }

  // 左侧固定列
  const projectCells = tableWrapper.value.querySelectorAll('.project-name, .project-header')
  projectCells.forEach(cell => {
    cell.style.setProperty('position', 'sticky', 'important')
    cell.style.setProperty('left', '0px', 'important')
    if (cell.classList.contains('project-header')) {
      cell.style.setProperty('top', '0px', 'important')
      cell.style.setProperty('z-index', '300', 'important')
    } else {
      cell.style.setProperty('z-index', '50', 'important')
    }
  })

  // 底部固定合计行
  if (props.showTotal) {
    const totalCells = tableWrapper.value.querySelectorAll('.total-row td')
    totalCells.forEach(cell => {
      cell.style.setProperty('position', 'sticky', 'important')
      cell.style.setProperty('bottom', '0px', 'important')
      if (cell.classList.contains('total-label')) {
        cell.style.setProperty('left', '0px', 'important')
        cell.style.setProperty('z-index', '200', 'important')
      } else {
        cell.style.setProperty('z-index', '50', 'important')
      }
    })

    const tfoot = tableWrapper.value.querySelector('tfoot')
    if (tfoot) {
      tfoot.style.setProperty('position', 'sticky', 'important')
      tfoot.style.setProperty('bottom', '0px', 'important')
      tfoot.style.setProperty('z-index', '50', 'important')
    }
  }
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    applySticky()
  })
})

// 监听数据变化，重新应用样式
watch([() => props.projects, () => props.members], () => {
  nextTick(() => {
    applySticky()
  })
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  applySticky,
  getScore,
  calculateMemberTotal
})
</script>

<style scoped>
/* 表格容器 */
.assessment-table-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.table-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
}

/* 表格基础样式 */
.assessment-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 14px;
  background: #fff;
  table-layout: fixed;
}

/* 表头样式 */
.table-header {
  position: -webkit-sticky !important;
  position: sticky !important;
  top: 0px !important;
  z-index: 250 !important;
  background: #f5f7fa;
}

.project-header {
  background: #f5f7fa !important;
  color: #303133;
  font-weight: 600;
  padding: 12px 8px;
  min-width: 200px;
  width: 200px;
  position: -webkit-sticky !important;
  position: sticky !important;
  left: 0px !important;
  top: 0px !important;
  z-index: 300 !important;
  border-right: 2px solid #dcdfe6;
  transition: all 0.2s ease;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.member-header {
  background: #f5f7fa !important;
  color: #303133;
  font-weight: 600;
  padding: 8px;
  min-width: 85px;
  width: 85px;
  text-align: center;
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  position: -webkit-sticky !important;
  position: sticky !important;
  top: 0px !important;
  z-index: 100 !important;
  transition: all 0.2s ease;
}

.member-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.member-name {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
}

.member-role {
  font-size: 11px;
  color: #909399;
  font-weight: normal;
}

/* 项目名称列 */
.project-name {
  background: #fafafa !important;
  padding: 8px;
  min-width: 200px;
  width: 200px;
  border-right: 2px solid #dcdfe6;
  border-bottom: 1px solid #e4e7ed;
  position: -webkit-sticky !important;
  position: sticky !important;
  left: 0px !important;
  z-index: 50 !important;
  transition: all 0.2s ease;
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.project-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.project-type {
  font-size: 11px;
  color: #909399;
  font-weight: normal;
}

/* 分数单元格 */
.score-cell {
  padding: 8px;
  text-align: center;
  min-width: 85px;
  width: 85px;
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  transition: all 0.2s ease;
  cursor: pointer;
}

.score-cell:hover {
  background: #f0f9ff !important;
}

/* 合计行样式 */
.assessment-table tfoot {
  background: #f5f7fa;
  font-weight: 600;
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
  z-index: 50 !important;
}

.total-row {
  background: #f5f7fa !important;
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
  z-index: 50 !important;
}

.total-label {
  background: #f5f7fa !important;
  color: #409eff !important;
  font-weight: 600;
  text-align: center;
  padding: 12px 8px;
  min-width: 200px;
  width: 200px;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.total-value {
  background: #f5f7fa !important;
  color: #409eff !important;
  font-weight: 600;
  text-align: center;
  padding: 12px 8px;
  min-width: 85px;
  width: 85px;
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
  z-index: 50 !important;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

/* 高亮效果 */
.row-highlight {
  background: rgba(64, 158, 255, 0.1) !important;
}

.col-highlight {
  background: rgba(64, 158, 255, 0.1) !important;
}

.cell-highlight {
  background: rgba(64, 158, 255, 0.2) !important;
  box-shadow: inset 0 0 0 2px #409eff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assessment-table {
    font-size: 12px;
  }

  .project-header,
  .member-header {
    min-width: 100px;
    padding: 8px 4px;
  }

  .project-name {
    min-width: 150px;
    width: 150px;
    padding: 8px 4px;
    font-size: 12px;
  }

  .score-cell {
    min-width: 70px;
    width: 70px;
    padding: 6px 4px;
    font-size: 12px;
  }

  .total-label {
    min-width: 150px;
    width: 150px;
    padding: 8px 4px;
    font-size: 12px;
  }

  .total-value {
    min-width: 70px;
    width: 70px;
    padding: 8px 4px;
    font-size: 12px;
  }

  .member-name {
    font-size: 11px;
  }

  .member-role {
    font-size: 10px;
  }

  .project-title {
    font-size: 11px;
  }

  .project-type {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .project-header,
  .project-name {
    min-width: 120px;
    width: 120px;
  }

  .member-header,
  .score-cell,
  .total-value {
    min-width: 60px;
    width: 60px;
  }

  .total-label {
    min-width: 120px;
    width: 120px;
  }
}
</style>
