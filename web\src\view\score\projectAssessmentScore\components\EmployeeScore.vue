<template>
  <div class="employee-score">
    <!-- 操作按钮区域 -->
    <div class="mb-1 flex justify-center items-center">
      <!-- 配额信息显示 -->
      <div class="quota-info mr-4">
        <span class="quota-text">当前高分配额(>=95分)剩余数量：</span>
        <span class="quota-number">{{ quotaRemaining }}</span>
      </div>
      
      <!-- 按钮组 -->
      <div class="flex gap-2">
        <el-button type="primary" @click="handleSubmit">提交评分</el-button>
        <el-button @click="handleExportExcel">导出Excel</el-button>
      </div>
      
      <!-- 奖金信息显示 -->
      <div class="bonus-info ml-4">
        <span class="bonus-text">剩余奖金额度：</span>
        <span class="bonus-number">¥{{ bonusRemaining.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 员工评分表格 -->
    <div class="employee-score-table-container">
      <el-table
        :data="tableData"
        stripe
        border
        style="width: 100%"
        :height="tableHeight"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        :row-class-name="getRowClassName"
        fit
      >
        <el-table-column
          prop="memberName"
          label="成员姓名"
          min-width="120"
          fixed="left"
          align="center"
        >
          <template #default="scope">
            <span>{{ scope.row.memberName }}</span>
            <span v-if="scope.row._modified" class="modified-indicator" title="已修改"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="departmentManagerScore"
          label="部门/机构负责人评分"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            <div
              v-if="scope.row.canEdit"
              class="score-cell editable-cell"
              :contenteditable="true"
              @blur="updateEmployeeScore(scope.row, 'departmentManagerScore', $event)"
              @keydown="handleKeydown($event)"
              @input="validateInput($event)"
              @mouseenter="highlightCell"
              @mouseleave="clearHighlight"
            >
              {{ formatEmployeeScoreDisplay(scope.row.departmentManagerScore) }}
            </div>
            <div v-else class="score-cell disabled-cell">
              /
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="bonusAmount"
          label="奖金"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            <div
              class="score-cell editable-cell"
              :contenteditable="true"
              @blur="updateEmployeeBonus(scope.row, $event)"
              @keydown="handleKeydown($event)"
              @input="validateInput($event)"
              @mouseenter="highlightCell"
              @mouseleave="clearHighlight"
            >
              {{ formatBonusDisplay(scope.row.bonusAmount) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="200"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                type="success"
                size="small"
                @click="handleViewDetails(scope.row)"
              >
                查看详情
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleResetScore(scope.row)"
                :disabled="!scope.row._modified"
              >
                重置
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  tabName: {
    type: String,
    required: true
  },
  employeeData: {
    type: Array,
    default: () => []
  },
  quotaRemaining: {
    type: Number,
    default: 0
  },
  bonusRemaining: {
    type: Number,
    default: 0
  },
  tableHeight: {
    type: [String, Number],
    default: 400
  }
})

// Emits
const emit = defineEmits([
  'submit',
  'export-excel',
  'update-employee-score',
  'update-employee-bonus',
  'view-details',
  'reset-score'
])

// Computed
const tableData = computed(() => props.employeeData)

// Methods
const handleSubmit = () => {
  emit('submit', props.tabName)
}

const handleExportExcel = () => {
  emit('export-excel', props.tabName)
}

const updateEmployeeScore = (row, field, event) => {
  emit('update-employee-score', row, field, event)
}

const updateEmployeeBonus = (row, event) => {
  emit('update-employee-bonus', row, event)
}

const handleViewDetails = (row) => {
  emit('view-details', row)
}

const handleResetScore = (row) => {
  emit('reset-score', row)
}

const getRowClassName = ({ row }) => {
  if (row._modified) {
    return 'modified-row'
  }
  if (!row.canEdit) {
    return 'disabled-row'
  }
  return ''
}

const formatEmployeeScoreDisplay = (value) => {
  if (value === 0 || value === '0' || value === null || value === undefined || value === '') {
    return ''
  }
  return value
}

const formatBonusDisplay = (value) => {
  if (value === 0 || value === '0' || value === null || value === undefined || value === '') {
    return ''
  }
  return Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const handleKeydown = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    event.target.blur()
  }
}

const validateInput = (event) => {
  const value = parseFloat(event.target.textContent) || 0
  if (value > 100) {
    event.target.textContent = '100'
  } else if (value < 0) {
    event.target.textContent = ''
  }
}

const highlightCell = (event) => {
  event.target.classList.add('cell-highlight')
}

const clearHighlight = (event) => {
  event.target.classList.remove('cell-highlight')
}
</script>

<style scoped>
.employee-score {
  width: 100%;
}

.employee-score-table-container {
  width: 100%;
}

/* 配额信息样式 */
.quota-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 14px;
}

.quota-text {
  color: #1e40af;
  font-weight: 500;
}

.quota-number {
  color: #dc2626;
  font-weight: bold;
  font-size: 16px;
}

/* 奖金信息样式 */
.bonus-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  font-size: 14px;
}

.bonus-text {
  color: #166534;
  font-weight: 500;
}

.bonus-number {
  color: #059669;
  font-weight: bold;
  font-size: 16px;
}

.score-cell {
  min-height: 32px;
  line-height: 32px;
  padding: 4px 8px;
  text-align: center;
  font-weight: 500;
  transition: all 0.2s ease;
}

.score-cell.editable-cell {
  cursor: text;
  background-color: transparent;
  border: none;
  color: #606266;
}

.score-cell.editable-cell:hover {
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 4px;
}

.score-cell.editable-cell:focus {
  outline: none;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px #409eff;
}

.score-cell.disabled-cell {
  background-color: transparent;
  color: #c0c4cc;
  cursor: not-allowed;
  border: none;
}

.score-cell.cell-highlight {
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
}

/* 已修改行的标识 */
:deep(.el-table__row.modified-row) {
  background-color: rgba(64, 158, 255, 0.05);
}

:deep(.el-table__row.modified-row:hover) {
  background-color: rgba(64, 158, 255, 0.1);
}

/* 禁用行样式 */
:deep(.el-table__row.disabled-row) {
  background-color: rgba(192, 196, 204, 0.05);
  color: #c0c4cc;
}

:deep(.el-table__row.disabled-row:hover) {
  background-color: rgba(192, 196, 204, 0.1);
}

/* 修改标记 */
.modified-indicator {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #f56c6c;
  border-radius: 50%;
  margin-left: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 禁用按钮样式 */
.operation-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
