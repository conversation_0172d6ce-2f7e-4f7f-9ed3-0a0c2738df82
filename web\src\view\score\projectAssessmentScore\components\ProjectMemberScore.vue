<template>
  <div class="project-member-score">
    <!-- 操作按钮区域 -->
    <div class="mb-1 flex justify-center items-center">
      <!-- 高分限制信息 - 左侧 -->
      <div class="high-score-limit-info mr-4">
        <span class="limit-text">高分限制信息：</span>
        <span class="limit-details">{{ highScoreLimitSummary }}</span>
      </div>

      <!-- 按钮组 -->
      <div class="flex gap-2">
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button type="success" @click="handleExportExcel">导出Excel</el-button>
      </div>

      <!-- 右侧占位，保持视觉平衡 -->
      <div class="ml-4" style="width: 300px;"></div>
    </div>

    <!-- 项目成员评分表格 -->
    <div class="project-member-table-container">
      <el-table
        :data="tableData"
        stripe
        border
        style="width: 100%"
        :height="tableHeight"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        fit
      >
        <el-table-column
          prop="memberName"
          label="成员姓名"
          min-width="120"
          fixed="left"
          align="center"
        />
        <el-table-column
          v-for="project in managerProjects"
          :key="project.projectId"
          :prop="`project_${project.projectId}`"
          :label="project.projectName"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            <div
              v-if="isEditable(project.projectId, scope.row.userName)"
              class="score-cell editable-cell"
              :contenteditable="true"
              @blur="updateScore(project.projectId, scope.row.userName, $event)"
              @keydown="handleKeydown($event)"
              @input="validateInput($event)"
              @mouseenter="highlightCell"
              @mouseleave="clearHighlight"
            >
              {{ formatScoreDisplay(scope.row[`project_${project.projectId}`]) }}
            </div>
            <div v-else class="score-cell disabled-cell">
              /
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  tabName: {
    type: String,
    required: true
  },
  members: {
    type: Array,
    default: () => []
  },
  managerProjects: {
    type: Array,
    default: () => []
  },
  projectMemberScores: {
    type: Object,
    default: () => ({})
  },
  highScoreLimitSummary: {
    type: String,
    default: ''
  },
  tableHeight: {
    type: [String, Number],
    default: 400
  },
  isProjectMemberEditable: {
    type: Function,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'submit',
  'export-excel',
  'update-score'
])

// Computed
const tableData = computed(() => {
  const data = []
  props.members.forEach(member => {
    const row = {
      userName: member.userName || member.name,
      memberName: member.name
    }
    
    props.managerProjects.forEach(project => {
      const key = `${project.projectId}-${member.userName || member.name}`
      row[`project_${project.projectId}`] = props.projectMemberScores[key] || 0
    })
    
    data.push(row)
  })
  return data
})

// Methods
const handleSubmit = () => {
  emit('submit', props.tabName)
}

const handleExportExcel = () => {
  emit('export-excel', props.tabName)
}

const updateScore = (projectId, userName, event) => {
  emit('update-score', props.tabName, projectId, userName, event)
}

const isEditable = (projectId, userName) => {
  return props.isProjectMemberEditable(props.tabName, projectId, userName)
}

const formatScoreDisplay = (value) => {
  if (value === 0 || value === '0' || value === null || value === undefined || value === '') {
    return ''
  }
  return value
}

const handleKeydown = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    event.target.blur()
  }
}

const validateInput = (event) => {
  const value = parseFloat(event.target.textContent) || 0
  if (value > 100) {
    event.target.textContent = '100'
  } else if (value < 0) {
    event.target.textContent = ''
  }
}

const highlightCell = (event) => {
  event.target.classList.add('cell-highlight')
}

const clearHighlight = (event) => {
  event.target.classList.remove('cell-highlight')
}
</script>

<style scoped>
.project-member-score {
  width: 100%;
}

.project-member-table-container {
  width: 100%;
}

.high-score-limit-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #fff7ed;
  border: 1px solid #fed7aa;
  border-radius: 6px;
  font-size: 14px;
  max-width: 300px;
}

.limit-text {
  color: #9a3412;
  font-weight: 500;
  white-space: nowrap;
}

.limit-details {
  color: #d97706;
  font-weight: bold;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.score-cell {
  min-height: 32px;
  line-height: 32px;
  padding: 4px 8px;
  text-align: center;
  font-weight: 500;
  transition: all 0.2s ease;
}

.score-cell.editable-cell {
  cursor: text;
  background-color: transparent;
  border: none;
  color: #606266;
}

.score-cell.editable-cell:hover {
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 4px;
}

.score-cell.editable-cell:focus {
  outline: none;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px #409eff;
}

.score-cell.disabled-cell {
  background-color: transparent;
  color: #c0c4cc;
  cursor: not-allowed;
  border: none;
}

.score-cell.cell-highlight {
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
}
</style>
