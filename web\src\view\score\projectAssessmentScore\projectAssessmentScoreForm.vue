
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="被评价用户名:" prop="username">
    <el-input v-model="formData.username" :clearable="true" placeholder="请输入被评价用户名" />
</el-form-item>
        <el-form-item label="项目ID，关联project_info.id:" prop="projectId">
    <el-input v-model.number="formData.projectId" :clearable="true" placeholder="请输入项目ID，关联project_info.id" />
</el-form-item>
        <el-form-item label="考核系数:" prop="assessmentCoefficient">
    <el-input-number v-model="formData.assessmentCoefficient" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="项目负责人评分:" prop="managerScore">
    <el-input-number v-model="formData.managerScore" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="评分人用户名:" prop="scorerUsername">
    <el-input v-model="formData.scorerUsername" :clearable="true" placeholder="请输入评分人用户名" />
</el-form-item>
        <el-form-item label="计算参数（英文字母，如A、B、C等）:" prop="calculationParameter">
    <el-input v-model="formData.calculationParameter" :clearable="true" placeholder="请输入计算参数（英文字母，如A、B、C等）" />
</el-form-item>
        <el-form-item label="创建时间:" prop="createdAt">
    <el-date-picker v-model="formData.createdAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="更新时间:" prop="updatedAt">
    <el-date-picker v-model="formData.updatedAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createProjectAssessmentScore,
  updateProjectAssessmentScore,
  findProjectAssessmentScore
} from '@/api/score/projectAssessmentScore'

defineOptions({
    name: 'ProjectAssessmentScoreForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            username: '',
            projectId: undefined,
            assessmentCoefficient: 0,
            managerScore: 0,
            scorerUsername: '',
            calculationParameter: '',
            createdAt: new Date(),
            updatedAt: new Date(),
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findProjectAssessmentScore({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createProjectAssessmentScore(formData.value)
               break
             case 'update':
               res = await updateProjectAssessmentScore(formData.value)
               break
             default:
               res = await createProjectAssessmentScore(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
