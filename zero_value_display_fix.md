# 表格零值显示优化

## 需求说明

用户希望当表格中的数值为0时，不显示"0"，而是显示为空的单元格，提供更清洁的视觉效果。

## 修改内容

### 1. 新增格式化函数

创建 `formatScoreDisplay` 函数来处理分数显示：

```javascript
// 格式化分数显示：0值显示为空，非0值正常显示
const formatScoreDisplay = (score) => {
  const numericScore = parseFloat(score) || 0
  return numericScore === 0 ? '' : numericScore
}
```

**逻辑说明**：
- 输入值为0或空值时，返回空字符串 `''`
- 输入值为非0数值时，返回原数值
- 自动处理字符串转数值的转换

### 2. 修改表格单元格显示

**修改前**：
```html
<td>
  {{ getCurrentTabData(item.name).scores[`${project.id}-${member.id}`] || 0 }}
</td>
```

**修改后**：
```html
<td>
  {{ formatScoreDisplay(getCurrentTabData(item.name).scores[`${project.id}-${member.id}`]) }}
</td>
```

### 3. 修改合计行显示

**修改前**：
```html
<td>
  {{ calculateTabMemberTotal(item.name, member.id) }}
</td>
```

**修改后**：
```html
<td>
  {{ formatScoreDisplay(calculateTabMemberTotal(item.name, member.id)) }}
</td>
```

### 4. 优化用户输入体验

修改 `updateTabScore` 函数，确保用户输入0值时界面显示为空：

```javascript
// 限制分数范围
if (value > 100) {
  event.target.textContent = '100'
  tabsData[tabName].scores[key] = 100
} else if (value < 0) {
  event.target.textContent = ''  // 0值显示为空
  tabsData[tabName].scores[key] = 0
} else if (value === 0) {
  event.target.textContent = ''  // 0值显示为空
  tabsData[tabName].scores[key] = 0
}
```

## 显示效果对比

### 修改前
```
| 成员 | 项目A | 项目B | 项目C | 合计 |
|------|-------|-------|-------|------|
| 张三 |   50  |   0   |   0   |  50  |
| 李四 |   0   |  30   |   0   |  30  |
| 王五 |   0   |   0   |   0   |   0  |
```

### 修改后
```
| 成员 | 项目A | 项目B | 项目C | 合计 |
|------|-------|-------|-------|------|
| 张三 |   50  |       |       |  50  |
| 李四 |       |  30   |       |  30  |
| 王五 |       |       |       |      |
```

## 数据处理逻辑

### 1. 显示层面
- 0值在界面上显示为空单元格
- 非0值正常显示数值
- 提供更清洁的视觉效果

### 2. 数据层面
- 内部数据结构仍然存储0值
- 提交到后端的数据保持完整性
- 计算逻辑不受影响

### 3. 用户交互
- 用户可以在空单元格中直接输入数值
- 输入0或删除内容时，单元格变为空显示
- 输入验证和范围限制功能保持不变

## 测试场景

### 1. 初始加载测试
1. 刷新页面
2. 验证已有的0值数据显示为空单元格
3. 验证非0值数据正常显示

### 2. 用户输入测试
1. 在空单元格中输入数值
2. 验证数值正常显示和保存
3. 输入0或删除内容
4. 验证单元格变为空显示

### 3. 合计行测试
1. 验证合计为0时显示为空
2. 验证合计非0时正常显示数值
3. 修改单元格数值，验证合计自动更新

### 4. 数据提交测试
1. 填写部分数据（包含0值）
2. 提交数据到后端
3. 验证后端接收到完整的数据（包括0值）

## 预期效果

修改后，用户将看到：
- ✅ 更清洁的表格视觉效果
- ✅ 0值单元格显示为空，减少视觉干扰
- ✅ 非0值正常显示，保持数据可读性
- ✅ 用户交互体验保持一致
- ✅ 数据完整性和计算逻辑不受影响

这个优化提升了表格的视觉体验，让用户更容易关注到有实际数值的单元格，同时保持了数据的完整性和功能的正常运行。
